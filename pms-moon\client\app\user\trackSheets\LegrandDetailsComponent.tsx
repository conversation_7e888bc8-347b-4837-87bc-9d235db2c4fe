import React, { useState, useCallback, useMemo, useEffect } from "react";
import { UseFormReturn } from "react-hook-form";
import SearchSelect from "@/app/_component/SearchSelect";
import { MapPin } from "lucide-react";
import { legrandMapping_routes } from "@/lib/routePath";
import { getAllData } from "@/lib/helpers";
import toast from "react-hot-toast";

interface LegrandMappingData {
  id: string;
  businessUnit: string | null;
  legalName: string | null;
  customeCode: string | null;
  shippingBillingName: string | null;
  shippingBillingAddress: string | null;
  location: string | null;
  zipPostal: string | null;
  aliasCity: string | null;
  aliasShippingNames: string | null;
  corporationId: number | null;
  createdAt: string;
  updatedAt: string;
}

interface LegrandDetailsComponentProps {
  form: UseFormReturn<any>;
  entryIndex: number;
  onLegrandDataChange?: (entryIndex: number, businessUnit: string, divisionCode: string) => void;
  blockTitle?: string;
  fieldPrefix?: string;
}

const LegrandDetailsComponent: React.FC<LegrandDetailsComponentProps> = ({
  form,
  entryIndex,
  onLegrandDataChange,
  blockTitle = "LEGRAND Details",
  fieldPrefix = "legrand"
}) => {
  const [legrandData, setLegrandData] = useState<LegrandMappingData[]>([]);
  const [loading, setLoading] = useState(true);

  const fetchLegrandData = useCallback(async () => {
    try {
      setLoading(true);
      const response = await getAllData(legrandMapping_routes.GET_LEGRAND_MAPPINGS);
      setLegrandData(response || []);
    } catch (error) {
      toast.error("Error fetching LEGRAND mapping data:", error);
      setLegrandData([]);
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchLegrandData();
  }, [fetchLegrandData]);

  const extractCityFromAddress = (address: string): string => {
    if (!address) return '';

    const parts = address.split(',');
    if (parts.length >= 2) {
      const cityPart = parts[parts.length - 2].trim();
      const city = cityPart.replace(/\d+/g, '').trim();
      return city;
    }

    const words = address.split(' ');
    for (const word of words) {
      if (word.length > 2 && !/^\d+$/.test(word) && !word.includes('ROAD') && !word.includes('STREET') && !word.includes('AVE')) {
        return word;
      }
    }

    return '';
  };

  const getAliasOptions = useMemo(() => {
    if (!legrandData.length) return [];

    const options: Array<{
      value: string;
      label: string;
      type: 'alias' | 'company';
      displayName: string;
    }> = [];

    // Pre-compute base names to avoid repeated calculations
    const baseNameMap = new Map<string, string>();
    const nameCountMap = new Map<string, number>();

    // First pass: compute base names and count occurrences
    legrandData.forEach(data => {
      const legalName = data.legalName || 'Unknown Company';
      const aliasName = data.aliasShippingNames && data.aliasShippingNames !== 'NONE' ? data.aliasShippingNames : null;
      const baseName = aliasName ? `${legalName} (${aliasName})` : legalName;

      baseNameMap.set(data.id, baseName);
      nameCountMap.set(baseName, (nameCountMap.get(baseName) || 0) + 1);
    });

    // Second pass: build options with optimized duplicate checking
    legrandData.forEach(data => {
      const location = data.location || extractCityFromAddress(data.shippingBillingAddress || '');
      const zipcode = data.zipPostal || '';
      const legalName = data.legalName || 'Unknown Company';
      const aliasName = data.aliasShippingNames && data.aliasShippingNames !== 'NONE' ? data.aliasShippingNames : null;

      let displayName = baseNameMap.get(data.id) || legalName;
      const type: 'alias' | 'company' = aliasName ? 'alias' : 'company';

      // Only add location/zipcode suffix if there are duplicates
      const nameCount = nameCountMap.get(displayName) || 1;
      if (nameCount > 1) {
        if (location) {
          displayName = `${displayName}_${location}`;
        } else if (zipcode) {
          displayName = `${displayName}_${zipcode}`;
        }
      }

      const uniqueKey = `${data.customeCode}-${data.aliasShippingNames || data.legalName}-${data.shippingBillingAddress}`;

      options.push({
        value: uniqueKey,
        label: displayName,
        type: type,
        displayName: displayName
      });
    });

    // Remove duplicates and sort
    const uniqueOptions = options.filter((option, index, self) =>
      index === self.findIndex(o => o.value === option.value)
    );

    return uniqueOptions.sort((a, b) => a.label.localeCompare(b.label));
  }, [legrandData]);

  const getBaseName = useCallback((data: LegrandMappingData): string => {
    const legalName = data.legalName || 'Unknown Company';
    const aliasName = data.aliasShippingNames && data.aliasShippingNames !== 'NONE' ? data.aliasShippingNames : null;

    if (aliasName) {
      return `${legalName} (${aliasName})`;
    } else {
      return legalName;
    }
  }, []);

  const checkForDuplicates = useMemo(() => {
    if (!legrandData.length) return () => [];

    return (field: 'alias' | 'address' | 'zipcode', value: string) => {
      let matchingEntries: LegrandMappingData[] = [];

      if (field === 'alias') {
        const exactMatch = legrandData.find(data => {
          const uniqueKey = `${data.customeCode}-${data.aliasShippingNames || data.legalName}-${data.shippingBillingAddress}`;
          return uniqueKey === value;
        });

        if (exactMatch) {
          const baseName = getBaseName(exactMatch);
          const sameBaseNameEntries = legrandData.filter(data => getBaseName(data) === baseName);

          if (sameBaseNameEntries.length === 1) {
            matchingEntries = [exactMatch];
          } else {
            const selectedDisplayName = getAliasOptions.find(option => option.value === value)?.displayName || '';

            const sameDisplayNameEntries = legrandData.filter(data => {
              const location = data.location || extractCityFromAddress(data.shippingBillingAddress || '');
              let displayName = getBaseName(data);

              const sameNameEntries = legrandData.filter(d => getBaseName(d) === displayName);
              if (sameNameEntries.length > 1) {
                if (location) {
                  displayName = `${displayName}_${location}`;
                } else if (data.zipPostal) {
                  displayName = `${displayName}_${data.zipPostal}`;
                }
              }

              return displayName === selectedDisplayName;
            });

            matchingEntries = sameDisplayNameEntries;
          }
        }
      } else if (field === 'address') {
        matchingEntries = legrandData.filter(data => data.shippingBillingAddress === value);
      } else if (field === 'zipcode') {
        matchingEntries = legrandData.filter(data => data.zipPostal === value);
      }

      return matchingEntries;
    };
  }, [legrandData, getBaseName, getAliasOptions]);

  const clearOtherBlocks = useCallback((currentPrefix: string) => {
    const prefixes = ['shipper', 'consignee', 'billto'];
    const otherPrefixes = prefixes.filter(p => p !== currentPrefix);

    otherPrefixes.forEach(prefix => {
      const currentAlias = form.getValues(`entries.${entryIndex}.${prefix}Alias`);
      const currentAddress = form.getValues(`entries.${entryIndex}.${prefix}Address`);
      const currentZipcode = form.getValues(`entries.${entryIndex}.${prefix}Zipcode`);

      if (currentAlias || currentAddress || currentZipcode) {
        form.setValue(`entries.${entryIndex}.${prefix}Alias`, '');
        form.setValue(`entries.${entryIndex}.${prefix}Address`, '');
        form.setValue(`entries.${entryIndex}.${prefix}Zipcode`, '');
      }
    });
  }, [form, entryIndex]);

  useEffect(() => {
    if (!legrandData.length) return;

    const subscription = form.watch((_, { name, type }) => {
      if (!name || !name.includes(`entries.${entryIndex}.${fieldPrefix}`) || type !== 'change') return;

      const currentAlias = form.getValues(`entries.${entryIndex}.${fieldPrefix}Alias`);
      const currentAddress = form.getValues(`entries.${entryIndex}.${fieldPrefix}Address`);
      const currentZipcode = form.getValues(`entries.${entryIndex}.${fieldPrefix}Zipcode`);

      if (currentAlias || currentAddress || currentZipcode) {
        clearOtherBlocks(fieldPrefix);
      }

      if (name === `entries.${entryIndex}.${fieldPrefix}Alias` && currentAlias) {
        const selectedData = legrandData.find(data => {
          const uniqueKey = `${data.customeCode}-${data.aliasShippingNames || data.legalName}-${data.shippingBillingAddress}`;
          return uniqueKey === currentAlias;
        });

        if (selectedData) {
          const duplicateEntries = checkForDuplicates('alias', currentAlias);

          if (duplicateEntries.length > 1) {
            if (currentAddress) {
              const addressMatchesAlias = duplicateEntries.some(entry => entry.shippingBillingAddress === currentAddress);

              if (addressMatchesAlias) {
                const specificEntry = duplicateEntries.find(entry => entry.shippingBillingAddress === currentAddress);
                if (specificEntry && currentZipcode !== specificEntry.zipPostal) {
                  form.setValue(`entries.${entryIndex}.${fieldPrefix}Zipcode`, specificEntry.zipPostal);
                }
              } else {
                form.setValue(`entries.${entryIndex}.${fieldPrefix}Address`, '');
                if (currentZipcode !== '') {
                  form.setValue(`entries.${entryIndex}.${fieldPrefix}Zipcode`, '');
                }
              }
            } else {
              if (currentZipcode) {
                const zipcodeMatchesAlias = duplicateEntries.some(entry => entry.zipPostal === currentZipcode);

                if (zipcodeMatchesAlias) {
                } else {
                  form.setValue(`entries.${entryIndex}.${fieldPrefix}Zipcode`, '');
                }
              } else {
              }
            }
          } else {

            if (currentAddress !== selectedData.shippingBillingAddress) {
              form.setValue(`entries.${entryIndex}.${fieldPrefix}Address`, selectedData.shippingBillingAddress);
            }
            if (currentZipcode !== selectedData.zipPostal) {
              form.setValue(`entries.${entryIndex}.${fieldPrefix}Zipcode`, selectedData.zipPostal);
            }
          }

          const currentCompany = form.getValues(`entries.${entryIndex}.company`);
          if (currentCompany !== selectedData.businessUnit) {
            form.setValue(`entries.${entryIndex}.company`, selectedData.businessUnit);
          }

          const allDivisions: string[] = [];
          duplicateEntries.forEach(entry => {
            if (entry.customeCode) {
              if (entry.customeCode.includes('/')) {
                const splitDivisions = entry.customeCode.split('/').map(d => d.trim());
                allDivisions.push(...splitDivisions);
              } else {
                allDivisions.push(entry.customeCode);
              }
            }
          });
          const uniqueDivisions = Array.from(new Set(allDivisions.filter(code => code)));

          if (uniqueDivisions.length === 1) {
            const currentDivision = form.getValues(`entries.${entryIndex}.division`);
            if (currentDivision !== uniqueDivisions[0]) {
              form.setValue(`entries.${entryIndex}.division`, uniqueDivisions[0]);
            }
          } else {
            const currentDivision = form.getValues(`entries.${entryIndex}.division`);
            if (currentDivision !== '') {
              form.setValue(`entries.${entryIndex}.division`, '');
            }
          }

          if (onLegrandDataChange) {
            const divisionCodeToPass = uniqueDivisions.length === 1 ? uniqueDivisions[0] : '';
            onLegrandDataChange(entryIndex, selectedData.businessUnit || '', divisionCodeToPass);
          }
        }
      }

      if (name === `entries.${entryIndex}.${fieldPrefix}Address` && currentAddress) {
        const duplicateEntries = checkForDuplicates('address', currentAddress);

        if (duplicateEntries.length > 1) {
          if (currentAlias) {
            const selectedAliasData = legrandData.find(data => {
              const uniqueKey = `${data.customeCode}-${data.aliasShippingNames || data.legalName}-${data.shippingBillingAddress}`;
              return uniqueKey === currentAlias;
            });

            if (selectedAliasData) {
              const baseName = getBaseName(selectedAliasData);

              const addressMatchesBaseName = legrandData.some(data =>
                getBaseName(data) === baseName && data.shippingBillingAddress === currentAddress
              );

              if (addressMatchesBaseName) {
                const addressSpecificEntry = legrandData.find(data =>
                  getBaseName(data) === baseName && data.shippingBillingAddress === currentAddress
                );

                if (addressSpecificEntry) {
                  const newUniqueKey = `${addressSpecificEntry.customeCode}-${addressSpecificEntry.aliasShippingNames || addressSpecificEntry.legalName}-${addressSpecificEntry.shippingBillingAddress}`;
                  if (currentAlias !== newUniqueKey) {
                    form.setValue(`entries.${entryIndex}.${fieldPrefix}Alias`, newUniqueKey);
                  }

                  if (currentZipcode !== addressSpecificEntry.zipPostal) {
                    form.setValue(`entries.${entryIndex}.${fieldPrefix}Zipcode`, addressSpecificEntry.zipPostal);
                  }

                  const currentCompany = form.getValues(`entries.${entryIndex}.company`);
                  if (currentCompany !== addressSpecificEntry.businessUnit) {
                    form.setValue(`entries.${entryIndex}.company`, addressSpecificEntry.businessUnit);
                  }

                  const currentDivision = form.getValues(`entries.${entryIndex}.division`);
                  if (currentDivision !== addressSpecificEntry.customeCode) {
                    form.setValue(`entries.${entryIndex}.division`, addressSpecificEntry.customeCode);
                  }

                  if (onLegrandDataChange) {
                    onLegrandDataChange(entryIndex, addressSpecificEntry.businessUnit || '', addressSpecificEntry.customeCode || '');
                  }
                  return; 
                }
              } else {
                form.setValue(`entries.${entryIndex}.${fieldPrefix}Alias`, '');
              }
            }
          }

          const uniqueZipcodes = Array.from(new Set(duplicateEntries.map(entry => entry.zipPostal).filter(zip => zip)));
          if (uniqueZipcodes.length === 1) {
            if (currentZipcode !== uniqueZipcodes[0]) {
              form.setValue(`entries.${entryIndex}.${fieldPrefix}Zipcode`, uniqueZipcodes[0]);
            }
          } else {
            if (currentZipcode !== '') {
              form.setValue(`entries.${entryIndex}.${fieldPrefix}Zipcode`, '');
            }
          }
        } else {
          const selectedData = duplicateEntries[0];
          if (selectedData) {
            const uniqueKey = `${selectedData.customeCode}-${selectedData.aliasShippingNames || selectedData.legalName}-${selectedData.shippingBillingAddress}`;

            if (currentAlias !== uniqueKey) {
              form.setValue(`entries.${entryIndex}.${fieldPrefix}Alias`, uniqueKey);
            }
            if (currentZipcode !== selectedData.zipPostal) {
              form.setValue(`entries.${entryIndex}.${fieldPrefix}Zipcode`, selectedData.zipPostal);
            }

            const currentCompany = form.getValues(`entries.${entryIndex}.company`);
            if (currentCompany !== selectedData.businessUnit) {
              form.setValue(`entries.${entryIndex}.company`, selectedData.businessUnit);
            }

            const currentDivision = form.getValues(`entries.${entryIndex}.division`);
            if (currentDivision !== selectedData.customeCode) {
              form.setValue(`entries.${entryIndex}.division`, selectedData.customeCode);
            }

            if (onLegrandDataChange) {
              onLegrandDataChange(entryIndex, selectedData.businessUnit || '', selectedData.customeCode || '');
            }
          }
        }
      }

      if (name === `entries.${entryIndex}.${fieldPrefix}Zipcode` && currentZipcode) {
        const duplicateEntries = checkForDuplicates('zipcode', currentZipcode);

        if (duplicateEntries.length > 1) {
          if (currentAlias !== '') {
            form.setValue(`entries.${entryIndex}.${fieldPrefix}Alias`, '');
          }
          if (currentAddress !== '') {
            form.setValue(`entries.${entryIndex}.${fieldPrefix}Address`, '');
          }
        } else {
          const selectedData = duplicateEntries[0];
          if (selectedData) {
            const uniqueKey = `${selectedData.customeCode}-${selectedData.aliasShippingNames || selectedData.legalName}-${selectedData.shippingBillingAddress}`;

            if (currentAlias !== uniqueKey) {
              form.setValue(`entries.${entryIndex}.${fieldPrefix}Alias`, uniqueKey);
            }
            if (currentAddress !== selectedData.shippingBillingAddress) {
              form.setValue(`entries.${entryIndex}.${fieldPrefix}Address`, selectedData.shippingBillingAddress);
            }

            const currentCompany = form.getValues(`entries.${entryIndex}.company`);
            if (currentCompany !== selectedData.businessUnit) {
              form.setValue(`entries.${entryIndex}.company`, selectedData.businessUnit);
            }

            const currentDivision = form.getValues(`entries.${entryIndex}.division`);
            if (currentDivision !== selectedData.customeCode) {
              form.setValue(`entries.${entryIndex}.division`, selectedData.customeCode);
            }

            if (onLegrandDataChange) {
              onLegrandDataChange(entryIndex, selectedData.businessUnit || '', selectedData.customeCode || '');
            }
          }
        }
      }
    });

    return () => subscription.unsubscribe();
  }, [legrandData.length, form, entryIndex, onLegrandDataChange, fieldPrefix]);

  const getAddressOptions = useCallback(() => {
    const currentAlias = form.getValues(`entries.${entryIndex}.${fieldPrefix}Alias`);
    const currentZipcode = form.getValues(`entries.${entryIndex}.${fieldPrefix}Zipcode`);

    if (currentAlias) {
      const selectedAliasData = legrandData.find(data => {
        const uniqueKey = `${data.customeCode}-${data.aliasShippingNames || data.legalName}-${data.shippingBillingAddress}`;
        return uniqueKey === currentAlias;
      });

      if (selectedAliasData) {
        const selectedDisplayName = getAliasOptions.find(option => option.value === currentAlias)?.displayName || '';

        const sameDisplayNameEntries = legrandData.filter(data => {
          const location = data.location || extractCityFromAddress(data.shippingBillingAddress || '');
          const zipcode = data.zipPostal || '';

          const legalName = data.legalName || 'Unknown Company';
          const aliasName = data.aliasShippingNames && data.aliasShippingNames !== 'NONE' ? data.aliasShippingNames : null;

          let displayName = '';
          if (aliasName) {
            displayName = `${legalName} (${aliasName})`;
          } else {
            displayName = legalName;
          }

          const baseName = displayName;
          const sameNameEntries = legrandData.filter(d => getBaseName(d) === displayName);
          if (sameNameEntries.length > 1) {
            if (location) {
              displayName = `${displayName}_${location}`;
            } else if (data.zipPostal) {
              displayName = `${displayName}_${data.zipPostal}`;
            }
          }

          return displayName === selectedDisplayName;
        });

        if (sameDisplayNameEntries.length > 1) {
          return sameDisplayNameEntries
            .map(data => data.shippingBillingAddress)
            .filter(address => address)
            .filter((address, index, self) => self.indexOf(address) === index)
            .sort();
        } else {
          return selectedAliasData.shippingBillingAddress ? [selectedAliasData.shippingBillingAddress] : [];
        }
      }
    }

    if (currentZipcode) {
      const duplicateEntries = checkForDuplicates('zipcode', currentZipcode);

      if (duplicateEntries.length > 1) {
        return duplicateEntries
          .map(data => data.shippingBillingAddress)
          .filter(address => address)
          .filter((address, index, self) => self.indexOf(address) === index) 
          .sort();
      }
    }

    const uniqueAddresses = new Set<string>();
    legrandData.forEach(data => {
      if (data.shippingBillingAddress) {
        uniqueAddresses.add(data.shippingBillingAddress);
      }
    });
    return Array.from(uniqueAddresses).sort();
  }, [form, entryIndex, fieldPrefix, legrandData, checkForDuplicates]);

  const getZipcodeOptions = useCallback(() => {
    const currentAlias = form.getValues(`entries.${entryIndex}.${fieldPrefix}Alias`);
    const currentAddress = form.getValues(`entries.${entryIndex}.${fieldPrefix}Address`);

    if (currentAlias) {
      const selectedAliasData = legrandData.find(data => {
        const uniqueKey = `${data.customeCode}-${data.aliasShippingNames || data.legalName}-${data.shippingBillingAddress}`;
        return uniqueKey === currentAlias;
      });

      if (selectedAliasData) {
        const selectedDisplayName = getAliasOptions.find(option => option.value === currentAlias)?.displayName || '';

        const sameDisplayNameEntries = legrandData.filter(data => {
          const location = data.location || extractCityFromAddress(data.shippingBillingAddress || '');
          const zipcode = data.zipPostal || '';

          const legalName = data.legalName || 'Unknown Company';
          const aliasName = data.aliasShippingNames && data.aliasShippingNames !== 'NONE' ? data.aliasShippingNames : null;

          let displayName = '';
          if (aliasName) {
            displayName = `${legalName} (${aliasName})`;
          } else {
            displayName = legalName;
          }
          const sameNameEntries = legrandData.filter(d => getBaseName(d) === displayName);
          if (sameNameEntries.length > 1) {
            if (location) {
              displayName = `${displayName}_${location}`;
            } else if (data.zipPostal) {
              displayName = `${displayName}_${data.zipPostal}`;
            }
          }

          return displayName === selectedDisplayName;
        });

        if (sameDisplayNameEntries.length > 1) {
          return sameDisplayNameEntries
            .map(data => data.zipPostal)
            .filter(zipcode => zipcode)
            .filter((zipcode, index, self) => self.indexOf(zipcode) === index) 
            .sort();
        } else {
          return selectedAliasData.zipPostal ? [selectedAliasData.zipPostal] : [];
        }
      }
    }

    if (currentAddress) {
      const duplicateEntries = checkForDuplicates('address', currentAddress);

      if (duplicateEntries.length > 1) {
        return duplicateEntries
          .map(data => data.zipPostal)
          .filter(zipcode => zipcode)
          .filter((zipcode, index, self) => self.indexOf(zipcode) === index) 
          .sort();
      }
    }

    const uniqueZipcodes = new Set<string>();
    legrandData.forEach(data => {
      if (data.zipPostal) {
        uniqueZipcodes.add(data.zipPostal);
      }
    });
    return Array.from(uniqueZipcodes).sort();
  }, [form, entryIndex, fieldPrefix, legrandData, checkForDuplicates]);

  const getCurrentAliasOptions = useCallback(() => {
    const currentAddress = form.getValues(`entries.${entryIndex}.${fieldPrefix}Address`);
    const currentZipcode = form.getValues(`entries.${entryIndex}.${fieldPrefix}Zipcode`);

    if (currentAddress) {
      const duplicateEntries = checkForDuplicates('address', currentAddress);
      if (duplicateEntries.length > 1) {
        return getFilteredAliasOptions();
      }
    }

    if (currentZipcode) {
      const duplicateEntries = checkForDuplicates('zipcode', currentZipcode);
      if (duplicateEntries.length > 1) {
        return getFilteredAliasOptions();
      }
    }

    return getAliasOptions;
  }, [form, entryIndex, fieldPrefix, checkForDuplicates, getAliasOptions]);

  const getFilteredAliasOptions = useCallback(() => {
    const currentAddress = form.getValues(`entries.${entryIndex}.${fieldPrefix}Address`);
    const currentZipcode = form.getValues(`entries.${entryIndex}.${fieldPrefix}Zipcode`);

    let filteredData = legrandData;

    if (currentAddress) {
      const duplicateEntries = checkForDuplicates('address', currentAddress);
      if (duplicateEntries.length > 1) {
        filteredData = duplicateEntries;
      }
    }

    if (currentZipcode) {
      const duplicateEntries = checkForDuplicates('zipcode', currentZipcode);
      if (duplicateEntries.length > 1) {
        filteredData = filteredData.filter(data =>
          duplicateEntries.some(dup => dup.id === data.id)
        );
      }
    }

    const options: Array<{
      value: string;
      label: string;
      type: 'alias' | 'company';
      displayName: string;
    }> = [];

    filteredData.forEach(data => {
      const location = data.location || extractCityFromAddress(data.shippingBillingAddress || '');
      const zipcode = data.zipPostal || '';

      let displayName = '';
      let type: 'alias' | 'company' = 'company';

      const legalName = data.legalName || 'Unknown Company';
      const aliasName = data.aliasShippingNames && data.aliasShippingNames !== 'NONE' ? data.aliasShippingNames : null;

      if (aliasName) {
        displayName = `${legalName} (${aliasName})`;
        type = 'alias';
      } else {
        displayName = legalName;
        type = 'company';
      }

      const uniqueKey = `${data.customeCode}-${data.aliasShippingNames || data.legalName}-${data.shippingBillingAddress}`;

      const sameNameEntries = filteredData.filter(d => {
        const dLegalName = d.legalName || 'Unknown Company';
        const dAliasName = d.aliasShippingNames && d.aliasShippingNames !== 'NONE' ? d.aliasShippingNames : null;

        let dDisplayName = '';
        if (dAliasName) {
          dDisplayName = `${dLegalName} (${dAliasName})`;
        } else {
          dDisplayName = dLegalName;
        }

        return dDisplayName === displayName;
      });

      let finalDisplayName = displayName;
      if (sameNameEntries.length > 1) {
        if (location) {
          finalDisplayName = `${displayName}_${location}`;
        } else if (zipcode) {
          finalDisplayName = `${displayName}_${zipcode}`;
        }
      }

      options.push({
        value: uniqueKey,
        label: finalDisplayName,
        type: type,
        displayName: finalDisplayName
      });
    });

    const uniqueOptions = options.filter((option, index, self) =>
      index === self.findIndex(o => o.value === option.value)
    );

    return uniqueOptions.sort((a, b) => a.label.localeCompare(b.label));
  }, [form, entryIndex, fieldPrefix, legrandData, checkForDuplicates]);
  
  if (loading) {
    return (
      <div className="bg-gray-50 rounded-lg p-3 max-w-sm mb-4">
        <div className="flex items-center space-x-2 mb-2">
          <MapPin className="w-4 h-4 text-purple-600" />
          <h3 className="text-sm font-semibold text-gray-900">LEGRAND Details</h3>
        </div>
        <div className="text-sm text-gray-500">Loading LEGRAND data...</div>
      </div>
    );
  }

  return (
    <div className="bg-gray-50 rounded-lg p-3 mb-3 max-w-lg">
      <div className="flex items-center space-x-2 mb-2">
        <MapPin className="w-4 h-4 text-purple-600" />
        <h3 className="text-sm font-semibold text-gray-900">{blockTitle}</h3>
      </div>

      {/* Vertical layout with all three fields - wider for better spacing */}
      <div className="grid grid-cols-1 gap-3">
        {/* Alias/Company */}
        <div className="[&_div[class*='flex'][class*='items-center']]:!h-10 [&_input]:!text-sm [&_label]:!text-sm">
          <SearchSelect
            form={form}
            name={`entries.${entryIndex}.${fieldPrefix}Alias`}
            label="Alias/Company"
            placeholder="Select Alias or Company"
            isEntryPage={true}
            className="text-sm"
            options={getCurrentAliasOptions().map((option) => ({
              value: option.value,
              label: option.displayName,
              badge: option.type === 'alias' ? 'Alias' : 'Company',
              badgeColor: option.type === 'alias' ? 'bg-blue-500' : 'bg-green-500'
            }))}
          />
        </div>

        {/* Address */}
        <div className="[&_div[class*='flex'][class*='items-center']]:!h-10 [&_input]:!text-sm [&_label]:!text-sm">
          <SearchSelect
            form={form}
            name={`entries.${entryIndex}.${fieldPrefix}Address`}
            label="Address"
            placeholder="Select Address"
            isEntryPage={true}
            className="text-sm"
            options={getAddressOptions().map((address) => ({
              value: address,
              label: address
            }))}
          />
        </div>

        {/* Zipcode */}
        <div className="[&_div[class*='flex'][class*='items-center']]:!h-10 [&_input]:!text-sm [&_label]:!text-sm">
          <SearchSelect
            form={form}
            name={`entries.${entryIndex}.${fieldPrefix}Zipcode`}
            label="Zipcode"
            placeholder="Select Zipcode"
            isEntryPage={true}
            className="text-sm"
            options={getZipcodeOptions().map((zipcode) => ({
              value: zipcode,
              label: zipcode
            }))}
          />
        </div>
      </div>
    </div>
  );
};

export default LegrandDetailsComponent;
