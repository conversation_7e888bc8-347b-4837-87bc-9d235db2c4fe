"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/user/trackSheets/page",{

/***/ "(app-pages-browser)/./app/user/trackSheets/createTrackSheet.tsx":
/*!***************************************************!*\
  !*** ./app/user/trackSheets/createTrackSheet.tsx ***!
  \***************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! react-hook-form */ \"(app-pages-browser)/./node_modules/react-hook-form/dist/index.esm.mjs\");\n/* harmony import */ var _hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @hookform/resolvers/zod */ \"(app-pages-browser)/./node_modules/@hookform/resolvers/zod/dist/zod.mjs\");\n/* harmony import */ var _components_ui_form__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/form */ \"(app-pages-browser)/./components/ui/form.tsx\");\n/* harmony import */ var _app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/_component/FormInput */ \"(app-pages-browser)/./app/_component/FormInput.tsx\");\n/* harmony import */ var _app_component_FormCheckboxGroup__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/app/_component/FormCheckboxGroup */ \"(app-pages-browser)/./app/_component/FormCheckboxGroup.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_Building2_DollarSign_FileText_Hash_Info_MinusCircle_PlusCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Building2,DollarSign,FileText,Hash,Info,MinusCircle,PlusCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/building-2.js\");\n/* harmony import */ var _barrel_optimize_names_Building2_DollarSign_FileText_Hash_Info_MinusCircle_PlusCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Building2,DollarSign,FileText,Hash,Info,MinusCircle,PlusCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Building2_DollarSign_FileText_Hash_Info_MinusCircle_PlusCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Building2,DollarSign,FileText,Hash,Info,MinusCircle,PlusCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_Building2_DollarSign_FileText_Hash_Info_MinusCircle_PlusCircle_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Building2,DollarSign,FileText,Hash,Info,MinusCircle,PlusCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/info.js\");\n/* harmony import */ var _barrel_optimize_names_Building2_DollarSign_FileText_Hash_Info_MinusCircle_PlusCircle_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Building2,DollarSign,FileText,Hash,Info,MinusCircle,PlusCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/hash.js\");\n/* harmony import */ var _barrel_optimize_names_Building2_DollarSign_FileText_Hash_Info_MinusCircle_PlusCircle_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Building2,DollarSign,FileText,Hash,Info,MinusCircle,PlusCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-minus.js\");\n/* harmony import */ var _barrel_optimize_names_Building2_DollarSign_FileText_Hash_Info_MinusCircle_PlusCircle_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Building2,DollarSign,FileText,Hash,Info,MinusCircle,PlusCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-plus.js\");\n/* harmony import */ var _lib_helpers__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/helpers */ \"(app-pages-browser)/./lib/helpers.ts\");\n/* harmony import */ var _lib_routePath__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/routePath */ \"(app-pages-browser)/./lib/routePath.ts\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! zod */ \"(app-pages-browser)/./node_modules/zod/lib/index.mjs\");\n/* harmony import */ var _app_component_SearchSelect__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/app/_component/SearchSelect */ \"(app-pages-browser)/./app/_component/SearchSelect.tsx\");\n/* harmony import */ var _app_component_PageInput__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/app/_component/PageInput */ \"(app-pages-browser)/./app/_component/PageInput.tsx\");\n/* harmony import */ var _components_ui_tooltip__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/ui/tooltip */ \"(app-pages-browser)/./components/ui/tooltip.tsx\");\n/* harmony import */ var _LegrandDetailsComponent__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ./LegrandDetailsComponent */ \"(app-pages-browser)/./app/user/trackSheets/LegrandDetailsComponent.tsx\");\n/* harmony import */ var _ClientSelectPage__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ./ClientSelectPage */ \"(app-pages-browser)/./app/user/trackSheets/ClientSelectPage.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst validateFtpPageFormat = (value)=>{\n    if (!value || value.trim() === \"\") return false;\n    const ftpPageRegex = /^(\\d+)\\s+of\\s+(\\d+)$/i;\n    const match = value.match(ftpPageRegex);\n    if (!match) return false;\n    const currentPage = parseInt(match[1], 10);\n    const totalPages = parseInt(match[2], 10);\n    return currentPage > 0 && totalPages > 0 && currentPage <= totalPages;\n};\nconst trackSheetSchema = zod__WEBPACK_IMPORTED_MODULE_16__.z.object({\n    clientId: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().min(1, \"Client is required\"),\n    entries: zod__WEBPACK_IMPORTED_MODULE_16__.z.array(zod__WEBPACK_IMPORTED_MODULE_16__.z.object({\n        company: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().min(1, \"Company is required\"),\n        division: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().optional(),\n        invoice: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().min(1, \"Invoice is required\"),\n        masterInvoice: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().optional(),\n        bol: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().optional(),\n        invoiceDate: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().min(1, \"Invoice date is required\"),\n        receivedDate: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().min(1, \"Received date is required\"),\n        shipmentDate: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().optional(),\n        carrierName: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().min(1, \"Carrier name is required\"),\n        invoiceStatus: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().min(1, \"Invoice status is required\"),\n        manualMatching: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().min(1, \"Manual matching is required\"),\n        invoiceType: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().min(1, \"Invoice type is required\"),\n        billToClient: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().optional(),\n        currency: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().min(1, \"Currency is required\"),\n        qtyShipped: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().optional(),\n        weightUnitName: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().min(1, \"Weight unit is required\"),\n        quantityBilledText: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().optional(),\n        invoiceTotal: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().min(1, \"Invoice total is required\"),\n        savings: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().optional(),\n        financialNotes: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().optional(),\n        ftpFileName: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().min(1, \"FTP File Name is required\"),\n        ftpPage: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().min(1, \"FTP Page is required\").refine((value)=>validateFtpPageFormat(value), (value)=>{\n            if (!value || value.trim() === \"\") {\n                return {\n                    message: \"FTP Page is required\"\n                };\n            }\n            const ftpPageRegex = /^(\\d+)\\s+of\\s+(\\d+)$/i;\n            const match = value.match(ftpPageRegex);\n            if (!match) {\n                return {\n                    message: \"\"\n                };\n            }\n            const currentPage = parseInt(match[1], 10);\n            const totalPages = parseInt(match[2], 10);\n            if (currentPage <= 0 || totalPages <= 0) {\n                return {\n                    message: \"Page numbers must be positive (greater than 0)\"\n                };\n            }\n            if (currentPage > totalPages) {\n                return {\n                    message: \"Please enter a page number between \".concat(totalPages, \" and \").concat(currentPage, \" \")\n                };\n            }\n            return {\n                message: \"Invalid page format\"\n            };\n        }),\n        docAvailable: zod__WEBPACK_IMPORTED_MODULE_16__.z.array(zod__WEBPACK_IMPORTED_MODULE_16__.z.string()).optional().default([]),\n        otherDocuments: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().optional(),\n        notes: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().optional(),\n        mistake: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().optional(),\n        legrandAlias: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().optional(),\n        legrandCompanyName: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().optional(),\n        legrandAddress: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().optional(),\n        legrandZipcode: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().optional(),\n        shipperAlias: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().optional(),\n        shipperAddress: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().optional(),\n        shipperZipcode: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().optional(),\n        consigneeAlias: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().optional(),\n        consigneeAddress: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().optional(),\n        consigneeZipcode: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().optional(),\n        billtoAlias: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().optional(),\n        billtoAddress: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().optional(),\n        billtoZipcode: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().optional(),\n        customFields: zod__WEBPACK_IMPORTED_MODULE_16__.z.array(zod__WEBPACK_IMPORTED_MODULE_16__.z.object({\n            id: zod__WEBPACK_IMPORTED_MODULE_16__.z.string(),\n            name: zod__WEBPACK_IMPORTED_MODULE_16__.z.string(),\n            type: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().optional(),\n            value: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().optional()\n        })).default([])\n    }))\n});\nconst CreateTrackSheet = (param)=>{\n    let { client, carrier, associate, userData, activeView, setActiveView, permissions } = param;\n    _s();\n    const companyFieldRefs = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)([]);\n    const [generatedFilenames, setGeneratedFilenames] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [filenameValidation, setFilenameValidation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [missingFields, setMissingFields] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [legrandData, setLegrandData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [manualMatchingData, setManualMatchingData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [customFieldsRefresh, setCustomFieldsRefresh] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [showFullForm, setShowFullForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [initialAssociateId, setInitialAssociateId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [initialClientId, setInitialClientId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const selectionForm = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_17__.useForm)({\n        defaultValues: {\n            associateId: \"\",\n            clientId: \"\"\n        }\n    });\n    const associateOptions = associate === null || associate === void 0 ? void 0 : associate.map((a)=>{\n        var _a_id;\n        return {\n            value: (_a_id = a.id) === null || _a_id === void 0 ? void 0 : _a_id.toString(),\n            label: a.name,\n            name: a.name\n        };\n    });\n    const carrierOptions = carrier === null || carrier === void 0 ? void 0 : carrier.map((c)=>{\n        var _c_id;\n        return {\n            value: (_c_id = c.id) === null || _c_id === void 0 ? void 0 : _c_id.toString(),\n            label: c.name\n        };\n    });\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_9__.useRouter)();\n    const form = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_17__.useForm)({\n        resolver: (0,_hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_2__.zodResolver)(trackSheetSchema),\n        defaultValues: {\n            associateId: \"\",\n            clientId: \"\",\n            entries: [\n                {\n                    company: \"\",\n                    division: \"\",\n                    invoice: \"\",\n                    masterInvoice: \"\",\n                    bol: \"\",\n                    invoiceDate: \"\",\n                    receivedDate: \"\",\n                    shipmentDate: \"\",\n                    carrierName: \"\",\n                    invoiceStatus: \"ENTRY\",\n                    manualMatching: \"\",\n                    invoiceType: \"\",\n                    billToClient: \"\",\n                    currency: \"\",\n                    qtyShipped: \"\",\n                    weightUnitName: \"\",\n                    quantityBilledText: \"\",\n                    invoiceTotal: \"\",\n                    savings: \"\",\n                    financialNotes: \"\",\n                    ftpFileName: \"\",\n                    ftpPage: \"\",\n                    docAvailable: [],\n                    otherDocuments: \"\",\n                    notes: \"\",\n                    mistake: \"\",\n                    legrandAlias: \"\",\n                    legrandCompanyName: \"\",\n                    legrandAddress: \"\",\n                    legrandZipcode: \"\",\n                    shipperAlias: \"\",\n                    shipperAddress: \"\",\n                    shipperZipcode: \"\",\n                    consigneeAlias: \"\",\n                    consigneeAddress: \"\",\n                    consigneeZipcode: \"\",\n                    billtoAlias: \"\",\n                    billtoAddress: \"\",\n                    billtoZipcode: \"\",\n                    customFields: []\n                }\n            ]\n        }\n    });\n    const getFilteredClientOptions = ()=>{\n        if (!initialAssociateId) {\n            return (client === null || client === void 0 ? void 0 : client.map((c)=>{\n                var _c_id;\n                return {\n                    value: (_c_id = c.id) === null || _c_id === void 0 ? void 0 : _c_id.toString(),\n                    label: c.client_name,\n                    name: c.client_name\n                };\n            })) || [];\n        }\n        const filteredClients = (client === null || client === void 0 ? void 0 : client.filter((c)=>{\n            var _c_associateId;\n            return ((_c_associateId = c.associateId) === null || _c_associateId === void 0 ? void 0 : _c_associateId.toString()) === initialAssociateId;\n        })) || [];\n        return filteredClients.map((c)=>{\n            var _c_id;\n            return {\n                value: (_c_id = c.id) === null || _c_id === void 0 ? void 0 : _c_id.toString(),\n                label: c.client_name,\n                name: c.client_name\n            };\n        });\n    };\n    const clientOptions = getFilteredClientOptions();\n    const entries = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_17__.useWatch)({\n        control: form.control,\n        name: \"entries\"\n    });\n    const validateClientForAssociate = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((associateId, currentClientId)=>{\n        if (associateId && currentClientId) {\n            var _currentClient_associateId;\n            const currentClient = client === null || client === void 0 ? void 0 : client.find((c)=>{\n                var _c_id;\n                return ((_c_id = c.id) === null || _c_id === void 0 ? void 0 : _c_id.toString()) === currentClientId;\n            });\n            if (currentClient && ((_currentClient_associateId = currentClient.associateId) === null || _currentClient_associateId === void 0 ? void 0 : _currentClient_associateId.toString()) !== associateId) {\n                form.setValue(\"clientId\", \"\");\n                setInitialClientId(\"\");\n                return false;\n            }\n        }\n        return true;\n    }, [\n        client,\n        form\n    ]);\n    const clearEntrySpecificClients = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        const currentEntries = form.getValues(\"entries\") || [];\n        if (currentEntries.length > 0) {\n            const hasEntrySpecificClients = currentEntries.some((entry)=>entry.clientId);\n            if (hasEntrySpecificClients) {\n                const updatedEntries = currentEntries.map((entry)=>({\n                        ...entry,\n                        clientId: \"\"\n                    }));\n                form.setValue(\"entries\", updatedEntries);\n            }\n        }\n    }, [\n        form\n    ]);\n    const fetchLegrandData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async ()=>{\n        try {\n            const response = await (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_7__.getAllData)(_lib_routePath__WEBPACK_IMPORTED_MODULE_8__.legrandMapping_routes.GET_LEGRAND_MAPPINGS);\n            if (response && Array.isArray(response)) {\n                setLegrandData(response);\n            }\n        } catch (error) {\n            sonner__WEBPACK_IMPORTED_MODULE_10__.toast.error(\"Error fetching LEGRAND mapping data:\", error);\n        }\n    }, []);\n    const fetchManualMatchingData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async ()=>{\n        try {\n            const response = await (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_7__.getAllData)(_lib_routePath__WEBPACK_IMPORTED_MODULE_8__.manualMatchingMapping_routes.GET_MANUAL_MATCHING_MAPPINGS);\n            if (response && Array.isArray(response)) {\n                setManualMatchingData(response);\n            }\n        } catch (error) {\n            sonner__WEBPACK_IMPORTED_MODULE_10__.toast.error(\"Error fetching manual matching mapping data:\", error);\n        }\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchLegrandData();\n        fetchManualMatchingData();\n    }, [\n        fetchLegrandData,\n        fetchManualMatchingData\n    ]);\n    const handleLegrandDataChange = (entryIndex, businessUnit, divisionCode)=>{\n        form.setValue(\"entries.\".concat(entryIndex, \".company\"), businessUnit);\n        if (divisionCode) {\n            form.setValue(\"entries.\".concat(entryIndex, \".division\"), divisionCode);\n            handleManualMatchingAutoFill(entryIndex, divisionCode);\n        } else {\n            form.setValue(\"entries.\".concat(entryIndex, \".division\"), \"\");\n            form.setValue(\"entries.\".concat(entryIndex, \".manualMatching\"), \"\");\n        }\n    };\n    const handleManualMatchingAutoFill = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((entryIndex, division)=>{\n        var _formValues_entries, _clientOptions_find;\n        if (!division || !manualMatchingData.length) {\n            return;\n        }\n        const formValues = form.getValues();\n        const entry = (_formValues_entries = formValues.entries) === null || _formValues_entries === void 0 ? void 0 : _formValues_entries[entryIndex];\n        const entryClientId = (entry === null || entry === void 0 ? void 0 : entry.clientId) || (entryIndex === 0 ? formValues.clientId : \"\");\n        const entryClientName = (clientOptions === null || clientOptions === void 0 ? void 0 : (_clientOptions_find = clientOptions.find((c)=>c.value === entryClientId)) === null || _clientOptions_find === void 0 ? void 0 : _clientOptions_find.name) || \"\";\n        if (entryClientName !== \"LEGRAND\") {\n            return;\n        }\n        const matchingEntry = manualMatchingData.find((mapping)=>mapping.division === division);\n        if (matchingEntry && matchingEntry.ManualShipment) {\n            form.setValue(\"entries.\".concat(entryIndex, \".manualMatching\"), matchingEntry.ManualShipment);\n        } else {\n            form.setValue(\"entries.\".concat(entryIndex, \".manualMatching\"), \"\");\n        }\n    }, [\n        form,\n        manualMatchingData,\n        clientOptions\n    ]);\n    const fetchCustomFieldsForClient = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async (clientId)=>{\n        if (!clientId) return [];\n        try {\n            const allCustomFieldsResponse = await (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_7__.getAllData)(\"\".concat(_lib_routePath__WEBPACK_IMPORTED_MODULE_8__.clientCustomFields_routes.GET_CLIENT_CUSTOM_FIELDS, \"/\").concat(clientId));\n            let customFieldsData = [];\n            if (allCustomFieldsResponse && allCustomFieldsResponse.custom_fields && allCustomFieldsResponse.custom_fields.length > 0) {\n                customFieldsData = allCustomFieldsResponse.custom_fields.map((field)=>{\n                    let autoFilledValue = \"\";\n                    if (field.type === \"AUTO\") {\n                        if (field.autoOption === \"DATE\") {\n                            autoFilledValue = new Date().toISOString().split(\"T\")[0];\n                        } else if (field.autoOption === \"USERNAME\") {\n                            autoFilledValue = (userData === null || userData === void 0 ? void 0 : userData.username) || \"\";\n                        }\n                    }\n                    return {\n                        id: field.id,\n                        name: field.name,\n                        type: field.type,\n                        autoOption: field.autoOption,\n                        value: autoFilledValue\n                    };\n                });\n            }\n            return customFieldsData;\n        } catch (error) {\n            return [];\n        }\n    }, [\n        userData\n    ]);\n    const { fields, append, remove } = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_17__.useFieldArray)({\n        control: form.control,\n        name: \"entries\"\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        companyFieldRefs.current = companyFieldRefs.current.slice(0, fields.length);\n    }, [\n        fields.length\n    ]);\n    const generateFilename = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((entryIndex, formValues)=>{\n        try {\n            const entry = formValues.entries[entryIndex];\n            if (!entry) return {\n                filename: \"\",\n                isValid: false,\n                missing: [\n                    \"Entry data\"\n                ]\n            };\n            const missing = [];\n            const selectedAssociate = associate === null || associate === void 0 ? void 0 : associate.find((a)=>{\n                var _a_id;\n                return ((_a_id = a.id) === null || _a_id === void 0 ? void 0 : _a_id.toString()) === formValues.associateId;\n            });\n            const associateName = (selectedAssociate === null || selectedAssociate === void 0 ? void 0 : selectedAssociate.name) || \"\";\n            if (!associateName) {\n                missing.push(\"Associate\");\n            }\n            const entryClientId = entry.clientId || formValues.clientId;\n            const selectedClient = client === null || client === void 0 ? void 0 : client.find((c)=>{\n                var _c_id;\n                return ((_c_id = c.id) === null || _c_id === void 0 ? void 0 : _c_id.toString()) === entryClientId;\n            });\n            const clientName = (selectedClient === null || selectedClient === void 0 ? void 0 : selectedClient.client_name) || \"\";\n            if (!clientName) {\n                missing.push(\"Client\");\n            }\n            let carrierName = \"\";\n            if (entry.carrierName) {\n                const carrierOption = carrier === null || carrier === void 0 ? void 0 : carrier.find((c)=>{\n                    var _c_id;\n                    return ((_c_id = c.id) === null || _c_id === void 0 ? void 0 : _c_id.toString()) === entry.carrierName;\n                });\n                carrierName = (carrierOption === null || carrierOption === void 0 ? void 0 : carrierOption.name) || \"\";\n            }\n            if (!carrierName) {\n                missing.push(\"Carrier\");\n            }\n            const receivedDate = entry.receivedDate;\n            const invoiceDate = entry.invoiceDate;\n            const currentDate = new Date();\n            const year = currentDate.getFullYear().toString();\n            const month = currentDate.toLocaleString(\"default\", {\n                month: \"short\"\n            }).toUpperCase();\n            if (!invoiceDate) {\n                missing.push(\"Invoice Date\");\n            }\n            let receivedDateStr = \"\";\n            if (receivedDate) {\n                // Parse DD/MM/YYYY format correctly\n                const dateParts = receivedDate.split(\"/\");\n                if (dateParts.length === 3) {\n                    const day = parseInt(dateParts[0], 10);\n                    const month = parseInt(dateParts[1], 10) - 1; // Month is 0-indexed\n                    const year = parseInt(dateParts[2], 10);\n                    const date = new Date(year, month, day);\n                    receivedDateStr = date.toISOString().split(\"T\")[0];\n                } else {\n                    // Fallback to original parsing if format doesn't match DD/MM/YYYY\n                    const date = new Date(receivedDate);\n                    receivedDateStr = date.toISOString().split(\"T\")[0];\n                }\n            } else {\n                missing.push(\"Received Date\");\n            }\n            const ftpFileName = entry.ftpFileName || \"\";\n            const baseFilename = ftpFileName ? ftpFileName.endsWith(\".pdf\") ? ftpFileName : \"\".concat(ftpFileName, \".pdf\") : \"\";\n            if (!baseFilename) {\n                missing.push(\"FTP File Name\");\n            }\n            const isValid = missing.length === 0;\n            const filename = isValid ? \"/\".concat(associateName, \"/\").concat(clientName, \"/CARRIERINVOICES/\").concat(carrierName, \"/\").concat(year, \"/\").concat(month, \"/\").concat(receivedDateStr, \"/\").concat(baseFilename) : \"\";\n            return {\n                filename,\n                isValid,\n                missing\n            };\n        } catch (error) {\n            return {\n                filename: \"\",\n                isValid: false,\n                missing: [\n                    \"Error generating filename\"\n                ]\n            };\n        }\n    }, [\n        client,\n        carrier,\n        associate\n    ]);\n    const handleCompanyAutoPopulation = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((entryIndex, entryClientId)=>{\n        var _clientOptions_find;\n        const entryClientName = (clientOptions === null || clientOptions === void 0 ? void 0 : (_clientOptions_find = clientOptions.find((c)=>c.value === entryClientId)) === null || _clientOptions_find === void 0 ? void 0 : _clientOptions_find.name) || \"\";\n        const currentEntry = form.getValues(\"entries.\".concat(entryIndex));\n        if (entryClientName && entryClientName !== \"LEGRAND\") {\n            form.setValue(\"entries.\".concat(entryIndex, \".company\"), entryClientName);\n        } else if (entryClientName === \"LEGRAND\") {\n            const shipperAlias = currentEntry.shipperAlias;\n            const consigneeAlias = currentEntry.consigneeAlias;\n            const billtoAlias = currentEntry.billtoAlias;\n            const hasAnyLegrandData = shipperAlias || consigneeAlias || billtoAlias;\n            if (!hasAnyLegrandData && currentEntry.company !== \"\") {\n                form.setValue(\"entries.\".concat(entryIndex, \".company\"), \"\");\n            }\n        } else {\n            if (currentEntry.company !== \"\") {\n                form.setValue(\"entries.\".concat(entryIndex, \".company\"), \"\");\n            }\n        }\n    }, [\n        form,\n        clientOptions\n    ]);\n    const handleCustomFieldsFetch = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async (entryIndex, entryClientId)=>{\n        var _currentCustomFields_, _currentCustomFields_1;\n        if (!entryClientId) {\n            const currentCustomFields = form.getValues(\"entries.\".concat(entryIndex, \".customFields\"));\n            if (currentCustomFields && currentCustomFields.length > 0) {\n                form.setValue(\"entries.\".concat(entryIndex, \".customFields\"), []);\n            }\n            return;\n        }\n        const currentCustomFields = form.getValues(\"entries.\".concat(entryIndex, \".customFields\")) || [];\n        const hasEmptyAutoUsernameFields = currentCustomFields.some((field)=>field.type === \"AUTO\" && field.autoOption === \"USERNAME\" && !field.value && (userData === null || userData === void 0 ? void 0 : userData.username));\n        const shouldFetchCustomFields = currentCustomFields.length === 0 || currentCustomFields.length > 0 && !((_currentCustomFields_ = currentCustomFields[0]) === null || _currentCustomFields_ === void 0 ? void 0 : _currentCustomFields_.clientId) || ((_currentCustomFields_1 = currentCustomFields[0]) === null || _currentCustomFields_1 === void 0 ? void 0 : _currentCustomFields_1.clientId) !== entryClientId || hasEmptyAutoUsernameFields;\n        if (shouldFetchCustomFields) {\n            const customFieldsData = await fetchCustomFieldsForClient(entryClientId);\n            const fieldsWithClientId = customFieldsData.map((field)=>({\n                    ...field,\n                    clientId: entryClientId\n                }));\n            form.setValue(\"entries.\".concat(entryIndex, \".customFields\"), fieldsWithClientId);\n            setTimeout(()=>{\n                fieldsWithClientId.forEach((field, fieldIndex)=>{\n                    const fieldPath = \"entries.\".concat(entryIndex, \".customFields.\").concat(fieldIndex, \".value\");\n                    if (field.value) {\n                        form.setValue(fieldPath, field.value);\n                    }\n                });\n                setCustomFieldsRefresh((prev)=>prev + 1);\n            }, 100);\n        }\n    }, [\n        form,\n        fetchCustomFieldsForClient,\n        userData === null || userData === void 0 ? void 0 : userData.username\n    ]);\n    const updateFilenames = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        const formValues = form.getValues();\n        const newFilenames = [];\n        const newValidation = [];\n        const newMissingFields = [];\n        if (formValues.entries && Array.isArray(formValues.entries)) {\n            formValues.entries.forEach((_, index)=>{\n                const { filename, isValid, missing } = generateFilename(index, formValues);\n                newFilenames[index] = filename;\n                newValidation[index] = isValid;\n                newMissingFields[index] = missing || [];\n            });\n        }\n        setGeneratedFilenames(newFilenames);\n        setFilenameValidation(newValidation);\n        setMissingFields(newMissingFields);\n    }, [\n        form,\n        generateFilename\n    ]);\n    const handleInitialSelection = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((associateId, clientId)=>{\n        form.setValue(\"associateId\", associateId);\n        form.setValue(\"clientId\", clientId);\n        setTimeout(()=>{\n            handleCompanyAutoPopulation(0, clientId);\n            handleCustomFieldsFetch(0, clientId);\n            updateFilenames();\n        }, 50);\n        setShowFullForm(true);\n    }, [\n        form,\n        handleCompanyAutoPopulation,\n        handleCustomFieldsFetch,\n        updateFilenames\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const timeoutId = setTimeout(()=>{\n            updateFilenames();\n            const formValues = form.getValues();\n            if (formValues.entries && Array.isArray(formValues.entries)) {\n                formValues.entries.forEach((entry, index)=>{\n                    const entryClientId = (entry === null || entry === void 0 ? void 0 : entry.clientId) || (index === 0 ? formValues.clientId : \"\");\n                    if (entryClientId) {\n                        handleCustomFieldsFetch(index, entryClientId);\n                    }\n                });\n            }\n        }, 50);\n        return ()=>clearTimeout(timeoutId);\n    }, [\n        updateFilenames,\n        handleCustomFieldsFetch\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const subscription = form.watch((_, param)=>{\n            let { name } = param;\n            if (name && (name.includes(\"associateId\") || name.includes(\"clientId\") || name.includes(\"carrierName\") || name.includes(\"invoiceDate\") || name.includes(\"receivedDate\") || name.includes(\"ftpFileName\") || name.includes(\"company\") || name.includes(\"division\"))) {\n                updateFilenames();\n                if (name.includes(\"division\")) {\n                    const entryMatch = name.match(/entries\\.(\\d+)\\.division/);\n                    if (entryMatch) {\n                        var _formValues_entries, _clientOptions_find;\n                        const entryIndex = parseInt(entryMatch[1], 10);\n                        const formValues = form.getValues();\n                        const entry = (_formValues_entries = formValues.entries) === null || _formValues_entries === void 0 ? void 0 : _formValues_entries[entryIndex];\n                        const entryClientId = (entry === null || entry === void 0 ? void 0 : entry.clientId) || (entryIndex === 0 ? formValues.clientId : \"\");\n                        const entryClientName = (clientOptions === null || clientOptions === void 0 ? void 0 : (_clientOptions_find = clientOptions.find((c)=>c.value === entryClientId)) === null || _clientOptions_find === void 0 ? void 0 : _clientOptions_find.name) || \"\";\n                        if (entryClientName === \"LEGRAND\") {\n                            var _formValues_entries_entryIndex, _formValues_entries1;\n                            const divisionValue = (_formValues_entries1 = formValues.entries) === null || _formValues_entries1 === void 0 ? void 0 : (_formValues_entries_entryIndex = _formValues_entries1[entryIndex]) === null || _formValues_entries_entryIndex === void 0 ? void 0 : _formValues_entries_entryIndex.division;\n                            if (divisionValue) {\n                                handleManualMatchingAutoFill(entryIndex, divisionValue);\n                            }\n                        }\n                    }\n                }\n            }\n        });\n        return ()=>subscription.unsubscribe();\n    }, [\n        form,\n        updateFilenames,\n        handleManualMatchingAutoFill,\n        clientOptions\n    ]);\n    const onSubmit = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async (values)=>{\n        try {\n            const currentFormValues = form.getValues();\n            const currentValidation = [];\n            const currentMissingFields = [];\n            const currentFilenames = [];\n            if (currentFormValues.entries && Array.isArray(currentFormValues.entries)) {\n                currentFormValues.entries.forEach((_, index)=>{\n                    const { filename, isValid, missing } = generateFilename(index, currentFormValues);\n                    currentValidation[index] = isValid;\n                    currentMissingFields[index] = missing || [];\n                    currentFilenames[index] = filename;\n                });\n            }\n            const allFilenamesValid = currentValidation.every((isValid)=>isValid);\n            if (!allFilenamesValid) {\n                const invalidEntries = currentValidation.map((isValid, index)=>({\n                        index,\n                        isValid,\n                        missing: currentMissingFields[index]\n                    })).filter((entry)=>!entry.isValid);\n                const errorDetails = invalidEntries.map((entry)=>\"Entry \".concat(entry.index + 1, \": \").concat(entry.missing.join(\", \"))).join(\" | \");\n                sonner__WEBPACK_IMPORTED_MODULE_10__.toast.error(\"Cannot submit: Missing fields - \".concat(errorDetails));\n                return;\n            }\n            const entries = values.entries.map((entry, index)=>{\n                var _entry_customFields;\n                return {\n                    company: entry.company,\n                    division: entry.division,\n                    invoice: entry.invoice,\n                    masterInvoice: entry.masterInvoice,\n                    bol: entry.bol,\n                    invoiceDate: entry.invoiceDate,\n                    receivedDate: entry.receivedDate,\n                    shipmentDate: entry.shipmentDate,\n                    carrierId: entry.carrierName,\n                    invoiceStatus: entry.invoiceStatus,\n                    manualMatching: entry.manualMatching,\n                    invoiceType: entry.invoiceType,\n                    billToClient: entry.billToClient,\n                    currency: entry.currency,\n                    qtyShipped: entry.qtyShipped,\n                    weightUnitName: entry.weightUnitName,\n                    quantityBilledText: entry.quantityBilledText,\n                    invoiceTotal: entry.invoiceTotal,\n                    savings: entry.savings,\n                    ftpFileName: entry.ftpFileName,\n                    ftpPage: entry.ftpPage,\n                    docAvailable: entry.docAvailable,\n                    notes: entry.notes,\n                    mistake: entry.mistake,\n                    filePath: generatedFilenames[index],\n                    customFields: (_entry_customFields = entry.customFields) === null || _entry_customFields === void 0 ? void 0 : _entry_customFields.map((cf)=>({\n                            id: cf.id,\n                            value: cf.value\n                        }))\n                };\n            });\n            const formData = {\n                clientId: values.clientId,\n                entries: entries\n            };\n            /* eslint-disable */ console.log(...oo_oo(\"1585064579_851_8_851_54_4\", \"Submitting form data:\", formData));\n            /* eslint-disable */ console.log(...oo_oo(\"1585064579_852_8_852_119_4\", \"Bill to Client values:\", entries.map((e)=>({\n                    invoice: e.invoice,\n                    billToClient: e.billToClient\n                }))));\n            const result = await (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_7__.formSubmit)(_lib_routePath__WEBPACK_IMPORTED_MODULE_8__.trackSheets_routes.CREATE_TRACK_SHEETS, \"POST\", formData);\n            /* eslint-disable */ console.log(...oo_oo(\"1585064579_860_8_860_44_4\", \"API Response:\", result));\n            if (result.success) {\n                sonner__WEBPACK_IMPORTED_MODULE_10__.toast.success(\"All TrackSheets created successfully\");\n                form.reset();\n                setTimeout(()=>{\n                    handleInitialSelection(initialAssociateId, initialClientId);\n                }, 100);\n            } else {\n                sonner__WEBPACK_IMPORTED_MODULE_10__.toast.error(result.message || \"Failed to create TrackSheets\");\n            }\n            router.refresh();\n        } catch (error) {\n            sonner__WEBPACK_IMPORTED_MODULE_10__.toast.error(\"An error occurred while creating the TrackSheets\");\n        }\n    }, [\n        form,\n        router,\n        generateFilename,\n        initialAssociateId,\n        initialClientId,\n        handleInitialSelection,\n        generatedFilenames\n    ]);\n    const addNewEntry = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        const newIndex = fields.length;\n        append({\n            clientId: initialClientId,\n            company: \"\",\n            division: \"\",\n            invoice: \"\",\n            masterInvoice: \"\",\n            bol: \"\",\n            invoiceDate: \"\",\n            receivedDate: \"\",\n            shipmentDate: \"\",\n            carrierName: \"\",\n            invoiceStatus: \"ENTRY\",\n            manualMatching: \"\",\n            invoiceType: \"\",\n            billToClient: \"\",\n            currency: \"\",\n            qtyShipped: \"\",\n            weightUnitName: \"\",\n            quantityBilledText: \"\",\n            invoiceTotal: \"\",\n            savings: \"\",\n            financialNotes: \"\",\n            ftpFileName: \"\",\n            ftpPage: \"\",\n            docAvailable: [],\n            otherDocuments: \"\",\n            notes: \"\",\n            mistake: \"\",\n            legrandAlias: \"\",\n            legrandCompanyName: \"\",\n            legrandAddress: \"\",\n            legrandZipcode: \"\",\n            shipperAlias: \"\",\n            shipperAddress: \"\",\n            shipperZipcode: \"\",\n            consigneeAlias: \"\",\n            consigneeAddress: \"\",\n            consigneeZipcode: \"\",\n            billtoAlias: \"\",\n            billtoAddress: \"\",\n            billtoZipcode: \"\",\n            customFields: []\n        });\n        setTimeout(()=>{\n            handleCompanyAutoPopulation(newIndex, initialClientId);\n            handleCustomFieldsFetch(newIndex, initialClientId);\n            if (companyFieldRefs.current[newIndex]) {\n                var _companyFieldRefs_current_newIndex, _companyFieldRefs_current_newIndex1, _companyFieldRefs_current_newIndex2;\n                const inputElement = ((_companyFieldRefs_current_newIndex = companyFieldRefs.current[newIndex]) === null || _companyFieldRefs_current_newIndex === void 0 ? void 0 : _companyFieldRefs_current_newIndex.querySelector(\"input\")) || ((_companyFieldRefs_current_newIndex1 = companyFieldRefs.current[newIndex]) === null || _companyFieldRefs_current_newIndex1 === void 0 ? void 0 : _companyFieldRefs_current_newIndex1.querySelector(\"button\")) || ((_companyFieldRefs_current_newIndex2 = companyFieldRefs.current[newIndex]) === null || _companyFieldRefs_current_newIndex2 === void 0 ? void 0 : _companyFieldRefs_current_newIndex2.querySelector(\"select\"));\n                if (inputElement) {\n                    inputElement.focus();\n                    try {\n                        inputElement.click();\n                    } catch (e) {}\n                }\n            }\n            updateFilenames();\n        }, 200);\n    }, [\n        append,\n        fields.length,\n        updateFilenames,\n        initialClientId,\n        handleCompanyAutoPopulation,\n        handleCustomFieldsFetch\n    ]);\n    const handleFormKeyDown = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((e)=>{\n        if (e.ctrlKey && (e.key === \"s\" || e.key === \"S\")) {\n            e.preventDefault();\n            form.handleSubmit(onSubmit)();\n        } else if (e.shiftKey && e.key === \"Enter\") {\n            e.preventDefault();\n            addNewEntry();\n        } else if (e.key === \"Enter\" && !e.ctrlKey && !e.shiftKey && !e.altKey) {\n            const activeElement = document.activeElement;\n            const isSubmitButton = (activeElement === null || activeElement === void 0 ? void 0 : activeElement.getAttribute(\"type\")) === \"submit\";\n            if (isSubmitButton) {\n                e.preventDefault();\n                form.handleSubmit(onSubmit)();\n            }\n        }\n    }, [\n        form,\n        onSubmit,\n        addNewEntry\n    ]);\n    const removeEntry = (index)=>{\n        if (fields.length > 1) {\n            remove(index);\n        } else {\n            sonner__WEBPACK_IMPORTED_MODULE_10__.toast.error(\"You must have at least one entry\");\n        }\n    };\n    const getFilteredDivisionOptions = (company, entryIndex)=>{\n        if (!company || !legrandData.length) {\n            return [];\n        }\n        if (entryIndex !== undefined) {\n            var _formValues_entries, _clientOptions_find;\n            const formValues = form.getValues();\n            const entry = (_formValues_entries = formValues.entries) === null || _formValues_entries === void 0 ? void 0 : _formValues_entries[entryIndex];\n            const entryClientId = (entry === null || entry === void 0 ? void 0 : entry.clientId) || (entryIndex === 0 ? formValues.clientId : \"\");\n            const entryClientName = (clientOptions === null || clientOptions === void 0 ? void 0 : (_clientOptions_find = clientOptions.find((c)=>c.value === entryClientId)) === null || _clientOptions_find === void 0 ? void 0 : _clientOptions_find.name) || \"\";\n            if (entryClientName === \"LEGRAND\") {\n                const shipperAlias = form.getValues(\"entries.\".concat(entryIndex, \".shipperAlias\"));\n                const consigneeAlias = form.getValues(\"entries.\".concat(entryIndex, \".consigneeAlias\"));\n                const billtoAlias = form.getValues(\"entries.\".concat(entryIndex, \".billtoAlias\"));\n                const currentAlias = shipperAlias || consigneeAlias || billtoAlias;\n                if (currentAlias) {\n                    const selectedData = legrandData.find((data)=>{\n                        const uniqueKey = \"\".concat(data.customeCode, \"-\").concat(data.aliasShippingNames || data.legalName, \"-\").concat(data.shippingBillingAddress);\n                        return uniqueKey === currentAlias;\n                    });\n                    if (selectedData) {\n                        const baseAliasName = selectedData.aliasShippingNames && selectedData.aliasShippingNames !== \"NONE\" ? selectedData.aliasShippingNames : selectedData.legalName;\n                        const sameAliasEntries = legrandData.filter((data)=>{\n                            const dataAliasName = data.aliasShippingNames && data.aliasShippingNames !== \"NONE\" ? data.aliasShippingNames : data.legalName;\n                            return dataAliasName === baseAliasName;\n                        });\n                        const allDivisions = [];\n                        sameAliasEntries.forEach((entry)=>{\n                            if (entry.customeCode) {\n                                if (entry.customeCode.includes(\"/\")) {\n                                    const splitDivisions = entry.customeCode.split(\"/\").map((d)=>d.trim());\n                                    allDivisions.push(...splitDivisions);\n                                } else {\n                                    allDivisions.push(entry.customeCode);\n                                }\n                            }\n                        });\n                        const uniqueDivisions = Array.from(new Set(allDivisions.filter((code)=>code)));\n                        if (uniqueDivisions.length > 1) {\n                            const contextDivisions = uniqueDivisions.sort().map((code)=>({\n                                    value: code,\n                                    label: code\n                                }));\n                            return contextDivisions;\n                        } else {}\n                    }\n                }\n            }\n        }\n        const allDivisions = [];\n        legrandData.filter((data)=>data.businessUnit === company && data.customeCode).forEach((data)=>{\n            if (data.customeCode.includes(\"/\")) {\n                const splitDivisions = data.customeCode.split(\"/\").map((d)=>d.trim());\n                allDivisions.push(...splitDivisions);\n            } else {\n                allDivisions.push(data.customeCode);\n            }\n        });\n        const divisions = Array.from(new Set(allDivisions.filter((code)=>code))).sort().map((code)=>({\n                value: code,\n                label: code\n            }));\n        return divisions;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_13__.TooltipProvider, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-gray-50 to-gray-100\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full px-2 py-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-4 pl-3 mb-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                variant: \"default\",\n                                onClick: ()=>setActiveView(\"view\"),\n                                className: \"w-40 shadow-md rounded-xl text-base transition-all duration-200 \".concat(activeView === \"view\" ? \"bg-neutral-800 hover:bg-neutral-900 text-white\" : \"bg-white text-neutral-800 border border-neutral-300 hover:bg-neutral-100\"),\n                                children: \"View TrackSheet\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                lineNumber: 1098,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                variant: \"default\",\n                                onClick: ()=>setActiveView(\"create\"),\n                                className: \"w-40 shadow-md rounded-xl text-base transition-all duration-200 \".concat(activeView === \"create\" ? \"bg-neutral-800 hover:bg-neutral-900 text-white\" : \"bg-white text-neutral-800 border border-neutral-300 hover:bg-neutral-100\"),\n                                children: \"Create TrackSheet\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                lineNumber: 1109,\n                                columnNumber: 13\n                            }, undefined),\n                            activeView === \"create\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-4 ml-4 flex-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2 mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building2_DollarSign_FileText_Hash_Info_MinusCircle_PlusCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                className: \"w-5 h-5 text-blue-600\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                lineNumber: 1125,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-lg font-semibold text-gray-900\",\n                                                children: \"Create TrackSheet\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                lineNumber: 1126,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                        lineNumber: 1124,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_3__.Form, {\n                                        ...selectionForm,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_SearchSelect__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        form: selectionForm,\n                                                        name: \"associateId\",\n                                                        label: \"Select Associate\",\n                                                        placeholder: \"Search Associate...\",\n                                                        isRequired: true,\n                                                        options: associateOptions || [],\n                                                        onValueChange: (value)=>{\n                                                            setInitialAssociateId(value);\n                                                            if (value && initialClientId) {\n                                                                validateClientForAssociate(value, initialClientId);\n                                                            } else {\n                                                                setInitialClientId(\"\");\n                                                                selectionForm.setValue(\"clientId\", \"\");\n                                                            }\n                                                            setShowFullForm(false);\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                        lineNumber: 1134,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                    lineNumber: 1133,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_SearchSelect__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        form: selectionForm,\n                                                        name: \"clientId\",\n                                                        label: \"Select Client\",\n                                                        placeholder: \"Search Client...\",\n                                                        isRequired: true,\n                                                        disabled: !initialAssociateId,\n                                                        options: clientOptions || [],\n                                                        onValueChange: (value)=>{\n                                                            setInitialClientId(value);\n                                                            if (showFullForm) {\n                                                                // Clear all form data when client changes\n                                                                form.reset();\n                                                                clearEntrySpecificClients();\n                                                            }\n                                                            if (value && initialAssociateId) {\n                                                                setTimeout(()=>{\n                                                                    handleInitialSelection(initialAssociateId, value);\n                                                                }, 100);\n                                                            } else {\n                                                                setShowFullForm(false);\n                                                            }\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                        lineNumber: 1156,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                    lineNumber: 1155,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                            lineNumber: 1132,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                        lineNumber: 1131,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                lineNumber: 1123,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                        lineNumber: 1097,\n                        columnNumber: 11\n                    }, undefined),\n                    activeView === \"view\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full animate-in fade-in duration-500 rounded-2xl shadow-sm dark:bg-gray-800 p-1\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ClientSelectPage__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                            permissions: permissions,\n                            client: client\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                            lineNumber: 1192,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                        lineNumber: 1191,\n                        columnNumber: 13\n                    }, undefined) : /* Form Section - Only show when both associate and client are selected */ showFullForm && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_3__.Form, {\n                        ...form,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            onSubmit: form.handleSubmit(onSubmit),\n                            onKeyDown: handleFormKeyDown,\n                            className: \"space-y-3\",\n                            children: [\n                                fields.map((field, index)=>{\n                                    var _missingFields_index;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mb-2 bg-gray-100 rounded-md px-3 py-2 border border-gray-200\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-5 h-5 bg-gray-600 rounded-full flex items-center justify-center text-white font-semibold text-xs\",\n                                                            children: index + 1\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                            lineNumber: 1208,\n                                                            columnNumber: 27\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                            className: \"text-sm font-semibold text-gray-900\",\n                                                            children: [\n                                                                \"Entry #\",\n                                                                index + 1\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                            lineNumber: 1211,\n                                                            columnNumber: 27\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                    lineNumber: 1207,\n                                                    columnNumber: 25\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                lineNumber: 1206,\n                                                columnNumber: 23\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mb-3 pb-3 border-b border-gray-100\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"bg-gray-50 rounded-md p-2 mb-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center space-x-2 mb-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building2_DollarSign_FileText_Hash_Info_MinusCircle_PlusCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                            className: \"w-4 h-4 text-blue-600\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                            lineNumber: 1224,\n                                                                            columnNumber: 31\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                            className: \"text-sm font-semibold text-gray-900\",\n                                                                            children: \"Client Information\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                            lineNumber: 1225,\n                                                                            columnNumber: 31\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                    lineNumber: 1223,\n                                                                    columnNumber: 29\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-2 mb-3\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                                className: \"mt-2\",\n                                                                                form: form,\n                                                                                label: \"FTP File Name\",\n                                                                                name: \"entries.\".concat(index, \".ftpFileName\"),\n                                                                                type: \"text\",\n                                                                                isRequired: true\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                                lineNumber: 1232,\n                                                                                columnNumber: 33\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                            lineNumber: 1231,\n                                                                            columnNumber: 31\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_PageInput__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                            className: \"mt-2\",\n                                                                            form: form,\n                                                                            label: \"FTP Page\",\n                                                                            name: \"entries.\".concat(index, \".ftpPage\"),\n                                                                            isRequired: true\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                            lineNumber: 1241,\n                                                                            columnNumber: 31\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex flex-col\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_SearchSelect__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                                className: \"mt-0\",\n                                                                                form: form,\n                                                                                name: \"entries.\".concat(index, \".carrierName\"),\n                                                                                label: \"Select Carrier\",\n                                                                                placeholder: \"Search Carrier\",\n                                                                                isRequired: true,\n                                                                                options: (carrierOptions === null || carrierOptions === void 0 ? void 0 : carrierOptions.filter((carrier)=>{\n                                                                                    const currentEntries = form.getValues(\"entries\") || [];\n                                                                                    const isSelectedInOtherEntries = currentEntries.some((entry, entryIndex)=>entryIndex !== index && entry.carrierName === carrier.value);\n                                                                                    return !isSelectedInOtherEntries;\n                                                                                })) || [],\n                                                                                onValueChange: ()=>{\n                                                                                    setTimeout(()=>updateFilenames(), 100);\n                                                                                }\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                                lineNumber: 1249,\n                                                                                columnNumber: 33\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                            lineNumber: 1248,\n                                                                            columnNumber: 31\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex flex-col\",\n                                                                            children: (()=>{\n                                                                                var _formValues_entries, _clientOptions_find;\n                                                                                const formValues = form.getValues();\n                                                                                const entry = (_formValues_entries = formValues.entries) === null || _formValues_entries === void 0 ? void 0 : _formValues_entries[index];\n                                                                                const entryClientId = (entry === null || entry === void 0 ? void 0 : entry.clientId) || formValues.clientId || \"\";\n                                                                                const entryClientName = (clientOptions === null || clientOptions === void 0 ? void 0 : (_clientOptions_find = clientOptions.find((c)=>c.value === entryClientId)) === null || _clientOptions_find === void 0 ? void 0 : _clientOptions_find.name) || \"\";\n                                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"mt-6\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                            className: \"text-sm font-medium text-gray-700 mb-1 block\",\n                                                                                            children: [\n                                                                                                \"Billed to \",\n                                                                                                entryClientName || \"Client\"\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                                            lineNumber: 1291,\n                                                                                            columnNumber: 39\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"flex items-center space-x-4\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                                    className: \"flex items-center\",\n                                                                                                    children: [\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                                            type: \"radio\",\n                                                                                                            ...form.register(\"entries.\".concat(index, \".billToClient\")),\n                                                                                                            value: \"yes\",\n                                                                                                            className: \"mr-2\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                                                            lineNumber: 1296,\n                                                                                                            columnNumber: 43\n                                                                                                        }, undefined),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                            className: \"text-sm\",\n                                                                                                            children: \"Yes\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                                                            lineNumber: 1302,\n                                                                                                            columnNumber: 43\n                                                                                                        }, undefined)\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                                                    lineNumber: 1295,\n                                                                                                    columnNumber: 41\n                                                                                                }, undefined),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                                    className: \"flex items-center\",\n                                                                                                    children: [\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                                            type: \"radio\",\n                                                                                                            ...form.register(\"entries.\".concat(index, \".billToClient\")),\n                                                                                                            value: \"no\",\n                                                                                                            className: \"mr-2\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                                                            lineNumber: 1305,\n                                                                                                            columnNumber: 43\n                                                                                                        }, undefined),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                            className: \"text-sm\",\n                                                                                                            children: \"No\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                                                            lineNumber: 1311,\n                                                                                                            columnNumber: 43\n                                                                                                        }, undefined)\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                                                    lineNumber: 1304,\n                                                                                                    columnNumber: 41\n                                                                                                }, undefined)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                                            lineNumber: 1294,\n                                                                                            columnNumber: 39\n                                                                                        }, undefined)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                                    lineNumber: 1290,\n                                                                                    columnNumber: 37\n                                                                                }, undefined);\n                                                                            })()\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                            lineNumber: 1274,\n                                                                            columnNumber: 31\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                    lineNumber: 1230,\n                                                                    columnNumber: 29\n                                                                }, undefined),\n                                                                (()=>{\n                                                                    var _formValues_entries, _clientOptions_find;\n                                                                    const formValues = form.getValues();\n                                                                    const entry = (_formValues_entries = formValues.entries) === null || _formValues_entries === void 0 ? void 0 : _formValues_entries[index];\n                                                                    const entryClientId = (entry === null || entry === void 0 ? void 0 : entry.clientId) || formValues.clientId || \"\";\n                                                                    const entryClientName = (clientOptions === null || clientOptions === void 0 ? void 0 : (_clientOptions_find = clientOptions.find((c)=>c.value === entryClientId)) === null || _clientOptions_find === void 0 ? void 0 : _clientOptions_find.name) || \"\";\n                                                                    return entryClientName === \"LEGRAND\";\n                                                                })() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"mb-3\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"grid grid-cols-1 lg:grid-cols-3 gap-3 mb-3\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_LegrandDetailsComponent__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                                form: form,\n                                                                                entryIndex: index,\n                                                                                onLegrandDataChange: handleLegrandDataChange,\n                                                                                blockTitle: \"Shipper\",\n                                                                                fieldPrefix: \"shipper\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                                lineNumber: 1334,\n                                                                                columnNumber: 35\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_LegrandDetailsComponent__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                                form: form,\n                                                                                entryIndex: index,\n                                                                                onLegrandDataChange: handleLegrandDataChange,\n                                                                                blockTitle: \"Consignee\",\n                                                                                fieldPrefix: \"consignee\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                                lineNumber: 1343,\n                                                                                columnNumber: 35\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_LegrandDetailsComponent__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                                form: form,\n                                                                                entryIndex: index,\n                                                                                onLegrandDataChange: handleLegrandDataChange,\n                                                                                blockTitle: \"Bill-to\",\n                                                                                fieldPrefix: \"billto\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                                lineNumber: 1352,\n                                                                                columnNumber: 35\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1333,\n                                                                        columnNumber: 33\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                    lineNumber: 1332,\n                                                                    columnNumber: 31\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            ref: (el)=>{\n                                                                                companyFieldRefs.current[index] = el;\n                                                                            },\n                                                                            className: \"flex flex-col mb-1 [&_input]:h-10\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                                form: form,\n                                                                                label: \"Company\",\n                                                                                name: \"entries.\".concat(index, \".company\"),\n                                                                                type: \"text\",\n                                                                                disable: (()=>{\n                                                                                    var _formValues_entries, _clientOptions_find;\n                                                                                    const formValues = form.getValues();\n                                                                                    const entry = (_formValues_entries = formValues.entries) === null || _formValues_entries === void 0 ? void 0 : _formValues_entries[index];\n                                                                                    const entryClientId = (entry === null || entry === void 0 ? void 0 : entry.clientId) || formValues.clientId || \"\";\n                                                                                    const entryClientName = (clientOptions === null || clientOptions === void 0 ? void 0 : (_clientOptions_find = clientOptions.find((c)=>c.value === entryClientId)) === null || _clientOptions_find === void 0 ? void 0 : _clientOptions_find.name) || \"\";\n                                                                                    return entryClientName === \"LEGRAND\";\n                                                                                })()\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                                lineNumber: 1373,\n                                                                                columnNumber: 33\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                            lineNumber: 1367,\n                                                                            columnNumber: 31\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex flex-col\",\n                                                                            children: (()=>{\n                                                                                var _formValues_entries, _clientOptions_find, _entries_index;\n                                                                                const formValues = form.getValues();\n                                                                                const entry = (_formValues_entries = formValues.entries) === null || _formValues_entries === void 0 ? void 0 : _formValues_entries[index];\n                                                                                const entryClientId = (entry === null || entry === void 0 ? void 0 : entry.clientId) || formValues.clientId || \"\";\n                                                                                const entryClientName = (clientOptions === null || clientOptions === void 0 ? void 0 : (_clientOptions_find = clientOptions.find((c)=>c.value === entryClientId)) === null || _clientOptions_find === void 0 ? void 0 : _clientOptions_find.name) || \"\";\n                                                                                const isLegrand = entryClientName === \"LEGRAND\";\n                                                                                return isLegrand ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_SearchSelect__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                                    form: form,\n                                                                                    name: \"entries.\".concat(index, \".division\"),\n                                                                                    label: \"Division\",\n                                                                                    placeholder: \"Search Division\",\n                                                                                    disabled: false,\n                                                                                    options: getFilteredDivisionOptions((entries === null || entries === void 0 ? void 0 : (_entries_index = entries[index]) === null || _entries_index === void 0 ? void 0 : _entries_index.company) || \"\", index),\n                                                                                    onValueChange: (value)=>{\n                                                                                        handleManualMatchingAutoFill(index, value);\n                                                                                    }\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                                    lineNumber: 1413,\n                                                                                    columnNumber: 37\n                                                                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                                    form: form,\n                                                                                    label: \"Division\",\n                                                                                    name: \"entries.\".concat(index, \".division\"),\n                                                                                    type: \"text\",\n                                                                                    placeholder: \"Enter Division\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                                    lineNumber: 1431,\n                                                                                    columnNumber: 37\n                                                                                }, undefined);\n                                                                            })()\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                            lineNumber: 1395,\n                                                                            columnNumber: 31\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                    lineNumber: 1366,\n                                                                    columnNumber: 29\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                            lineNumber: 1222,\n                                                            columnNumber: 27\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                        lineNumber: 1220,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mb-3 pb-3 border-b border-gray-100\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2 mb-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building2_DollarSign_FileText_Hash_Info_MinusCircle_PlusCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                        className: \"w-4 h-4 text-orange-600\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1448,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                        className: \"text-sm font-semibold text-gray-900\",\n                                                                        children: \"Document Information\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1449,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                lineNumber: 1447,\n                                                                columnNumber: 27\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"grid grid-cols-1 md:grid-cols-3 gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                        form: form,\n                                                                        label: \"Master Invoice\",\n                                                                        name: \"entries.\".concat(index, \".masterInvoice\"),\n                                                                        type: \"text\",\n                                                                        onBlur: (e)=>{\n                                                                            const masterInvoiceValue = e.target.value;\n                                                                            if (masterInvoiceValue) {\n                                                                                form.setValue(\"entries.\".concat(index, \".invoice\"), masterInvoiceValue);\n                                                                            }\n                                                                        }\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1454,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                        form: form,\n                                                                        label: \"Invoice\",\n                                                                        name: \"entries.\".concat(index, \".invoice\"),\n                                                                        type: \"text\",\n                                                                        isRequired: true\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1469,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                        form: form,\n                                                                        label: \"BOL\",\n                                                                        name: \"entries.\".concat(index, \".bol\"),\n                                                                        type: \"text\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1476,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                lineNumber: 1453,\n                                                                columnNumber: 27\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"grid grid-cols-1 md:grid-cols-3 gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                        form: form,\n                                                                        label: \"Received Date\",\n                                                                        name: \"entries.\".concat(index, \".receivedDate\"),\n                                                                        type: \"text\",\n                                                                        isRequired: true,\n                                                                        placeholder: \"DD/MM/YYYY\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1484,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                        form: form,\n                                                                        label: \"Invoice Date\",\n                                                                        name: \"entries.\".concat(index, \".invoiceDate\"),\n                                                                        type: \"text\",\n                                                                        isRequired: true,\n                                                                        placeholder: \"DD/MM/YYYY\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1492,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                        form: form,\n                                                                        label: \"Shipment Date\",\n                                                                        name: \"entries.\".concat(index, \".shipmentDate\"),\n                                                                        type: \"text\",\n                                                                        placeholder: \"DD/MM/YYYY\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1500,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                lineNumber: 1483,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                        lineNumber: 1446,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mb-4 pb-4 border-b border-gray-100\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2 mb-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building2_DollarSign_FileText_Hash_Info_MinusCircle_PlusCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                        className: \"w-4 h-4 text-green-600\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1513,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                        className: \"text-sm font-semibold text-gray-900\",\n                                                                        children: \"Financial & Shipment\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1514,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                lineNumber: 1512,\n                                                                columnNumber: 27\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                        form: form,\n                                                                        label: \"Invoice Total\",\n                                                                        name: \"entries.\".concat(index, \".invoiceTotal\"),\n                                                                        type: \"number\",\n                                                                        isRequired: true\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1519,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_SearchSelect__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                        form: form,\n                                                                        name: \"entries.\".concat(index, \".currency\"),\n                                                                        label: \"Currency\",\n                                                                        placeholder: \"Search currency\",\n                                                                        isRequired: true,\n                                                                        options: [\n                                                                            {\n                                                                                value: \"USD\",\n                                                                                label: \"USD\"\n                                                                            },\n                                                                            {\n                                                                                value: \"CAD\",\n                                                                                label: \"CAD\"\n                                                                            },\n                                                                            {\n                                                                                value: \"EUR\",\n                                                                                label: \"EUR\"\n                                                                            }\n                                                                        ]\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1526,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                        form: form,\n                                                                        label: \"Savings\",\n                                                                        name: \"entries.\".concat(index, \".savings\"),\n                                                                        type: \"text\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1538,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                        form: form,\n                                                                        label: \"Notes\",\n                                                                        name: \"entries.\".concat(index, \".financialNotes\"),\n                                                                        type: \"text\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1544,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                lineNumber: 1518,\n                                                                columnNumber: 27\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-2 mt-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                        form: form,\n                                                                        label: \"Quantity Shipped\",\n                                                                        name: \"entries.\".concat(index, \".qtyShipped\"),\n                                                                        type: \"number\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1552,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                        form: form,\n                                                                        label: \"Weight Unit\",\n                                                                        name: \"entries.\".concat(index, \".weightUnitName\"),\n                                                                        type: \"text\",\n                                                                        isRequired: true\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1558,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_SearchSelect__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                        form: form,\n                                                                        name: \"entries.\".concat(index, \".invoiceType\"),\n                                                                        label: \"Invoice Type\",\n                                                                        placeholder: \"Search Invoice Type\",\n                                                                        isRequired: true,\n                                                                        options: [\n                                                                            {\n                                                                                value: \"FREIGHT\",\n                                                                                label: \"FREIGHT\"\n                                                                            },\n                                                                            {\n                                                                                value: \"ADDITIONAL\",\n                                                                                label: \"ADDITIONAL\"\n                                                                            },\n                                                                            {\n                                                                                value: \"BALANCED DUE\",\n                                                                                label: \"BALANCED DUE\"\n                                                                            },\n                                                                            {\n                                                                                value: \"CREDIT\",\n                                                                                label: \"CREDIT\"\n                                                                            },\n                                                                            {\n                                                                                value: \"REVISED\",\n                                                                                label: \"REVISED\"\n                                                                            }\n                                                                        ]\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1565,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                        form: form,\n                                                                        label: \"Quantity Billed Text\",\n                                                                        name: \"entries.\".concat(index, \".quantityBilledText\"),\n                                                                        type: \"text\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1582,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                lineNumber: 1551,\n                                                                columnNumber: 27\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-2 mt-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                        form: form,\n                                                                        label: \"Invoice Status\",\n                                                                        name: \"entries.\".concat(index, \".invoiceStatus\"),\n                                                                        type: \"text\",\n                                                                        disable: true\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1590,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {}, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1597,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {}, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1598,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {}, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1599,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                lineNumber: 1589,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                        lineNumber: 1511,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mb-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2 mb-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building2_DollarSign_FileText_Hash_Info_MinusCircle_PlusCircle_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                        className: \"w-4 h-4 text-gray-600\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1606,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                        className: \"text-sm font-semibold text-gray-900\",\n                                                                        children: \"Additional Information\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1607,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                lineNumber: 1605,\n                                                                columnNumber: 27\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-2\",\n                                                                children: [\n                                                                    (()=>{\n                                                                        var _formValues_entries, _clientOptions_find;\n                                                                        const formValues = form.getValues();\n                                                                        const entry = (_formValues_entries = formValues.entries) === null || _formValues_entries === void 0 ? void 0 : _formValues_entries[index];\n                                                                        const entryClientId = (entry === null || entry === void 0 ? void 0 : entry.clientId) || formValues.clientId || \"\";\n                                                                        const entryClientName = (clientOptions === null || clientOptions === void 0 ? void 0 : (_clientOptions_find = clientOptions.find((c)=>c.value === entryClientId)) === null || _clientOptions_find === void 0 ? void 0 : _clientOptions_find.name) || \"\";\n                                                                        const isLegrand = entryClientName === \"LEGRAND\";\n                                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                            form: form,\n                                                                            label: isLegrand ? \"Manual or Matching (Auto-filled)\" : \"Manual or Matching\",\n                                                                            name: \"entries.\".concat(index, \".manualMatching\"),\n                                                                            type: \"text\",\n                                                                            isRequired: true,\n                                                                            disable: isLegrand,\n                                                                            placeholder: isLegrand ? \"Auto-filled based on division\" : \"Enter manual or matching\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                            lineNumber: 1624,\n                                                                            columnNumber: 33\n                                                                        }, undefined);\n                                                                    })(),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                        form: form,\n                                                                        label: \"Notes (Remarks)\",\n                                                                        name: \"entries.\".concat(index, \".notes\"),\n                                                                        type: \"text\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1643,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                        form: form,\n                                                                        label: \"Mistake\",\n                                                                        name: \"entries.\".concat(index, \".mistake\"),\n                                                                        type: \"text\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1649,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormCheckboxGroup__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                            form: form,\n                                                                            label: \"Documents Available\",\n                                                                            name: \"entries.\".concat(index, \".docAvailable\"),\n                                                                            options: [\n                                                                                {\n                                                                                    label: \"Invoice\",\n                                                                                    value: \"Invoice\"\n                                                                                },\n                                                                                {\n                                                                                    label: \"BOL\",\n                                                                                    value: \"Bol\"\n                                                                                },\n                                                                                {\n                                                                                    label: \"POD\",\n                                                                                    value: \"Pod\"\n                                                                                },\n                                                                                {\n                                                                                    label: \"Packages List\",\n                                                                                    value: \"Packages List\"\n                                                                                },\n                                                                                {\n                                                                                    label: \"Other Documents\",\n                                                                                    value: \"Other Documents\"\n                                                                                }\n                                                                            ],\n                                                                            className: \"flex-row gap-2 text-xs\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                            lineNumber: 1656,\n                                                                            columnNumber: 31\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1655,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                lineNumber: 1611,\n                                                                columnNumber: 27\n                                                            }, undefined),\n                                                            (()=>{\n                                                                var _formValues_entries;\n                                                                const formValues = form.getValues();\n                                                                const entry = (_formValues_entries = formValues.entries) === null || _formValues_entries === void 0 ? void 0 : _formValues_entries[index];\n                                                                const docAvailable = (entry === null || entry === void 0 ? void 0 : entry.docAvailable) || [];\n                                                                const hasOtherDocuments = docAvailable.includes(\"Other Documents\");\n                                                                return hasOtherDocuments ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"mt-2\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                        form: form,\n                                                                        label: \"Specify Other Documents\",\n                                                                        name: \"entries.\".concat(index, \".otherDocuments\"),\n                                                                        type: \"text\",\n                                                                        isRequired: true,\n                                                                        placeholder: \"Enter other document types...\",\n                                                                        className: \"max-w-md\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1687,\n                                                                        columnNumber: 33\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                    lineNumber: 1686,\n                                                                    columnNumber: 31\n                                                                }, undefined) : null;\n                                                            })()\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                        lineNumber: 1604,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    (()=>{\n                                                        var _formValues_entries;\n                                                        const formValues = form.getValues();\n                                                        const entry = (_formValues_entries = formValues.entries) === null || _formValues_entries === void 0 ? void 0 : _formValues_entries[index];\n                                                        const customFields = (entry === null || entry === void 0 ? void 0 : entry.customFields) || [];\n                                                        return Array.isArray(customFields) && customFields.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"pt-3 border-t border-gray-100\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center space-x-2 mb-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building2_DollarSign_FileText_Hash_Info_MinusCircle_PlusCircle_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                            className: \"w-4 h-4 text-purple-600\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                            lineNumber: 1714,\n                                                                            columnNumber: 33\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                            className: \"text-sm font-semibold text-gray-900\",\n                                                                            children: [\n                                                                                \"Custom Fields (\",\n                                                                                customFields.length,\n                                                                                \")\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                            lineNumber: 1715,\n                                                                            columnNumber: 33\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                    lineNumber: 1713,\n                                                                    columnNumber: 31\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-2\",\n                                                                    children: customFields.map((cf, cfIdx)=>{\n                                                                        const fieldType = cf.type || \"TEXT\";\n                                                                        const isAutoField = fieldType === \"AUTO\";\n                                                                        const autoOption = cf.autoOption;\n                                                                        let inputType = \"text\";\n                                                                        if (fieldType === \"DATE\" || isAutoField && autoOption === \"DATE\") {\n                                                                            inputType = \"date\";\n                                                                        } else if (fieldType === \"NUMBER\") {\n                                                                            inputType = \"number\";\n                                                                        }\n                                                                        const fieldLabel = isAutoField ? \"\".concat(cf.name, \" (Auto - \").concat(autoOption, \")\") : cf.name;\n                                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                            form: form,\n                                                                            label: fieldLabel,\n                                                                            name: \"entries.\".concat(index, \".customFields.\").concat(cfIdx, \".value\"),\n                                                                            type: inputType,\n                                                                            className: \"w-full\",\n                                                                            disable: isAutoField\n                                                                        }, cf.id, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                            lineNumber: 1740,\n                                                                            columnNumber: 37\n                                                                        }, undefined);\n                                                                    })\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                    lineNumber: 1719,\n                                                                    columnNumber: 31\n                                                                }, undefined)\n                                                            ]\n                                                        }, \"custom-fields-\".concat(index, \"-\").concat(customFieldsRefresh), true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                            lineNumber: 1709,\n                                                            columnNumber: 29\n                                                        }, undefined) : null;\n                                                    })(),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"pt-3 border-t border-gray-100 mt-3\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-end space-x-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_13__.Tooltip, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_13__.TooltipTrigger, {\n                                                                            asChild: true,\n                                                                            tabIndex: -1,\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"w-5 h-5 rounded-full flex items-center justify-center text-white font-bold text-xs cursor-help transition-colors duration-200 \".concat(filenameValidation[index] ? \"bg-green-500 hover:bg-green-600\" : \"bg-orange-500 hover:bg-orange-600\"),\n                                                                                tabIndex: -1,\n                                                                                role: \"button\",\n                                                                                \"aria-label\": \"Entry \".concat(index + 1, \" filename status\"),\n                                                                                children: \"!\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                                lineNumber: 1762,\n                                                                                columnNumber: 33\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                            lineNumber: 1761,\n                                                                            columnNumber: 31\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_13__.TooltipContent, {\n                                                                            side: \"top\",\n                                                                            align: \"center\",\n                                                                            className: \"z-[9999]\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-sm max-w-md\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"font-medium mb-1\",\n                                                                                        children: [\n                                                                                            \"Entry #\",\n                                                                                            index + 1,\n                                                                                            \" Filename\"\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                                        lineNumber: 1783,\n                                                                                        columnNumber: 35\n                                                                                    }, undefined),\n                                                                                    filenameValidation[index] ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                                className: \"font-medium text-green-600 mb-2\",\n                                                                                                children: \"Filename Generated\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                                                lineNumber: 1788,\n                                                                                                columnNumber: 39\n                                                                                            }, undefined),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                                className: \"text-xs font-mono break-all bg-gray-100 p-2 rounded text-black\",\n                                                                                                children: generatedFilenames[index]\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                                                lineNumber: 1791,\n                                                                                                columnNumber: 39\n                                                                                            }, undefined)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                                        lineNumber: 1787,\n                                                                                        columnNumber: 37\n                                                                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                                className: \"font-medium text-orange-600 mb-1\",\n                                                                                                children: \"Please fill the form to generate filename\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                                                lineNumber: 1797,\n                                                                                                columnNumber: 39\n                                                                                            }, undefined),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                                className: \"text-xs text-gray-600 mb-2\",\n                                                                                                children: \"Missing fields:\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                                                lineNumber: 1801,\n                                                                                                columnNumber: 39\n                                                                                            }, undefined),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                                                className: \"list-disc list-inside space-y-1\",\n                                                                                                children: (_missingFields_index = missingFields[index]) === null || _missingFields_index === void 0 ? void 0 : _missingFields_index.map((field, fieldIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                                        className: \"text-xs\",\n                                                                                                        children: field\n                                                                                                    }, fieldIndex, false, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                                                        lineNumber: 1807,\n                                                                                                        columnNumber: 45\n                                                                                                    }, undefined))\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                                                lineNumber: 1804,\n                                                                                                columnNumber: 39\n                                                                                            }, undefined)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                                        lineNumber: 1796,\n                                                                                        columnNumber: 37\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                                lineNumber: 1782,\n                                                                                columnNumber: 33\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                            lineNumber: 1777,\n                                                                            columnNumber: 31\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                    lineNumber: 1760,\n                                                                    columnNumber: 29\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                                                    type: \"button\",\n                                                                    variant: \"outline\",\n                                                                    size: \"sm\",\n                                                                    className: \"h-7 w-7 p-0 hover:bg-red-50 hover:border-red-200\",\n                                                                    onClick: ()=>removeEntry(index),\n                                                                    disabled: fields.length <= 1,\n                                                                    tabIndex: -1,\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building2_DollarSign_FileText_Hash_Info_MinusCircle_PlusCircle_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                        className: \"h-3 w-3 text-red-500\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1831,\n                                                                        columnNumber: 31\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                    lineNumber: 1822,\n                                                                    columnNumber: 29\n                                                                }, undefined),\n                                                                index === fields.length - 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_13__.Tooltip, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_13__.TooltipTrigger, {\n                                                                            asChild: true,\n                                                                            tabIndex: -1,\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                                                                type: \"button\",\n                                                                                variant: \"outline\",\n                                                                                size: \"sm\",\n                                                                                className: \"h-7 w-7 p-0 hover:bg-green-50 hover:border-green-200\",\n                                                                                onClick: addNewEntry,\n                                                                                tabIndex: -1,\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building2_DollarSign_FileText_Hash_Info_MinusCircle_PlusCircle_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                                    className: \"h-3 w-3 text-green-500\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                                    lineNumber: 1844,\n                                                                                    columnNumber: 37\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                                lineNumber: 1836,\n                                                                                columnNumber: 35\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                            lineNumber: 1835,\n                                                                            columnNumber: 33\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_13__.TooltipContent, {\n                                                                            side: \"top\",\n                                                                            align: \"center\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-xs\",\n                                                                                children: \"Add New Entry (Shift+Enter)\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                                lineNumber: 1848,\n                                                                                columnNumber: 35\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                            lineNumber: 1847,\n                                                                            columnNumber: 33\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                    lineNumber: 1834,\n                                                                    columnNumber: 31\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                            lineNumber: 1758,\n                                                            columnNumber: 27\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                        lineNumber: 1757,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                lineNumber: 1218,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        ]\n                                    }, field.id, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                        lineNumber: 1204,\n                                        columnNumber: 21\n                                    }, undefined);\n                                }),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-3\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                            type: \"submit\",\n                                            className: \"px-6 py-2 rounded-lg font-medium transition-all duration-200 shadow-md hover:shadow-lg text-sm\",\n                                            children: \"Save\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                            lineNumber: 1863,\n                                            columnNumber: 23\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                        lineNumber: 1862,\n                                        columnNumber: 21\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                    lineNumber: 1861,\n                                    columnNumber: 19\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                            lineNumber: 1198,\n                            columnNumber: 17\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                        lineNumber: 1197,\n                        columnNumber: 15\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                lineNumber: 1095,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n            lineNumber: 1094,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n        lineNumber: 1093,\n        columnNumber: 5\n    }, undefined);\n};\n_s(CreateTrackSheet, \"uZMLHlCbMWae99cMX7u66tjCGog=\", false, function() {\n    return [\n        react_hook_form__WEBPACK_IMPORTED_MODULE_17__.useForm,\n        next_navigation__WEBPACK_IMPORTED_MODULE_9__.useRouter,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_17__.useForm,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_17__.useWatch,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_17__.useFieldArray\n    ];\n});\n_c = CreateTrackSheet;\n/* harmony default export */ __webpack_exports__[\"default\"] = (CreateTrackSheet); /* eslint-disable */ \nfunction oo_cm() {\n    try {\n        return (0, eval)(\"globalThis._console_ninja\") || (0, eval)(\"/* https://github.com/wallabyjs/console-ninja#how-does-it-work */'use strict';var _0x1d5429=_0x5cbf;function _0x475c(){var _0xe4c209=['_inNextEdge','_connectToHostNow','setter','data','elements','_objectToString','_cleanNode','_treeNodePropertiesAfterFullValue','unshift',[\\\"localhost\\\",\\\"127.0.0.1\\\",\\\"example.cypress.io\\\",\\\"DESKTOP-5O96LAU\\\",\\\"***************\\\"],'array','[object\\\\x20Date]','String','angular','_isPrimitiveType','depth','127.0.0.1','length','HTMLAllCollection','replace','expressionsToEvaluate','args','_maxConnectAttemptCount','timeStamp','_isMap','toString','valueOf','null','_addLoadNode','getPrototypeOf','Buffer','_processTreeNodeResult','_isPrimitiveWrapperType','_getOwnPropertyNames','funcName','hrtime','unref','_addObjectProperty','_isUndefined','Symbol','Console\\\\x20Ninja\\\\x20failed\\\\x20to\\\\x20send\\\\x20logs,\\\\x20refreshing\\\\x20the\\\\x20page\\\\x20may\\\\x20help;\\\\x20also\\\\x20see\\\\x20','global','__es'+'Module','_isSet','_WebSocketClass','nan','env','_HTMLAllCollection','startsWith','23810vNvoaR','_connecting','ws://','autoExpandLimit','stringify','number','forEach','prototype','_regExpToString','catch','reload','_type','value','_undefined','default','WebSocket','undefined','_allowedToSend','Number','_keyStrRegExp','Boolean','https://tinyurl.com/37x8b79t','readyState','_reconnectTimeout','_ws','match','16SRlwRe','substr','_sendErrorMessage','75528lExDnN','perf_hooks','onclose','_isArray','next.js','log','5804608shRRxZ','getWebSocketClass','_sortProps','Set','_Symbol','rootExpression','_connected','[object\\\\x20BigInt]','includes','1313349BJhhAA','defineProperty','noFunctions','send','POSITIVE_INFINITY','push','method','call','_webSocketErrorDocsLink',\\\"c:\\\\\\\\Users\\\\\\\\<USER>\\\\\\\\.vscode\\\\\\\\extensions\\\\\\\\wallabyjs.console-ninja-1.0.451\\\\\\\\node_modules\\\",'totalStrLength','_addFunctionsNode','indexOf','1.0.0','_console_ninja_session','7139xxkPpx','_dateToString','negativeZero','some','constructor','_setNodeExpressionPath','_inBrowser','bind','_disposeWebsocket','current','fromCharCode','boolean','_blacklistedProperty','onerror','expId','versions','reduceLimits','_addProperty','autoExpandMaxDepth','count','parent','hostname','negativeInfinity','\\\\x20browser','3363829QkgWvN','1872226naitmN','autoExpand','_hasMapOnItsPath','_allowedToConnectOnSend','_WebSocket','getOwnPropertyDescriptor','_p_','split','slice','location','5fSmxpa','_consoleNinjaAllowedToStart','Error','_p_length','join','cappedProps','performance','hasOwnProperty','enumerable','_setNodePermissions','trace','3dJqZoJ','node','symbol','pop','close','strLength','_capIfString','getOwnPropertyNames','Map','_isNegativeZero','nodeModules','60267','','eventReceivedCallback','error','_setNodeId','resolveGetters','_attemptToReconnectShortly','_setNodeQueryPath','concat','capped','_getOwnPropertyDescriptor','onopen','charAt','_setNodeExpandableState','1','background:\\\\x20rgb(30,30,30);\\\\x20color:\\\\x20rgb(255,213,92)','_console_ninja','failed\\\\x20to\\\\x20connect\\\\x20to\\\\x20host:\\\\x20','_getOwnPropertySymbols','5139BQVfzY','toLowerCase','%c\\\\x20Console\\\\x20Ninja\\\\x20extension\\\\x20is\\\\x20connected\\\\x20to\\\\x20','logger\\\\x20failed\\\\x20to\\\\x20connect\\\\x20to\\\\x20host','_hasSymbolPropertyOnItsPath','allStrLength','name','_numberRegExp','props','map','url','unknown','edge','[object\\\\x20Array]','_additionalMetadata','_socket','time','toUpperCase','[object\\\\x20Set]','level','_treeNodePropertiesBeforeFullValue','autoExpandPropertyCount','then','process','isExpressionToEvaluate','getter','host','serialize','warn','_setNodeLabel','positiveInfinity','root_exp_id','_hasSetOnItsPath','8942052mrNncS','NEXT_RUNTIME','NEGATIVE_INFINITY','logger\\\\x20websocket\\\\x20error','string','logger\\\\x20failed\\\\x20to\\\\x20connect\\\\x20to\\\\x20host,\\\\x20see\\\\x20','see\\\\x20https://tinyurl.com/2vt8jxzw\\\\x20for\\\\x20more\\\\x20info.','elapsed','autoExpandPreviousObjects','origin','sort','1749180795466','bigint','\\\\x20server','getOwnPropertySymbols','_connectAttemptCount','_property','hits','root_exp','console','object','message','index','path','function','stack','now','pathToFileURL','type','next.js','isArray','sortProps','stackTraceLimit','port','test','dockerizedApp','_extendedWarning','coverage'];_0x475c=function(){return _0xe4c209;};return _0x475c();}(function(_0x357ba3,_0x58b7ad){var _0x4888d7=_0x5cbf,_0x23b466=_0x357ba3();while(!![]){try{var _0x4d2554=-parseInt(_0x4888d7(0x213))/0x1+-parseInt(_0x4888d7(0x23b))/0x2*(-parseInt(_0x4888d7(0x250))/0x3)+parseInt(_0x4888d7(0x20a))/0x4+-parseInt(_0x4888d7(0x245))/0x5*(-parseInt(_0x4888d7(0x190))/0x6)+parseInt(_0x4888d7(0x23a))/0x7*(parseInt(_0x4888d7(0x201))/0x8)+parseInt(_0x4888d7(0x16f))/0x9*(parseInt(_0x4888d7(0x1e7))/0xa)+-parseInt(_0x4888d7(0x222))/0xb*(parseInt(_0x4888d7(0x204))/0xc);if(_0x4d2554===_0x58b7ad)break;else _0x23b466['push'](_0x23b466['shift']());}catch(_0x26ad71){_0x23b466['push'](_0x23b466['shift']());}}}(_0x475c,0xc3561));var G=Object['create'],V=Object[_0x1d5429(0x214)],ee=Object[_0x1d5429(0x240)],te=Object['getOwnPropertyNames'],ne=Object[_0x1d5429(0x1d3)],re=Object[_0x1d5429(0x1ee)][_0x1d5429(0x24c)],ie=(_0x4be056,_0x22ec44,_0x565f40,_0x3427ea)=>{var _0x1b5108=_0x1d5429;if(_0x22ec44&&typeof _0x22ec44==_0x1b5108(0x1a4)||typeof _0x22ec44==_0x1b5108(0x1a8)){for(let _0x46cca5 of te(_0x22ec44))!re[_0x1b5108(0x21a)](_0x4be056,_0x46cca5)&&_0x46cca5!==_0x565f40&&V(_0x4be056,_0x46cca5,{'get':()=>_0x22ec44[_0x46cca5],'enumerable':!(_0x3427ea=ee(_0x22ec44,_0x46cca5))||_0x3427ea[_0x1b5108(0x24d)]});}return _0x4be056;},j=(_0x305dec,_0x1bc176,_0x30a70f)=>(_0x30a70f=_0x305dec!=null?G(ne(_0x305dec)):{},ie(_0x1bc176||!_0x305dec||!_0x305dec[_0x1d5429(0x1e0)]?V(_0x30a70f,'default',{'value':_0x305dec,'enumerable':!0x0}):_0x30a70f,_0x305dec)),q=class{constructor(_0x3d1a71,_0x4d9b91,_0x442325,_0x1088d0,_0x1cd5f7,_0x5ba3cc){var _0x45f415=_0x1d5429,_0x1afb0b,_0x219236,_0x3a3e48,_0x2a9c0a;this[_0x45f415(0x1df)]=_0x3d1a71,this[_0x45f415(0x189)]=_0x4d9b91,this['port']=_0x442325,this['nodeModules']=_0x1088d0,this[_0x45f415(0x1b3)]=_0x1cd5f7,this[_0x45f415(0x25d)]=_0x5ba3cc,this[_0x45f415(0x1f8)]=!0x0,this[_0x45f415(0x23e)]=!0x0,this[_0x45f415(0x210)]=!0x1,this[_0x45f415(0x1e8)]=!0x1,this['_inNextEdge']=((_0x219236=(_0x1afb0b=_0x3d1a71[_0x45f415(0x186)])==null?void 0x0:_0x1afb0b[_0x45f415(0x1e4)])==null?void 0x0:_0x219236[_0x45f415(0x191)])==='edge',this[_0x45f415(0x228)]=!((_0x2a9c0a=(_0x3a3e48=this[_0x45f415(0x1df)]['process'])==null?void 0x0:_0x3a3e48[_0x45f415(0x231)])!=null&&_0x2a9c0a[_0x45f415(0x251)])&&!this[_0x45f415(0x1b6)],this[_0x45f415(0x1e2)]=null,this[_0x45f415(0x19f)]=0x0,this[_0x45f415(0x1cc)]=0x14,this[_0x45f415(0x21b)]=_0x45f415(0x1fc),this[_0x45f415(0x203)]=(this[_0x45f415(0x228)]?_0x45f415(0x1de):'Console\\\\x20Ninja\\\\x20failed\\\\x20to\\\\x20send\\\\x20logs,\\\\x20restarting\\\\x20the\\\\x20process\\\\x20may\\\\x20help;\\\\x20also\\\\x20see\\\\x20')+this[_0x45f415(0x21b)];}async[_0x1d5429(0x20b)](){var _0x371d88=_0x1d5429,_0x78e02c,_0x4b140f;if(this[_0x371d88(0x1e2)])return this[_0x371d88(0x1e2)];let _0x2f37bd;if(this[_0x371d88(0x228)]||this['_inNextEdge'])_0x2f37bd=this[_0x371d88(0x1df)][_0x371d88(0x1f6)];else{if((_0x78e02c=this[_0x371d88(0x1df)][_0x371d88(0x186)])!=null&&_0x78e02c[_0x371d88(0x23f)])_0x2f37bd=(_0x4b140f=this[_0x371d88(0x1df)]['process'])==null?void 0x0:_0x4b140f[_0x371d88(0x23f)];else try{let _0x26a01e=await import('path');_0x2f37bd=(await import((await import(_0x371d88(0x179)))[_0x371d88(0x1ab)](_0x26a01e[_0x371d88(0x249)](this[_0x371d88(0x25a)],'ws/index.js'))['toString']()))[_0x371d88(0x1f5)];}catch{try{_0x2f37bd=require(require(_0x371d88(0x1a7))[_0x371d88(0x249)](this[_0x371d88(0x25a)],'ws'));}catch{throw new Error('failed\\\\x20to\\\\x20find\\\\x20and\\\\x20load\\\\x20WebSocket');}}}return this[_0x371d88(0x1e2)]=_0x2f37bd,_0x2f37bd;}[_0x1d5429(0x1b7)](){var _0x303ad0=_0x1d5429;this[_0x303ad0(0x1e8)]||this[_0x303ad0(0x210)]||this[_0x303ad0(0x19f)]>=this[_0x303ad0(0x1cc)]||(this['_allowedToConnectOnSend']=!0x1,this[_0x303ad0(0x1e8)]=!0x0,this[_0x303ad0(0x19f)]++,this[_0x303ad0(0x1ff)]=new Promise((_0x411466,_0x249636)=>{var _0x5820e2=_0x303ad0;this[_0x5820e2(0x20b)]()[_0x5820e2(0x185)](_0x189850=>{var _0x54d133=_0x5820e2;let _0x1f39b6=new _0x189850(_0x54d133(0x1e9)+(!this[_0x54d133(0x228)]&&this[_0x54d133(0x1b3)]?'gateway.docker.internal':this[_0x54d133(0x189)])+':'+this[_0x54d133(0x1b1)]);_0x1f39b6[_0x54d133(0x22f)]=()=>{var _0x1182b6=_0x54d133;this[_0x1182b6(0x1f8)]=!0x1,this[_0x1182b6(0x22a)](_0x1f39b6),this[_0x1182b6(0x261)](),_0x249636(new Error(_0x1182b6(0x193)));},_0x1f39b6[_0x54d133(0x266)]=()=>{var _0x344dde=_0x54d133;this[_0x344dde(0x228)]||_0x1f39b6[_0x344dde(0x17e)]&&_0x1f39b6['_socket']['unref']&&_0x1f39b6['_socket'][_0x344dde(0x1da)](),_0x411466(_0x1f39b6);},_0x1f39b6[_0x54d133(0x206)]=()=>{var _0x195966=_0x54d133;this['_allowedToConnectOnSend']=!0x0,this[_0x195966(0x22a)](_0x1f39b6),this[_0x195966(0x261)]();},_0x1f39b6['onmessage']=_0x54cab8=>{var _0x570663=_0x54d133;try{if(!(_0x54cab8!=null&&_0x54cab8[_0x570663(0x1b9)])||!this['eventReceivedCallback'])return;let _0x596d55=JSON['parse'](_0x54cab8[_0x570663(0x1b9)]);this[_0x570663(0x25d)](_0x596d55[_0x570663(0x219)],_0x596d55[_0x570663(0x1cb)],this[_0x570663(0x1df)],this[_0x570663(0x228)]);}catch{}};})['then'](_0x4e8240=>(this['_connected']=!0x0,this[_0x5820e2(0x1e8)]=!0x1,this[_0x5820e2(0x23e)]=!0x1,this['_allowedToSend']=!0x0,this[_0x5820e2(0x19f)]=0x0,_0x4e8240))[_0x5820e2(0x1f0)](_0x487a4f=>(this[_0x5820e2(0x210)]=!0x1,this[_0x5820e2(0x1e8)]=!0x1,console[_0x5820e2(0x18b)](_0x5820e2(0x195)+this['_webSocketErrorDocsLink']),_0x249636(new Error(_0x5820e2(0x26c)+(_0x487a4f&&_0x487a4f[_0x5820e2(0x1a5)])))));}));}['_disposeWebsocket'](_0x1da141){var _0x5e8253=_0x1d5429;this[_0x5e8253(0x210)]=!0x1,this['_connecting']=!0x1;try{_0x1da141[_0x5e8253(0x206)]=null,_0x1da141[_0x5e8253(0x22f)]=null,_0x1da141[_0x5e8253(0x266)]=null;}catch{}try{_0x1da141[_0x5e8253(0x1fd)]<0x2&&_0x1da141[_0x5e8253(0x254)]();}catch{}}['_attemptToReconnectShortly'](){var _0x30e327=_0x1d5429;clearTimeout(this[_0x30e327(0x1fe)]),!(this[_0x30e327(0x19f)]>=this[_0x30e327(0x1cc)])&&(this[_0x30e327(0x1fe)]=setTimeout(()=>{var _0x33b706=_0x30e327,_0x132147;this['_connected']||this['_connecting']||(this['_connectToHostNow'](),(_0x132147=this[_0x33b706(0x1ff)])==null||_0x132147[_0x33b706(0x1f0)](()=>this[_0x33b706(0x261)]()));},0x1f4),this['_reconnectTimeout'][_0x30e327(0x1da)]&&this[_0x30e327(0x1fe)][_0x30e327(0x1da)]());}async[_0x1d5429(0x216)](_0x10bb38){var _0x5bfa1d=_0x1d5429;try{if(!this['_allowedToSend'])return;this[_0x5bfa1d(0x23e)]&&this[_0x5bfa1d(0x1b7)](),(await this[_0x5bfa1d(0x1ff)])['send'](JSON[_0x5bfa1d(0x1eb)](_0x10bb38));}catch(_0x1057db){this[_0x5bfa1d(0x1b4)]?console[_0x5bfa1d(0x18b)](this[_0x5bfa1d(0x203)]+':\\\\x20'+(_0x1057db&&_0x1057db[_0x5bfa1d(0x1a5)])):(this['_extendedWarning']=!0x0,console[_0x5bfa1d(0x18b)](this[_0x5bfa1d(0x203)]+':\\\\x20'+(_0x1057db&&_0x1057db[_0x5bfa1d(0x1a5)]),_0x10bb38)),this[_0x5bfa1d(0x1f8)]=!0x1,this[_0x5bfa1d(0x261)]();}}};function H(_0x44ea79,_0xd43057,_0x3e9dc0,_0x23338b,_0x171a49,_0x56392f,_0xc9ec8d,_0x46d8af=oe){var _0x5ef3a=_0x1d5429;let _0x2af8b7=_0x3e9dc0[_0x5ef3a(0x242)](',')[_0x5ef3a(0x178)](_0x3a0a9f=>{var _0x22dfc1=_0x5ef3a,_0x4e8400,_0xd48d75,_0x332614,_0x935bb8;try{if(!_0x44ea79[_0x22dfc1(0x221)]){let _0x58f21b=((_0xd48d75=(_0x4e8400=_0x44ea79['process'])==null?void 0x0:_0x4e8400['versions'])==null?void 0x0:_0xd48d75[_0x22dfc1(0x251)])||((_0x935bb8=(_0x332614=_0x44ea79[_0x22dfc1(0x186)])==null?void 0x0:_0x332614[_0x22dfc1(0x1e4)])==null?void 0x0:_0x935bb8[_0x22dfc1(0x191)])===_0x22dfc1(0x17b);(_0x171a49===_0x22dfc1(0x208)||_0x171a49==='remix'||_0x171a49==='astro'||_0x171a49===_0x22dfc1(0x1c3))&&(_0x171a49+=_0x58f21b?_0x22dfc1(0x19d):_0x22dfc1(0x239)),_0x44ea79[_0x22dfc1(0x221)]={'id':+new Date(),'tool':_0x171a49},_0xc9ec8d&&_0x171a49&&!_0x58f21b&&console['log'](_0x22dfc1(0x171)+(_0x171a49[_0x22dfc1(0x267)](0x0)[_0x22dfc1(0x180)]()+_0x171a49['substr'](0x1))+',',_0x22dfc1(0x26a),_0x22dfc1(0x196));}let _0x47c983=new q(_0x44ea79,_0xd43057,_0x3a0a9f,_0x23338b,_0x56392f,_0x46d8af);return _0x47c983[_0x22dfc1(0x216)][_0x22dfc1(0x229)](_0x47c983);}catch(_0x5d33f6){return console[_0x22dfc1(0x18b)](_0x22dfc1(0x172),_0x5d33f6&&_0x5d33f6[_0x22dfc1(0x1a5)]),()=>{};}});return _0x56f5a8=>_0x2af8b7['forEach'](_0x297f71=>_0x297f71(_0x56f5a8));}function oe(_0x4f3d7f,_0x1f257d,_0x4e6ff4,_0x2a6d0d){var _0x57f7d6=_0x1d5429;_0x2a6d0d&&_0x4f3d7f===_0x57f7d6(0x1f1)&&_0x4e6ff4[_0x57f7d6(0x244)][_0x57f7d6(0x1f1)]();}function B(_0x23c635){var _0x2ecf3b=_0x1d5429,_0x372a90,_0x1cb96d;let _0x399e47=function(_0xb3a49d,_0x5f0736){return _0x5f0736-_0xb3a49d;},_0x11adc7;if(_0x23c635[_0x2ecf3b(0x24b)])_0x11adc7=function(){var _0x3634b5=_0x2ecf3b;return _0x23c635[_0x3634b5(0x24b)]['now']();};else{if(_0x23c635['process']&&_0x23c635[_0x2ecf3b(0x186)][_0x2ecf3b(0x1d9)]&&((_0x1cb96d=(_0x372a90=_0x23c635[_0x2ecf3b(0x186)])==null?void 0x0:_0x372a90[_0x2ecf3b(0x1e4)])==null?void 0x0:_0x1cb96d[_0x2ecf3b(0x191)])!==_0x2ecf3b(0x17b))_0x11adc7=function(){var _0x463427=_0x2ecf3b;return _0x23c635['process'][_0x463427(0x1d9)]();},_0x399e47=function(_0x20c94a,_0x4c6ab8){return 0x3e8*(_0x4c6ab8[0x0]-_0x20c94a[0x0])+(_0x4c6ab8[0x1]-_0x20c94a[0x1])/0xf4240;};else try{let {performance:_0x453fa4}=require(_0x2ecf3b(0x205));_0x11adc7=function(){var _0x467eee=_0x2ecf3b;return _0x453fa4[_0x467eee(0x1aa)]();};}catch{_0x11adc7=function(){return+new Date();};}}return{'elapsed':_0x399e47,'timeStamp':_0x11adc7,'now':()=>Date[_0x2ecf3b(0x1aa)]()};}function _0x5cbf(_0x575035,_0x5f0c68){var _0x475c25=_0x475c();return _0x5cbf=function(_0x5cbf0f,_0xd8a2d8){_0x5cbf0f=_0x5cbf0f-0x16f;var _0x5e9285=_0x475c25[_0x5cbf0f];return _0x5e9285;},_0x5cbf(_0x575035,_0x5f0c68);}function X(_0x1c40e6,_0x4af79b,_0x9ab4a8){var _0x140f52=_0x1d5429,_0xa76f5e,_0x26af55,_0x553b98,_0x414f6c,_0x5611c5;if(_0x1c40e6[_0x140f52(0x246)]!==void 0x0)return _0x1c40e6[_0x140f52(0x246)];let _0x5ed8e8=((_0x26af55=(_0xa76f5e=_0x1c40e6[_0x140f52(0x186)])==null?void 0x0:_0xa76f5e[_0x140f52(0x231)])==null?void 0x0:_0x26af55[_0x140f52(0x251)])||((_0x414f6c=(_0x553b98=_0x1c40e6[_0x140f52(0x186)])==null?void 0x0:_0x553b98[_0x140f52(0x1e4)])==null?void 0x0:_0x414f6c['NEXT_RUNTIME'])===_0x140f52(0x17b);function _0x3eb4b4(_0x7081ba){var _0xa69acc=_0x140f52;if(_0x7081ba[_0xa69acc(0x1e6)]('/')&&_0x7081ba['endsWith']('/')){let _0x1d73bb=new RegExp(_0x7081ba[_0xa69acc(0x243)](0x1,-0x1));return _0x305251=>_0x1d73bb['test'](_0x305251);}else{if(_0x7081ba[_0xa69acc(0x212)]('*')||_0x7081ba[_0xa69acc(0x212)]('?')){let _0x5ccd3b=new RegExp('^'+_0x7081ba[_0xa69acc(0x1c9)](/\\\\./g,String['fromCharCode'](0x5c)+'.')['replace'](/\\\\*/g,'.*')[_0xa69acc(0x1c9)](/\\\\?/g,'.')+String[_0xa69acc(0x22c)](0x24));return _0xd56ec3=>_0x5ccd3b[_0xa69acc(0x1b2)](_0xd56ec3);}else return _0x10c897=>_0x10c897===_0x7081ba;}}let _0x27a499=_0x4af79b[_0x140f52(0x178)](_0x3eb4b4);return _0x1c40e6[_0x140f52(0x246)]=_0x5ed8e8||!_0x4af79b,!_0x1c40e6[_0x140f52(0x246)]&&((_0x5611c5=_0x1c40e6[_0x140f52(0x244)])==null?void 0x0:_0x5611c5[_0x140f52(0x237)])&&(_0x1c40e6['_consoleNinjaAllowedToStart']=_0x27a499[_0x140f52(0x225)](_0x1f9f75=>_0x1f9f75(_0x1c40e6[_0x140f52(0x244)][_0x140f52(0x237)]))),_0x1c40e6[_0x140f52(0x246)];}function J(_0x408b28,_0x2a09fc,_0x1d4002,_0x29575b){var _0x24925f=_0x1d5429;_0x408b28=_0x408b28,_0x2a09fc=_0x2a09fc,_0x1d4002=_0x1d4002,_0x29575b=_0x29575b;let _0x75ca3b=B(_0x408b28),_0x27f964=_0x75ca3b[_0x24925f(0x197)],_0x57fcdb=_0x75ca3b[_0x24925f(0x1cd)];class _0x16dd22{constructor(){var _0x387736=_0x24925f;this[_0x387736(0x1fa)]=/^(?!(?:do|if|in|for|let|new|try|var|case|else|enum|eval|false|null|this|true|void|with|break|catch|class|const|super|throw|while|yield|delete|export|import|public|return|static|switch|typeof|default|extends|finally|package|private|continue|debugger|function|arguments|interface|protected|implements|instanceof)$)[_$a-zA-Z\\\\xA0-\\\\uFFFF][_$a-zA-Z0-9\\\\xA0-\\\\uFFFF]*$/,this[_0x387736(0x176)]=/^(0|[1-9][0-9]*)$/,this['_quotedRegExp']=/'([^\\\\\\\\']|\\\\\\\\')*'/,this[_0x387736(0x1f4)]=_0x408b28[_0x387736(0x1f7)],this['_HTMLAllCollection']=_0x408b28[_0x387736(0x1c8)],this[_0x387736(0x265)]=Object[_0x387736(0x240)],this[_0x387736(0x1d7)]=Object[_0x387736(0x257)],this['_Symbol']=_0x408b28[_0x387736(0x1dd)],this[_0x387736(0x1ef)]=RegExp[_0x387736(0x1ee)][_0x387736(0x1cf)],this[_0x387736(0x223)]=Date['prototype']['toString'];}[_0x24925f(0x18a)](_0x318365,_0x16ae1f,_0x494e4c,_0x500ee1){var _0x3e110b=_0x24925f,_0x532cde=this,_0xa223e=_0x494e4c[_0x3e110b(0x23c)];function _0x36573b(_0x9c2496,_0x13922e,_0x3050a8){var _0x3dd2f9=_0x3e110b;_0x13922e['type']=_0x3dd2f9(0x17a),_0x13922e[_0x3dd2f9(0x25e)]=_0x9c2496['message'],_0x4ad13e=_0x3050a8[_0x3dd2f9(0x251)][_0x3dd2f9(0x22b)],_0x3050a8['node']['current']=_0x13922e,_0x532cde[_0x3dd2f9(0x183)](_0x13922e,_0x3050a8);}let _0x23a8e7;_0x408b28[_0x3e110b(0x1a3)]&&(_0x23a8e7=_0x408b28[_0x3e110b(0x1a3)][_0x3e110b(0x25e)],_0x23a8e7&&(_0x408b28[_0x3e110b(0x1a3)][_0x3e110b(0x25e)]=function(){}));try{try{_0x494e4c[_0x3e110b(0x182)]++,_0x494e4c[_0x3e110b(0x23c)]&&_0x494e4c[_0x3e110b(0x198)]['push'](_0x16ae1f);var _0x43899a,_0x20fafe,_0x2c4a78,_0x25ee5e,_0x5b14ea=[],_0x24ef3a=[],_0x3d7e78,_0x3dfa80=this[_0x3e110b(0x1f2)](_0x16ae1f),_0x4f5cb9=_0x3dfa80==='array',_0x5911ee=!0x1,_0x4414d3=_0x3dfa80==='function',_0x40a892=this[_0x3e110b(0x1c4)](_0x3dfa80),_0x5442a9=this['_isPrimitiveWrapperType'](_0x3dfa80),_0x3891cd=_0x40a892||_0x5442a9,_0x492d66={},_0x263fd3=0x0,_0x460566=!0x1,_0x4ad13e,_0x454869=/^(([1-9]{1}[0-9]*)|0)$/;if(_0x494e4c[_0x3e110b(0x1c5)]){if(_0x4f5cb9){if(_0x20fafe=_0x16ae1f[_0x3e110b(0x1c7)],_0x20fafe>_0x494e4c[_0x3e110b(0x1ba)]){for(_0x2c4a78=0x0,_0x25ee5e=_0x494e4c[_0x3e110b(0x1ba)],_0x43899a=_0x2c4a78;_0x43899a<_0x25ee5e;_0x43899a++)_0x24ef3a[_0x3e110b(0x218)](_0x532cde[_0x3e110b(0x233)](_0x5b14ea,_0x16ae1f,_0x3dfa80,_0x43899a,_0x494e4c));_0x318365['cappedElements']=!0x0;}else{for(_0x2c4a78=0x0,_0x25ee5e=_0x20fafe,_0x43899a=_0x2c4a78;_0x43899a<_0x25ee5e;_0x43899a++)_0x24ef3a['push'](_0x532cde[_0x3e110b(0x233)](_0x5b14ea,_0x16ae1f,_0x3dfa80,_0x43899a,_0x494e4c));}_0x494e4c[_0x3e110b(0x184)]+=_0x24ef3a['length'];}if(!(_0x3dfa80===_0x3e110b(0x1d1)||_0x3dfa80===_0x3e110b(0x1f7))&&!_0x40a892&&_0x3dfa80!==_0x3e110b(0x1c2)&&_0x3dfa80!==_0x3e110b(0x1d4)&&_0x3dfa80!==_0x3e110b(0x19c)){var _0xb39856=_0x500ee1[_0x3e110b(0x177)]||_0x494e4c[_0x3e110b(0x177)];if(this['_isSet'](_0x16ae1f)?(_0x43899a=0x0,_0x16ae1f[_0x3e110b(0x1ed)](function(_0x35412a){var _0x11cac7=_0x3e110b;if(_0x263fd3++,_0x494e4c[_0x11cac7(0x184)]++,_0x263fd3>_0xb39856){_0x460566=!0x0;return;}if(!_0x494e4c[_0x11cac7(0x187)]&&_0x494e4c[_0x11cac7(0x23c)]&&_0x494e4c[_0x11cac7(0x184)]>_0x494e4c['autoExpandLimit']){_0x460566=!0x0;return;}_0x24ef3a[_0x11cac7(0x218)](_0x532cde['_addProperty'](_0x5b14ea,_0x16ae1f,_0x11cac7(0x20d),_0x43899a++,_0x494e4c,function(_0x116481){return function(){return _0x116481;};}(_0x35412a)));})):this['_isMap'](_0x16ae1f)&&_0x16ae1f[_0x3e110b(0x1ed)](function(_0x3ffba7,_0x1786da){var _0x218e16=_0x3e110b;if(_0x263fd3++,_0x494e4c['autoExpandPropertyCount']++,_0x263fd3>_0xb39856){_0x460566=!0x0;return;}if(!_0x494e4c[_0x218e16(0x187)]&&_0x494e4c[_0x218e16(0x23c)]&&_0x494e4c['autoExpandPropertyCount']>_0x494e4c[_0x218e16(0x1ea)]){_0x460566=!0x0;return;}var _0x2d61b9=_0x1786da[_0x218e16(0x1cf)]();_0x2d61b9['length']>0x64&&(_0x2d61b9=_0x2d61b9[_0x218e16(0x243)](0x0,0x64)+'...'),_0x24ef3a[_0x218e16(0x218)](_0x532cde['_addProperty'](_0x5b14ea,_0x16ae1f,_0x218e16(0x258),_0x2d61b9,_0x494e4c,function(_0x7070f7){return function(){return _0x7070f7;};}(_0x3ffba7)));}),!_0x5911ee){try{for(_0x3d7e78 in _0x16ae1f)if(!(_0x4f5cb9&&_0x454869[_0x3e110b(0x1b2)](_0x3d7e78))&&!this['_blacklistedProperty'](_0x16ae1f,_0x3d7e78,_0x494e4c)){if(_0x263fd3++,_0x494e4c[_0x3e110b(0x184)]++,_0x263fd3>_0xb39856){_0x460566=!0x0;break;}if(!_0x494e4c['isExpressionToEvaluate']&&_0x494e4c[_0x3e110b(0x23c)]&&_0x494e4c[_0x3e110b(0x184)]>_0x494e4c[_0x3e110b(0x1ea)]){_0x460566=!0x0;break;}_0x24ef3a['push'](_0x532cde[_0x3e110b(0x1db)](_0x5b14ea,_0x492d66,_0x16ae1f,_0x3dfa80,_0x3d7e78,_0x494e4c));}}catch{}if(_0x492d66[_0x3e110b(0x248)]=!0x0,_0x4414d3&&(_0x492d66['_p_name']=!0x0),!_0x460566){var _0x4ef07b=[][_0x3e110b(0x263)](this[_0x3e110b(0x1d7)](_0x16ae1f))[_0x3e110b(0x263)](this[_0x3e110b(0x26d)](_0x16ae1f));for(_0x43899a=0x0,_0x20fafe=_0x4ef07b[_0x3e110b(0x1c7)];_0x43899a<_0x20fafe;_0x43899a++)if(_0x3d7e78=_0x4ef07b[_0x43899a],!(_0x4f5cb9&&_0x454869['test'](_0x3d7e78['toString']()))&&!this[_0x3e110b(0x22e)](_0x16ae1f,_0x3d7e78,_0x494e4c)&&!_0x492d66['_p_'+_0x3d7e78[_0x3e110b(0x1cf)]()]){if(_0x263fd3++,_0x494e4c[_0x3e110b(0x184)]++,_0x263fd3>_0xb39856){_0x460566=!0x0;break;}if(!_0x494e4c[_0x3e110b(0x187)]&&_0x494e4c[_0x3e110b(0x23c)]&&_0x494e4c[_0x3e110b(0x184)]>_0x494e4c[_0x3e110b(0x1ea)]){_0x460566=!0x0;break;}_0x24ef3a[_0x3e110b(0x218)](_0x532cde['_addObjectProperty'](_0x5b14ea,_0x492d66,_0x16ae1f,_0x3dfa80,_0x3d7e78,_0x494e4c));}}}}}if(_0x318365[_0x3e110b(0x1ac)]=_0x3dfa80,_0x3891cd?(_0x318365[_0x3e110b(0x1f3)]=_0x16ae1f[_0x3e110b(0x1d0)](),this[_0x3e110b(0x256)](_0x3dfa80,_0x318365,_0x494e4c,_0x500ee1)):_0x3dfa80==='date'?_0x318365[_0x3e110b(0x1f3)]=this['_dateToString']['call'](_0x16ae1f):_0x3dfa80===_0x3e110b(0x19c)?_0x318365[_0x3e110b(0x1f3)]=_0x16ae1f[_0x3e110b(0x1cf)]():_0x3dfa80==='RegExp'?_0x318365[_0x3e110b(0x1f3)]=this[_0x3e110b(0x1ef)]['call'](_0x16ae1f):_0x3dfa80===_0x3e110b(0x252)&&this[_0x3e110b(0x20e)]?_0x318365[_0x3e110b(0x1f3)]=this[_0x3e110b(0x20e)][_0x3e110b(0x1ee)][_0x3e110b(0x1cf)][_0x3e110b(0x21a)](_0x16ae1f):!_0x494e4c['depth']&&!(_0x3dfa80===_0x3e110b(0x1d1)||_0x3dfa80==='undefined')&&(delete _0x318365[_0x3e110b(0x1f3)],_0x318365[_0x3e110b(0x264)]=!0x0),_0x460566&&(_0x318365[_0x3e110b(0x24a)]=!0x0),_0x4ad13e=_0x494e4c[_0x3e110b(0x251)]['current'],_0x494e4c[_0x3e110b(0x251)]['current']=_0x318365,this['_treeNodePropertiesBeforeFullValue'](_0x318365,_0x494e4c),_0x24ef3a[_0x3e110b(0x1c7)]){for(_0x43899a=0x0,_0x20fafe=_0x24ef3a['length'];_0x43899a<_0x20fafe;_0x43899a++)_0x24ef3a[_0x43899a](_0x43899a);}_0x5b14ea[_0x3e110b(0x1c7)]&&(_0x318365[_0x3e110b(0x177)]=_0x5b14ea);}catch(_0x471c84){_0x36573b(_0x471c84,_0x318365,_0x494e4c);}this[_0x3e110b(0x17d)](_0x16ae1f,_0x318365),this[_0x3e110b(0x1bd)](_0x318365,_0x494e4c),_0x494e4c['node']['current']=_0x4ad13e,_0x494e4c['level']--,_0x494e4c[_0x3e110b(0x23c)]=_0xa223e,_0x494e4c[_0x3e110b(0x23c)]&&_0x494e4c[_0x3e110b(0x198)][_0x3e110b(0x253)]();}finally{_0x23a8e7&&(_0x408b28[_0x3e110b(0x1a3)][_0x3e110b(0x25e)]=_0x23a8e7);}return _0x318365;}[_0x24925f(0x26d)](_0x157e72){var _0x2435fa=_0x24925f;return Object[_0x2435fa(0x19e)]?Object[_0x2435fa(0x19e)](_0x157e72):[];}[_0x24925f(0x1e1)](_0xf0ffd6){var _0x4adaed=_0x24925f;return!!(_0xf0ffd6&&_0x408b28[_0x4adaed(0x20d)]&&this['_objectToString'](_0xf0ffd6)===_0x4adaed(0x181)&&_0xf0ffd6[_0x4adaed(0x1ed)]);}[_0x24925f(0x22e)](_0x147e87,_0x6e19fc,_0x1075d9){return _0x1075d9['noFunctions']?typeof _0x147e87[_0x6e19fc]=='function':!0x1;}[_0x24925f(0x1f2)](_0x67eb47){var _0x133cb4=_0x24925f,_0x3f733f='';return _0x3f733f=typeof _0x67eb47,_0x3f733f===_0x133cb4(0x1a4)?this[_0x133cb4(0x1bb)](_0x67eb47)===_0x133cb4(0x17c)?_0x3f733f=_0x133cb4(0x1c0):this[_0x133cb4(0x1bb)](_0x67eb47)===_0x133cb4(0x1c1)?_0x3f733f='date':this['_objectToString'](_0x67eb47)===_0x133cb4(0x211)?_0x3f733f=_0x133cb4(0x19c):_0x67eb47===null?_0x3f733f=_0x133cb4(0x1d1):_0x67eb47[_0x133cb4(0x226)]&&(_0x3f733f=_0x67eb47[_0x133cb4(0x226)][_0x133cb4(0x175)]||_0x3f733f):_0x3f733f===_0x133cb4(0x1f7)&&this['_HTMLAllCollection']&&_0x67eb47 instanceof this[_0x133cb4(0x1e5)]&&(_0x3f733f=_0x133cb4(0x1c8)),_0x3f733f;}[_0x24925f(0x1bb)](_0x52879e){var _0x5077a4=_0x24925f;return Object[_0x5077a4(0x1ee)][_0x5077a4(0x1cf)][_0x5077a4(0x21a)](_0x52879e);}[_0x24925f(0x1c4)](_0xdbf8f7){var _0x543e92=_0x24925f;return _0xdbf8f7===_0x543e92(0x22d)||_0xdbf8f7==='string'||_0xdbf8f7===_0x543e92(0x1ec);}[_0x24925f(0x1d6)](_0x4839f4){var _0x462430=_0x24925f;return _0x4839f4===_0x462430(0x1fb)||_0x4839f4==='String'||_0x4839f4===_0x462430(0x1f9);}[_0x24925f(0x233)](_0x23c9a2,_0x43b7ac,_0x5ca049,_0x46aa6e,_0x58a005,_0xd9769f){var _0x2a4052=this;return function(_0x16e0f4){var _0x372a2d=_0x5cbf,_0x460470=_0x58a005[_0x372a2d(0x251)][_0x372a2d(0x22b)],_0x16546a=_0x58a005[_0x372a2d(0x251)][_0x372a2d(0x1a6)],_0x2534ca=_0x58a005[_0x372a2d(0x251)][_0x372a2d(0x236)];_0x58a005[_0x372a2d(0x251)]['parent']=_0x460470,_0x58a005['node'][_0x372a2d(0x1a6)]=typeof _0x46aa6e==_0x372a2d(0x1ec)?_0x46aa6e:_0x16e0f4,_0x23c9a2[_0x372a2d(0x218)](_0x2a4052[_0x372a2d(0x1a0)](_0x43b7ac,_0x5ca049,_0x46aa6e,_0x58a005,_0xd9769f)),_0x58a005['node'][_0x372a2d(0x236)]=_0x2534ca,_0x58a005[_0x372a2d(0x251)]['index']=_0x16546a;};}[_0x24925f(0x1db)](_0x3b42f6,_0x244a07,_0x43e0b3,_0x3f05f0,_0x509124,_0x312e7a,_0x2069c8){var _0x106cd6=_0x24925f,_0x156c9f=this;return _0x244a07[_0x106cd6(0x241)+_0x509124[_0x106cd6(0x1cf)]()]=!0x0,function(_0x27fe44){var _0x4ab60b=_0x106cd6,_0x1c89a0=_0x312e7a['node'][_0x4ab60b(0x22b)],_0x15b90=_0x312e7a[_0x4ab60b(0x251)]['index'],_0x279f28=_0x312e7a[_0x4ab60b(0x251)]['parent'];_0x312e7a[_0x4ab60b(0x251)]['parent']=_0x1c89a0,_0x312e7a[_0x4ab60b(0x251)][_0x4ab60b(0x1a6)]=_0x27fe44,_0x3b42f6['push'](_0x156c9f[_0x4ab60b(0x1a0)](_0x43e0b3,_0x3f05f0,_0x509124,_0x312e7a,_0x2069c8)),_0x312e7a['node']['parent']=_0x279f28,_0x312e7a[_0x4ab60b(0x251)]['index']=_0x15b90;};}[_0x24925f(0x1a0)](_0x56e0f3,_0x37dc9c,_0x22da57,_0x1767c9,_0x351a90){var _0x1f68db=_0x24925f,_0x5ec8fd=this;_0x351a90||(_0x351a90=function(_0x80afd,_0xc70a1f){return _0x80afd[_0xc70a1f];});var _0x5e263f=_0x22da57[_0x1f68db(0x1cf)](),_0x496ea1=_0x1767c9[_0x1f68db(0x1ca)]||{},_0x2d53da=_0x1767c9[_0x1f68db(0x1c5)],_0x3d8061=_0x1767c9[_0x1f68db(0x187)];try{var _0x38e2a0=this[_0x1f68db(0x1ce)](_0x56e0f3),_0xa9a931=_0x5e263f;_0x38e2a0&&_0xa9a931[0x0]==='\\\\x27'&&(_0xa9a931=_0xa9a931[_0x1f68db(0x202)](0x1,_0xa9a931[_0x1f68db(0x1c7)]-0x2));var _0x2d1cd=_0x1767c9[_0x1f68db(0x1ca)]=_0x496ea1[_0x1f68db(0x241)+_0xa9a931];_0x2d1cd&&(_0x1767c9['depth']=_0x1767c9[_0x1f68db(0x1c5)]+0x1),_0x1767c9[_0x1f68db(0x187)]=!!_0x2d1cd;var _0x72b981=typeof _0x22da57==_0x1f68db(0x252),_0x43a580={'name':_0x72b981||_0x38e2a0?_0x5e263f:this['_propertyName'](_0x5e263f)};if(_0x72b981&&(_0x43a580[_0x1f68db(0x252)]=!0x0),!(_0x37dc9c===_0x1f68db(0x1c0)||_0x37dc9c===_0x1f68db(0x247))){var _0x4f831d=this[_0x1f68db(0x265)](_0x56e0f3,_0x22da57);if(_0x4f831d&&(_0x4f831d['set']&&(_0x43a580[_0x1f68db(0x1b8)]=!0x0),_0x4f831d['get']&&!_0x2d1cd&&!_0x1767c9['resolveGetters']))return _0x43a580[_0x1f68db(0x188)]=!0x0,this[_0x1f68db(0x1d5)](_0x43a580,_0x1767c9),_0x43a580;}var _0x26c2ff;try{_0x26c2ff=_0x351a90(_0x56e0f3,_0x22da57);}catch(_0x3a2eda){return _0x43a580={'name':_0x5e263f,'type':'unknown','error':_0x3a2eda[_0x1f68db(0x1a5)]},this[_0x1f68db(0x1d5)](_0x43a580,_0x1767c9),_0x43a580;}var _0x2f106c=this['_type'](_0x26c2ff),_0x1fea13=this[_0x1f68db(0x1c4)](_0x2f106c);if(_0x43a580[_0x1f68db(0x1ac)]=_0x2f106c,_0x1fea13)this[_0x1f68db(0x1d5)](_0x43a580,_0x1767c9,_0x26c2ff,function(){var _0x31e2f0=_0x1f68db;_0x43a580[_0x31e2f0(0x1f3)]=_0x26c2ff['valueOf'](),!_0x2d1cd&&_0x5ec8fd[_0x31e2f0(0x256)](_0x2f106c,_0x43a580,_0x1767c9,{});});else{var _0x573d4a=_0x1767c9['autoExpand']&&_0x1767c9[_0x1f68db(0x182)]<_0x1767c9['autoExpandMaxDepth']&&_0x1767c9[_0x1f68db(0x198)][_0x1f68db(0x21f)](_0x26c2ff)<0x0&&_0x2f106c!==_0x1f68db(0x1a8)&&_0x1767c9[_0x1f68db(0x184)]<_0x1767c9[_0x1f68db(0x1ea)];_0x573d4a||_0x1767c9[_0x1f68db(0x182)]<_0x2d53da||_0x2d1cd?(this[_0x1f68db(0x18a)](_0x43a580,_0x26c2ff,_0x1767c9,_0x2d1cd||{}),this['_additionalMetadata'](_0x26c2ff,_0x43a580)):this[_0x1f68db(0x1d5)](_0x43a580,_0x1767c9,_0x26c2ff,function(){var _0x1ede4e=_0x1f68db;_0x2f106c===_0x1ede4e(0x1d1)||_0x2f106c===_0x1ede4e(0x1f7)||(delete _0x43a580['value'],_0x43a580['capped']=!0x0);});}return _0x43a580;}finally{_0x1767c9[_0x1f68db(0x1ca)]=_0x496ea1,_0x1767c9[_0x1f68db(0x1c5)]=_0x2d53da,_0x1767c9[_0x1f68db(0x187)]=_0x3d8061;}}[_0x24925f(0x256)](_0x4fc504,_0x3bd1a0,_0x558e05,_0x150054){var _0x489bda=_0x24925f,_0x252ee7=_0x150054[_0x489bda(0x255)]||_0x558e05[_0x489bda(0x255)];if((_0x4fc504==='string'||_0x4fc504===_0x489bda(0x1c2))&&_0x3bd1a0[_0x489bda(0x1f3)]){let _0x22add3=_0x3bd1a0['value']['length'];_0x558e05[_0x489bda(0x174)]+=_0x22add3,_0x558e05['allStrLength']>_0x558e05[_0x489bda(0x21d)]?(_0x3bd1a0[_0x489bda(0x264)]='',delete _0x3bd1a0['value']):_0x22add3>_0x252ee7&&(_0x3bd1a0[_0x489bda(0x264)]=_0x3bd1a0[_0x489bda(0x1f3)][_0x489bda(0x202)](0x0,_0x252ee7),delete _0x3bd1a0[_0x489bda(0x1f3)]);}}['_isMap'](_0x1c6583){var _0xc0553e=_0x24925f;return!!(_0x1c6583&&_0x408b28[_0xc0553e(0x258)]&&this[_0xc0553e(0x1bb)](_0x1c6583)==='[object\\\\x20Map]'&&_0x1c6583[_0xc0553e(0x1ed)]);}['_propertyName'](_0x3e98b4){var _0x5783bf=_0x24925f;if(_0x3e98b4[_0x5783bf(0x200)](/^\\\\d+$/))return _0x3e98b4;var _0x29480e;try{_0x29480e=JSON['stringify'](''+_0x3e98b4);}catch{_0x29480e='\\\\x22'+this['_objectToString'](_0x3e98b4)+'\\\\x22';}return _0x29480e[_0x5783bf(0x200)](/^\\\"([a-zA-Z_][a-zA-Z_0-9]*)\\\"$/)?_0x29480e=_0x29480e[_0x5783bf(0x202)](0x1,_0x29480e[_0x5783bf(0x1c7)]-0x2):_0x29480e=_0x29480e[_0x5783bf(0x1c9)](/'/g,'\\\\x5c\\\\x27')[_0x5783bf(0x1c9)](/\\\\\\\\\\\"/g,'\\\\x22')['replace'](/(^\\\"|\\\"$)/g,'\\\\x27'),_0x29480e;}[_0x24925f(0x1d5)](_0x558d23,_0x6b8a82,_0x5a247c,_0x2e606b){var _0x24e788=_0x24925f;this[_0x24e788(0x183)](_0x558d23,_0x6b8a82),_0x2e606b&&_0x2e606b(),this[_0x24e788(0x17d)](_0x5a247c,_0x558d23),this[_0x24e788(0x1bd)](_0x558d23,_0x6b8a82);}['_treeNodePropertiesBeforeFullValue'](_0x416f97,_0x3b4960){var _0x41e21a=_0x24925f;this['_setNodeId'](_0x416f97,_0x3b4960),this[_0x41e21a(0x262)](_0x416f97,_0x3b4960),this[_0x41e21a(0x227)](_0x416f97,_0x3b4960),this[_0x41e21a(0x24e)](_0x416f97,_0x3b4960);}[_0x24925f(0x25f)](_0x575e16,_0x125fde){}['_setNodeQueryPath'](_0x5bc81a,_0x4e2ede){}[_0x24925f(0x18c)](_0x3a80cd,_0x238892){}[_0x24925f(0x1dc)](_0x610f7){var _0x885368=_0x24925f;return _0x610f7===this[_0x885368(0x1f4)];}[_0x24925f(0x1bd)](_0x11ddb7,_0x3c07b7){var _0x43b4d7=_0x24925f;this['_setNodeLabel'](_0x11ddb7,_0x3c07b7),this['_setNodeExpandableState'](_0x11ddb7),_0x3c07b7[_0x43b4d7(0x1af)]&&this[_0x43b4d7(0x20c)](_0x11ddb7),this[_0x43b4d7(0x21e)](_0x11ddb7,_0x3c07b7),this[_0x43b4d7(0x1d2)](_0x11ddb7,_0x3c07b7),this['_cleanNode'](_0x11ddb7);}['_additionalMetadata'](_0xc95a86,_0x59da12){var _0xd16d1b=_0x24925f;try{_0xc95a86&&typeof _0xc95a86[_0xd16d1b(0x1c7)]==_0xd16d1b(0x1ec)&&(_0x59da12[_0xd16d1b(0x1c7)]=_0xc95a86['length']);}catch{}if(_0x59da12['type']===_0xd16d1b(0x1ec)||_0x59da12['type']===_0xd16d1b(0x1f9)){if(isNaN(_0x59da12[_0xd16d1b(0x1f3)]))_0x59da12[_0xd16d1b(0x1e3)]=!0x0,delete _0x59da12['value'];else switch(_0x59da12[_0xd16d1b(0x1f3)]){case Number[_0xd16d1b(0x217)]:_0x59da12[_0xd16d1b(0x18d)]=!0x0,delete _0x59da12[_0xd16d1b(0x1f3)];break;case Number[_0xd16d1b(0x192)]:_0x59da12[_0xd16d1b(0x238)]=!0x0,delete _0x59da12[_0xd16d1b(0x1f3)];break;case 0x0:this[_0xd16d1b(0x259)](_0x59da12['value'])&&(_0x59da12[_0xd16d1b(0x224)]=!0x0);break;}}else _0x59da12[_0xd16d1b(0x1ac)]==='function'&&typeof _0xc95a86[_0xd16d1b(0x175)]==_0xd16d1b(0x194)&&_0xc95a86['name']&&_0x59da12[_0xd16d1b(0x175)]&&_0xc95a86['name']!==_0x59da12[_0xd16d1b(0x175)]&&(_0x59da12[_0xd16d1b(0x1d8)]=_0xc95a86[_0xd16d1b(0x175)]);}[_0x24925f(0x259)](_0x3016ce){var _0x1f84f7=_0x24925f;return 0x1/_0x3016ce===Number[_0x1f84f7(0x192)];}['_sortProps'](_0x4a8a82){var _0x7c464d=_0x24925f;!_0x4a8a82[_0x7c464d(0x177)]||!_0x4a8a82[_0x7c464d(0x177)][_0x7c464d(0x1c7)]||_0x4a8a82[_0x7c464d(0x1ac)]===_0x7c464d(0x1c0)||_0x4a8a82[_0x7c464d(0x1ac)]===_0x7c464d(0x258)||_0x4a8a82['type']===_0x7c464d(0x20d)||_0x4a8a82[_0x7c464d(0x177)][_0x7c464d(0x19a)](function(_0x191784,_0x4e6ef0){var _0x30e51c=_0x7c464d,_0x36b804=_0x191784['name'][_0x30e51c(0x170)](),_0x4f6713=_0x4e6ef0[_0x30e51c(0x175)][_0x30e51c(0x170)]();return _0x36b804<_0x4f6713?-0x1:_0x36b804>_0x4f6713?0x1:0x0;});}[_0x24925f(0x21e)](_0x3a1415,_0x452ad6){var _0x2db392=_0x24925f;if(!(_0x452ad6['noFunctions']||!_0x3a1415['props']||!_0x3a1415[_0x2db392(0x177)]['length'])){for(var _0x4e747d=[],_0x2d7344=[],_0x2f2a51=0x0,_0x1f3463=_0x3a1415[_0x2db392(0x177)][_0x2db392(0x1c7)];_0x2f2a51<_0x1f3463;_0x2f2a51++){var _0x3f7e04=_0x3a1415['props'][_0x2f2a51];_0x3f7e04[_0x2db392(0x1ac)]===_0x2db392(0x1a8)?_0x4e747d[_0x2db392(0x218)](_0x3f7e04):_0x2d7344[_0x2db392(0x218)](_0x3f7e04);}if(!(!_0x2d7344[_0x2db392(0x1c7)]||_0x4e747d[_0x2db392(0x1c7)]<=0x1)){_0x3a1415[_0x2db392(0x177)]=_0x2d7344;var _0x2ad4dd={'functionsNode':!0x0,'props':_0x4e747d};this['_setNodeId'](_0x2ad4dd,_0x452ad6),this['_setNodeLabel'](_0x2ad4dd,_0x452ad6),this[_0x2db392(0x268)](_0x2ad4dd),this[_0x2db392(0x24e)](_0x2ad4dd,_0x452ad6),_0x2ad4dd['id']+='\\\\x20f',_0x3a1415[_0x2db392(0x177)][_0x2db392(0x1be)](_0x2ad4dd);}}}[_0x24925f(0x1d2)](_0x47887b,_0x4592d7){}[_0x24925f(0x268)](_0x3ec714){}[_0x24925f(0x207)](_0x4b5518){var _0x693152=_0x24925f;return Array[_0x693152(0x1ae)](_0x4b5518)||typeof _0x4b5518==_0x693152(0x1a4)&&this[_0x693152(0x1bb)](_0x4b5518)===_0x693152(0x17c);}['_setNodePermissions'](_0x5347a8,_0x12b080){}[_0x24925f(0x1bc)](_0x41d40d){var _0x26bd7f=_0x24925f;delete _0x41d40d[_0x26bd7f(0x173)],delete _0x41d40d[_0x26bd7f(0x18f)],delete _0x41d40d[_0x26bd7f(0x23d)];}[_0x24925f(0x227)](_0x5c5ee5,_0x457b54){}}let _0x13c4c2=new _0x16dd22(),_0x310fbb={'props':0x64,'elements':0x64,'strLength':0x400*0x32,'totalStrLength':0x400*0x32,'autoExpandLimit':0x1388,'autoExpandMaxDepth':0xa},_0x36da40={'props':0x5,'elements':0x5,'strLength':0x100,'totalStrLength':0x100*0x3,'autoExpandLimit':0x1e,'autoExpandMaxDepth':0x2};function _0x286351(_0x42ff0e,_0x4b7333,_0x5099a8,_0x488fa0,_0x3a235f,_0x2489dc){var _0x2a93e9=_0x24925f;let _0x3f3ff2,_0x295edc;try{_0x295edc=_0x57fcdb(),_0x3f3ff2=_0x1d4002[_0x4b7333],!_0x3f3ff2||_0x295edc-_0x3f3ff2['ts']>0x1f4&&_0x3f3ff2[_0x2a93e9(0x235)]&&_0x3f3ff2[_0x2a93e9(0x17f)]/_0x3f3ff2[_0x2a93e9(0x235)]<0x64?(_0x1d4002[_0x4b7333]=_0x3f3ff2={'count':0x0,'time':0x0,'ts':_0x295edc},_0x1d4002['hits']={}):_0x295edc-_0x1d4002[_0x2a93e9(0x1a1)]['ts']>0x32&&_0x1d4002[_0x2a93e9(0x1a1)]['count']&&_0x1d4002[_0x2a93e9(0x1a1)][_0x2a93e9(0x17f)]/_0x1d4002[_0x2a93e9(0x1a1)][_0x2a93e9(0x235)]<0x64&&(_0x1d4002[_0x2a93e9(0x1a1)]={});let _0x599111=[],_0x10de9b=_0x3f3ff2[_0x2a93e9(0x232)]||_0x1d4002[_0x2a93e9(0x1a1)][_0x2a93e9(0x232)]?_0x36da40:_0x310fbb,_0x47456f=_0x2af867=>{var _0x2c4230=_0x2a93e9;let _0x2ef191={};return _0x2ef191['props']=_0x2af867[_0x2c4230(0x177)],_0x2ef191['elements']=_0x2af867['elements'],_0x2ef191[_0x2c4230(0x255)]=_0x2af867[_0x2c4230(0x255)],_0x2ef191['totalStrLength']=_0x2af867[_0x2c4230(0x21d)],_0x2ef191['autoExpandLimit']=_0x2af867[_0x2c4230(0x1ea)],_0x2ef191[_0x2c4230(0x234)]=_0x2af867[_0x2c4230(0x234)],_0x2ef191['sortProps']=!0x1,_0x2ef191[_0x2c4230(0x215)]=!_0x2a09fc,_0x2ef191[_0x2c4230(0x1c5)]=0x1,_0x2ef191['level']=0x0,_0x2ef191[_0x2c4230(0x230)]=_0x2c4230(0x18e),_0x2ef191[_0x2c4230(0x20f)]=_0x2c4230(0x1a2),_0x2ef191[_0x2c4230(0x23c)]=!0x0,_0x2ef191[_0x2c4230(0x198)]=[],_0x2ef191[_0x2c4230(0x184)]=0x0,_0x2ef191[_0x2c4230(0x260)]=!0x0,_0x2ef191[_0x2c4230(0x174)]=0x0,_0x2ef191[_0x2c4230(0x251)]={'current':void 0x0,'parent':void 0x0,'index':0x0},_0x2ef191;};for(var _0x4942f=0x0;_0x4942f<_0x3a235f[_0x2a93e9(0x1c7)];_0x4942f++)_0x599111[_0x2a93e9(0x218)](_0x13c4c2['serialize']({'timeNode':_0x42ff0e===_0x2a93e9(0x17f)||void 0x0},_0x3a235f[_0x4942f],_0x47456f(_0x10de9b),{}));if(_0x42ff0e==='trace'||_0x42ff0e===_0x2a93e9(0x25e)){let _0xd32cb6=Error['stackTraceLimit'];try{Error[_0x2a93e9(0x1b0)]=0x1/0x0,_0x599111[_0x2a93e9(0x218)](_0x13c4c2['serialize']({'stackNode':!0x0},new Error()[_0x2a93e9(0x1a9)],_0x47456f(_0x10de9b),{'strLength':0x1/0x0}));}finally{Error[_0x2a93e9(0x1b0)]=_0xd32cb6;}}return{'method':_0x2a93e9(0x209),'version':_0x29575b,'args':[{'ts':_0x5099a8,'session':_0x488fa0,'args':_0x599111,'id':_0x4b7333,'context':_0x2489dc}]};}catch(_0x2bff50){return{'method':_0x2a93e9(0x209),'version':_0x29575b,'args':[{'ts':_0x5099a8,'session':_0x488fa0,'args':[{'type':_0x2a93e9(0x17a),'error':_0x2bff50&&_0x2bff50['message']}],'id':_0x4b7333,'context':_0x2489dc}]};}finally{try{if(_0x3f3ff2&&_0x295edc){let _0x101bc1=_0x57fcdb();_0x3f3ff2[_0x2a93e9(0x235)]++,_0x3f3ff2[_0x2a93e9(0x17f)]+=_0x27f964(_0x295edc,_0x101bc1),_0x3f3ff2['ts']=_0x101bc1,_0x1d4002['hits']['count']++,_0x1d4002[_0x2a93e9(0x1a1)][_0x2a93e9(0x17f)]+=_0x27f964(_0x295edc,_0x101bc1),_0x1d4002[_0x2a93e9(0x1a1)]['ts']=_0x101bc1,(_0x3f3ff2[_0x2a93e9(0x235)]>0x32||_0x3f3ff2[_0x2a93e9(0x17f)]>0x64)&&(_0x3f3ff2[_0x2a93e9(0x232)]=!0x0),(_0x1d4002[_0x2a93e9(0x1a1)][_0x2a93e9(0x235)]>0x3e8||_0x1d4002['hits'][_0x2a93e9(0x17f)]>0x12c)&&(_0x1d4002[_0x2a93e9(0x1a1)][_0x2a93e9(0x232)]=!0x0);}}catch{}}}return _0x286351;}((_0x52e357,_0x601197,_0xf66a79,_0x28d4ce,_0x4c8431,_0x836d1e,_0x2a5428,_0xd126d2,_0x5b83a5,_0x92e93a,_0x23a755)=>{var _0x40ab87=_0x1d5429;if(_0x52e357['_console_ninja'])return _0x52e357[_0x40ab87(0x26b)];if(!X(_0x52e357,_0xd126d2,_0x4c8431))return _0x52e357[_0x40ab87(0x26b)]={'consoleLog':()=>{},'consoleTrace':()=>{},'consoleTime':()=>{},'consoleTimeEnd':()=>{},'autoLog':()=>{},'autoLogMany':()=>{},'autoTraceMany':()=>{},'coverage':()=>{},'autoTrace':()=>{},'autoTime':()=>{},'autoTimeEnd':()=>{}},_0x52e357[_0x40ab87(0x26b)];let _0x4629eb=B(_0x52e357),_0x139da8=_0x4629eb[_0x40ab87(0x197)],_0x5c0e3e=_0x4629eb[_0x40ab87(0x1cd)],_0x5e974f=_0x4629eb[_0x40ab87(0x1aa)],_0x53a3ee={'hits':{},'ts':{}},_0x14414a=J(_0x52e357,_0x5b83a5,_0x53a3ee,_0x836d1e),_0x46e199=_0x23d5d3=>{_0x53a3ee['ts'][_0x23d5d3]=_0x5c0e3e();},_0x39c023=(_0x47ab03,_0x500766)=>{var _0x368b2=_0x40ab87;let _0xcb8756=_0x53a3ee['ts'][_0x500766];if(delete _0x53a3ee['ts'][_0x500766],_0xcb8756){let _0x380d4c=_0x139da8(_0xcb8756,_0x5c0e3e());_0x50a41a(_0x14414a(_0x368b2(0x17f),_0x47ab03,_0x5e974f(),_0x3c8255,[_0x380d4c],_0x500766));}},_0x208754=_0x1cc04c=>{var _0x5e95b0=_0x40ab87,_0x2d3cf8;return _0x4c8431===_0x5e95b0(0x208)&&_0x52e357[_0x5e95b0(0x199)]&&((_0x2d3cf8=_0x1cc04c==null?void 0x0:_0x1cc04c[_0x5e95b0(0x1cb)])==null?void 0x0:_0x2d3cf8[_0x5e95b0(0x1c7)])&&(_0x1cc04c[_0x5e95b0(0x1cb)][0x0][_0x5e95b0(0x199)]=_0x52e357[_0x5e95b0(0x199)]),_0x1cc04c;};_0x52e357['_console_ninja']={'consoleLog':(_0x290e74,_0x1924d0)=>{var _0x297039=_0x40ab87;_0x52e357[_0x297039(0x1a3)][_0x297039(0x209)][_0x297039(0x175)]!=='disabledLog'&&_0x50a41a(_0x14414a(_0x297039(0x209),_0x290e74,_0x5e974f(),_0x3c8255,_0x1924d0));},'consoleTrace':(_0x1ed7be,_0x387bd0)=>{var _0xeca4c4=_0x40ab87,_0x3433f5,_0x40fd55;_0x52e357[_0xeca4c4(0x1a3)][_0xeca4c4(0x209)][_0xeca4c4(0x175)]!=='disabledTrace'&&((_0x40fd55=(_0x3433f5=_0x52e357[_0xeca4c4(0x186)])==null?void 0x0:_0x3433f5['versions'])!=null&&_0x40fd55['node']&&(_0x52e357['_ninjaIgnoreNextError']=!0x0),_0x50a41a(_0x208754(_0x14414a(_0xeca4c4(0x24f),_0x1ed7be,_0x5e974f(),_0x3c8255,_0x387bd0))));},'consoleError':(_0x42a3b7,_0x30bc8c)=>{var _0xf9655f=_0x40ab87;_0x52e357['_ninjaIgnoreNextError']=!0x0,_0x50a41a(_0x208754(_0x14414a(_0xf9655f(0x25e),_0x42a3b7,_0x5e974f(),_0x3c8255,_0x30bc8c)));},'consoleTime':_0x3efe77=>{_0x46e199(_0x3efe77);},'consoleTimeEnd':(_0x132f41,_0x27e220)=>{_0x39c023(_0x27e220,_0x132f41);},'autoLog':(_0x4f0726,_0x3d1ffa)=>{_0x50a41a(_0x14414a('log',_0x3d1ffa,_0x5e974f(),_0x3c8255,[_0x4f0726]));},'autoLogMany':(_0x1a48fc,_0x229101)=>{var _0x1a8cf=_0x40ab87;_0x50a41a(_0x14414a(_0x1a8cf(0x209),_0x1a48fc,_0x5e974f(),_0x3c8255,_0x229101));},'autoTrace':(_0x187091,_0x1713a4)=>{_0x50a41a(_0x208754(_0x14414a('trace',_0x1713a4,_0x5e974f(),_0x3c8255,[_0x187091])));},'autoTraceMany':(_0x207a20,_0x42e86e)=>{var _0x1c9630=_0x40ab87;_0x50a41a(_0x208754(_0x14414a(_0x1c9630(0x24f),_0x207a20,_0x5e974f(),_0x3c8255,_0x42e86e)));},'autoTime':(_0x4fc227,_0xd15575,_0xe32265)=>{_0x46e199(_0xe32265);},'autoTimeEnd':(_0x2600b1,_0x561e96,_0x581d44)=>{_0x39c023(_0x561e96,_0x581d44);},'coverage':_0x11be4f=>{var _0x525d8c=_0x40ab87;_0x50a41a({'method':_0x525d8c(0x1b5),'version':_0x836d1e,'args':[{'id':_0x11be4f}]});}};let _0x50a41a=H(_0x52e357,_0x601197,_0xf66a79,_0x28d4ce,_0x4c8431,_0x92e93a,_0x23a755),_0x3c8255=_0x52e357['_console_ninja_session'];return _0x52e357[_0x40ab87(0x26b)];})(globalThis,_0x1d5429(0x1c6),_0x1d5429(0x25b),_0x1d5429(0x21c),_0x1d5429(0x1ad),_0x1d5429(0x220),_0x1d5429(0x19b),_0x1d5429(0x1bf),'',_0x1d5429(0x25c),_0x1d5429(0x269));\");\n    } catch (e) {}\n}\nfunction oo_oo(i) {\n    for(var _len = arguments.length, v = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++){\n        v[_key - 1] = arguments[_key];\n    }\n    try {\n        oo_cm().consoleLog(i, v);\n    } catch (e) {}\n    return v;\n}\noo_oo; /* istanbul ignore next */ \nfunction oo_tr(i) {\n    for(var _len = arguments.length, v = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++){\n        v[_key - 1] = arguments[_key];\n    }\n    try {\n        oo_cm().consoleTrace(i, v);\n    } catch (e) {}\n    return v;\n}\noo_tr; /* istanbul ignore next */ \nfunction oo_tx(i) {\n    for(var _len = arguments.length, v = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++){\n        v[_key - 1] = arguments[_key];\n    }\n    try {\n        oo_cm().consoleError(i, v);\n    } catch (e) {}\n    return v;\n}\noo_tx; /* istanbul ignore next */ \nfunction oo_ts(v) {\n    try {\n        oo_cm().consoleTime(v);\n    } catch (e) {}\n    return v;\n}\noo_ts; /* istanbul ignore next */ \nfunction oo_te(v, i) {\n    try {\n        oo_cm().consoleTimeEnd(v, i);\n    } catch (e) {}\n    return v;\n}\noo_te; /*eslint unicorn/no-abusive-eslint-disable:,eslint-comments/disable-enable-pair:,eslint-comments/no-unlimited-disable:,eslint-comments/no-aggregating-enable:,eslint-comments/no-duplicate-disable:,eslint-comments/no-unused-disable:,eslint-comments/no-unused-enable:,*/ \nvar _c;\n$RefreshReg$(_c, \"CreateTrackSheet\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/user/trackSheets/createTrackSheet.tsx\n"));

/***/ })

});