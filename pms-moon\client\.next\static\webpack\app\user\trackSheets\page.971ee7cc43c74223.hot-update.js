"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/user/trackSheets/page",{

/***/ "(app-pages-browser)/./app/user/trackSheets/LegrandDetailsComponent.tsx":
/*!**********************************************************!*\
  !*** ./app/user/trackSheets/LegrandDetailsComponent.tsx ***!
  \**********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _app_component_SearchSelect__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/app/_component/SearchSelect */ \"(app-pages-browser)/./app/_component/SearchSelect.tsx\");\n/* harmony import */ var _barrel_optimize_names_MapPin_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=MapPin!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _lib_routePath__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/routePath */ \"(app-pages-browser)/./lib/routePath.ts\");\n/* harmony import */ var _lib_helpers__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/helpers */ \"(app-pages-browser)/./lib/helpers.ts\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react-hot-toast */ \"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst LegrandDetailsComponent = (param)=>{\n    let { form, entryIndex, onLegrandDataChange, blockTitle = \"LEGRAND Details\", fieldPrefix = \"legrand\" } = param;\n    _s();\n    const [legrandData, setLegrandData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const fetchLegrandData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async ()=>{\n        try {\n            setLoading(true);\n            const response = await (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_4__.getAllData)(_lib_routePath__WEBPACK_IMPORTED_MODULE_3__.legrandMapping_routes.GET_LEGRAND_MAPPINGS);\n            // Filter out any invalid entries to reduce processing load\n            const validData = (response || []).filter((item)=>item && (item.legalName || item.aliasShippingNames) && item.shippingBillingAddress);\n            setLegrandData(validData);\n        } catch (error) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_5__[\"default\"].error(\"Error fetching LEGRAND mapping data:\", error);\n            setLegrandData([]);\n        } finally{\n            setLoading(false);\n        }\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchLegrandData();\n    }, [\n        fetchLegrandData\n    ]);\n    const extractCityFromAddress = (address)=>{\n        if (!address) return \"\";\n        const parts = address.split(\",\");\n        if (parts.length >= 2) {\n            const cityPart = parts[parts.length - 2].trim();\n            const city = cityPart.replace(/\\d+/g, \"\").trim();\n            return city;\n        }\n        const words = address.split(\" \");\n        for (const word of words){\n            if (word.length > 2 && !/^\\d+$/.test(word) && !word.includes(\"ROAD\") && !word.includes(\"STREET\") && !word.includes(\"AVE\")) {\n                return word;\n            }\n        }\n        return \"\";\n    };\n    const aliasOptions = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        const options = [];\n        legrandData.forEach((data)=>{\n            const location = data.location || extractCityFromAddress(data.shippingBillingAddress || \"\");\n            const zipcode = data.zipPostal || \"\";\n            let displayName = \"\";\n            let type = \"company\";\n            const legalName = data.legalName || \"Unknown Company\";\n            const aliasName = data.aliasShippingNames && data.aliasShippingNames !== \"NONE\" ? data.aliasShippingNames : null;\n            if (aliasName) {\n                displayName = \"\".concat(legalName, \" (\").concat(aliasName, \")\");\n                type = \"alias\";\n            } else {\n                displayName = legalName;\n                type = \"company\";\n            }\n            const uniqueKey = \"\".concat(data.customeCode, \"-\").concat(data.aliasShippingNames || data.legalName, \"-\").concat(data.shippingBillingAddress);\n            const sameNameEntries = legrandData.filter((d)=>{\n                const dLegalName = d.legalName || \"Unknown Company\";\n                const dAliasName = d.aliasShippingNames && d.aliasShippingNames !== \"NONE\" ? d.aliasShippingNames : null;\n                let dDisplayName = \"\";\n                if (dAliasName) {\n                    dDisplayName = \"\".concat(dLegalName, \" (\").concat(dAliasName, \")\");\n                } else {\n                    dDisplayName = dLegalName;\n                }\n                return dDisplayName === displayName;\n            });\n            let finalDisplayName = displayName;\n            if (sameNameEntries.length > 1) {\n                if (location) {\n                    finalDisplayName = \"\".concat(displayName, \"_\").concat(location);\n                } else if (zipcode) {\n                    finalDisplayName = \"\".concat(displayName, \"_\").concat(zipcode);\n                }\n            }\n            options.push({\n                value: uniqueKey,\n                label: finalDisplayName,\n                type: type,\n                displayName: finalDisplayName\n            });\n        });\n        const uniqueOptions = options.filter((option, index, self)=>index === self.findIndex((o)=>o.value === option.value));\n        return uniqueOptions.sort((a, b)=>a.label.localeCompare(b.label));\n    }, [\n        legrandData\n    ]);\n    const getBaseName = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((data)=>{\n        const legalName = data.legalName || \"Unknown Company\";\n        const aliasName = data.aliasShippingNames && data.aliasShippingNames !== \"NONE\" ? data.aliasShippingNames : null;\n        if (aliasName) {\n            return \"\".concat(legalName, \" (\").concat(aliasName, \")\");\n        } else {\n            return legalName;\n        }\n    }, []);\n    const checkForDuplicates = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((field, value)=>{\n        let matchingEntries = [];\n        if (field === \"alias\") {\n            const exactMatch = legrandData.find((data)=>{\n                const uniqueKey = \"\".concat(data.customeCode, \"-\").concat(data.aliasShippingNames || data.legalName, \"-\").concat(data.shippingBillingAddress);\n                return uniqueKey === value;\n            });\n            if (exactMatch) {\n                const baseName = getBaseName(exactMatch);\n                const sameBaseNameEntries = legrandData.filter((data)=>getBaseName(data) === baseName);\n                if (sameBaseNameEntries.length === 1) {\n                    matchingEntries = [\n                        exactMatch\n                    ];\n                } else {\n                    var _aliasOptions_find;\n                    const selectedDisplayName = ((_aliasOptions_find = aliasOptions.find((option)=>option.value === value)) === null || _aliasOptions_find === void 0 ? void 0 : _aliasOptions_find.displayName) || \"\";\n                    const sameDisplayNameEntries = legrandData.filter((data)=>{\n                        const location = data.location || extractCityFromAddress(data.shippingBillingAddress || \"\");\n                        let displayName = getBaseName(data);\n                        const sameNameEntries = legrandData.filter((d)=>getBaseName(d) === displayName);\n                        if (sameNameEntries.length > 1) {\n                            if (location) {\n                                displayName = \"\".concat(displayName, \"_\").concat(location);\n                            } else if (data.zipPostal) {\n                                displayName = \"\".concat(displayName, \"_\").concat(data.zipPostal);\n                            }\n                        }\n                        return displayName === selectedDisplayName;\n                    });\n                    matchingEntries = sameDisplayNameEntries;\n                }\n            }\n        } else if (field === \"address\") {\n            matchingEntries = legrandData.filter((data)=>data.shippingBillingAddress === value);\n        } else if (field === \"zipcode\") {\n            matchingEntries = legrandData.filter((data)=>data.zipPostal === value);\n        }\n        return matchingEntries;\n    }, [\n        legrandData,\n        getBaseName,\n        aliasOptions\n    ]);\n    const clearOtherBlocks = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((currentPrefix)=>{\n        const prefixes = [\n            \"shipper\",\n            \"consignee\",\n            \"billto\"\n        ];\n        const otherPrefixes = prefixes.filter((p)=>p !== currentPrefix);\n        otherPrefixes.forEach((prefix)=>{\n            const currentAlias = form.getValues(\"entries.\".concat(entryIndex, \".\").concat(prefix, \"Alias\"));\n            const currentAddress = form.getValues(\"entries.\".concat(entryIndex, \".\").concat(prefix, \"Address\"));\n            const currentZipcode = form.getValues(\"entries.\".concat(entryIndex, \".\").concat(prefix, \"Zipcode\"));\n            if (currentAlias || currentAddress || currentZipcode) {\n                form.setValue(\"entries.\".concat(entryIndex, \".\").concat(prefix, \"Alias\"), \"\");\n                form.setValue(\"entries.\".concat(entryIndex, \".\").concat(prefix, \"Address\"), \"\");\n                form.setValue(\"entries.\".concat(entryIndex, \".\").concat(prefix, \"Zipcode\"), \"\");\n            }\n        });\n    }, [\n        form,\n        entryIndex\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!legrandData.length) return;\n        let timeoutId;\n        const subscription = form.watch((_, param)=>{\n            let { name, type } = param;\n            if (!name || !name.includes(\"entries.\".concat(entryIndex, \".\").concat(fieldPrefix)) || type !== \"change\") return;\n            // Debounce the processing to avoid excessive calculations\n            clearTimeout(timeoutId);\n            timeoutId = setTimeout(()=>{\n                const currentAlias = form.getValues(\"entries.\".concat(entryIndex, \".\").concat(fieldPrefix, \"Alias\"));\n                const currentAddress = form.getValues(\"entries.\".concat(entryIndex, \".\").concat(fieldPrefix, \"Address\"));\n                const currentZipcode = form.getValues(\"entries.\".concat(entryIndex, \".\").concat(fieldPrefix, \"Zipcode\"));\n                if (currentAlias || currentAddress || currentZipcode) {\n                    clearOtherBlocks(fieldPrefix);\n                }\n                if (name === \"entries.\".concat(entryIndex, \".\").concat(fieldPrefix, \"Alias\") && currentAlias) {\n                    const selectedData = legrandData.find((data)=>{\n                        const uniqueKey = \"\".concat(data.customeCode, \"-\").concat(data.aliasShippingNames || data.legalName, \"-\").concat(data.shippingBillingAddress);\n                        return uniqueKey === currentAlias;\n                    });\n                    if (selectedData) {\n                        const duplicateEntries = checkForDuplicates(\"alias\", currentAlias);\n                        if (duplicateEntries.length > 1) {\n                            if (currentAddress) {\n                                const addressMatchesAlias = duplicateEntries.some((entry)=>entry.shippingBillingAddress === currentAddress);\n                                if (addressMatchesAlias) {\n                                    const specificEntry = duplicateEntries.find((entry)=>entry.shippingBillingAddress === currentAddress);\n                                    if (specificEntry && currentZipcode !== specificEntry.zipPostal) {\n                                        form.setValue(\"entries.\".concat(entryIndex, \".\").concat(fieldPrefix, \"Zipcode\"), specificEntry.zipPostal);\n                                    }\n                                } else {\n                                    form.setValue(\"entries.\".concat(entryIndex, \".\").concat(fieldPrefix, \"Address\"), \"\");\n                                    if (currentZipcode !== \"\") {\n                                        form.setValue(\"entries.\".concat(entryIndex, \".\").concat(fieldPrefix, \"Zipcode\"), \"\");\n                                    }\n                                }\n                            } else {\n                                if (currentZipcode) {\n                                    const zipcodeMatchesAlias = duplicateEntries.some((entry)=>entry.zipPostal === currentZipcode);\n                                    if (zipcodeMatchesAlias) {} else {\n                                        form.setValue(\"entries.\".concat(entryIndex, \".\").concat(fieldPrefix, \"Zipcode\"), \"\");\n                                    }\n                                } else {}\n                            }\n                        } else {\n                            if (currentAddress !== selectedData.shippingBillingAddress) {\n                                form.setValue(\"entries.\".concat(entryIndex, \".\").concat(fieldPrefix, \"Address\"), selectedData.shippingBillingAddress);\n                            }\n                            if (currentZipcode !== selectedData.zipPostal) {\n                                form.setValue(\"entries.\".concat(entryIndex, \".\").concat(fieldPrefix, \"Zipcode\"), selectedData.zipPostal);\n                            }\n                        }\n                        const currentCompany = form.getValues(\"entries.\".concat(entryIndex, \".company\"));\n                        if (currentCompany !== selectedData.businessUnit) {\n                            form.setValue(\"entries.\".concat(entryIndex, \".company\"), selectedData.businessUnit);\n                        }\n                        const allDivisions = [];\n                        duplicateEntries.forEach((entry)=>{\n                            if (entry.customeCode) {\n                                if (entry.customeCode.includes(\"/\")) {\n                                    const splitDivisions = entry.customeCode.split(\"/\").map((d)=>d.trim());\n                                    allDivisions.push(...splitDivisions);\n                                } else {\n                                    allDivisions.push(entry.customeCode);\n                                }\n                            }\n                        });\n                        const uniqueDivisions = Array.from(new Set(allDivisions.filter((code)=>code)));\n                        if (uniqueDivisions.length === 1) {\n                            const currentDivision = form.getValues(\"entries.\".concat(entryIndex, \".division\"));\n                            if (currentDivision !== uniqueDivisions[0]) {\n                                form.setValue(\"entries.\".concat(entryIndex, \".division\"), uniqueDivisions[0]);\n                            }\n                        } else {\n                            const currentDivision = form.getValues(\"entries.\".concat(entryIndex, \".division\"));\n                            if (currentDivision !== \"\") {\n                                form.setValue(\"entries.\".concat(entryIndex, \".division\"), \"\");\n                            }\n                        }\n                        if (onLegrandDataChange) {\n                            const divisionCodeToPass = uniqueDivisions.length === 1 ? uniqueDivisions[0] : \"\";\n                            onLegrandDataChange(entryIndex, selectedData.businessUnit || \"\", divisionCodeToPass);\n                        }\n                    }\n                }\n                if (name === \"entries.\".concat(entryIndex, \".\").concat(fieldPrefix, \"Address\") && currentAddress) {\n                    const duplicateEntries = checkForDuplicates(\"address\", currentAddress);\n                    if (duplicateEntries.length > 1) {\n                        if (currentAlias) {\n                            const selectedAliasData = legrandData.find((data)=>{\n                                const uniqueKey = \"\".concat(data.customeCode, \"-\").concat(data.aliasShippingNames || data.legalName, \"-\").concat(data.shippingBillingAddress);\n                                return uniqueKey === currentAlias;\n                            });\n                            if (selectedAliasData) {\n                                const baseName = getBaseName(selectedAliasData);\n                                const addressMatchesBaseName = legrandData.some((data)=>getBaseName(data) === baseName && data.shippingBillingAddress === currentAddress);\n                                if (addressMatchesBaseName) {\n                                    const addressSpecificEntry = legrandData.find((data)=>getBaseName(data) === baseName && data.shippingBillingAddress === currentAddress);\n                                    if (addressSpecificEntry) {\n                                        const newUniqueKey = \"\".concat(addressSpecificEntry.customeCode, \"-\").concat(addressSpecificEntry.aliasShippingNames || addressSpecificEntry.legalName, \"-\").concat(addressSpecificEntry.shippingBillingAddress);\n                                        if (currentAlias !== newUniqueKey) {\n                                            form.setValue(\"entries.\".concat(entryIndex, \".\").concat(fieldPrefix, \"Alias\"), newUniqueKey);\n                                        }\n                                        if (currentZipcode !== addressSpecificEntry.zipPostal) {\n                                            form.setValue(\"entries.\".concat(entryIndex, \".\").concat(fieldPrefix, \"Zipcode\"), addressSpecificEntry.zipPostal);\n                                        }\n                                        const currentCompany = form.getValues(\"entries.\".concat(entryIndex, \".company\"));\n                                        if (currentCompany !== addressSpecificEntry.businessUnit) {\n                                            form.setValue(\"entries.\".concat(entryIndex, \".company\"), addressSpecificEntry.businessUnit);\n                                        }\n                                        const currentDivision = form.getValues(\"entries.\".concat(entryIndex, \".division\"));\n                                        if (currentDivision !== addressSpecificEntry.customeCode) {\n                                            form.setValue(\"entries.\".concat(entryIndex, \".division\"), addressSpecificEntry.customeCode);\n                                        }\n                                        if (onLegrandDataChange) {\n                                            onLegrandDataChange(entryIndex, addressSpecificEntry.businessUnit || \"\", addressSpecificEntry.customeCode || \"\");\n                                        }\n                                        return;\n                                    }\n                                } else {\n                                    form.setValue(\"entries.\".concat(entryIndex, \".\").concat(fieldPrefix, \"Alias\"), \"\");\n                                }\n                            }\n                        }\n                        const uniqueZipcodes = Array.from(new Set(duplicateEntries.map((entry)=>entry.zipPostal).filter((zip)=>zip)));\n                        if (uniqueZipcodes.length === 1) {\n                            if (currentZipcode !== uniqueZipcodes[0]) {\n                                form.setValue(\"entries.\".concat(entryIndex, \".\").concat(fieldPrefix, \"Zipcode\"), uniqueZipcodes[0]);\n                            }\n                        } else {\n                            if (currentZipcode !== \"\") {\n                                form.setValue(\"entries.\".concat(entryIndex, \".\").concat(fieldPrefix, \"Zipcode\"), \"\");\n                            }\n                        }\n                    } else {\n                        const selectedData = duplicateEntries[0];\n                        if (selectedData) {\n                            const uniqueKey = \"\".concat(selectedData.customeCode, \"-\").concat(selectedData.aliasShippingNames || selectedData.legalName, \"-\").concat(selectedData.shippingBillingAddress);\n                            if (currentAlias !== uniqueKey) {\n                                form.setValue(\"entries.\".concat(entryIndex, \".\").concat(fieldPrefix, \"Alias\"), uniqueKey);\n                            }\n                            if (currentZipcode !== selectedData.zipPostal) {\n                                form.setValue(\"entries.\".concat(entryIndex, \".\").concat(fieldPrefix, \"Zipcode\"), selectedData.zipPostal);\n                            }\n                            const currentCompany = form.getValues(\"entries.\".concat(entryIndex, \".company\"));\n                            if (currentCompany !== selectedData.businessUnit) {\n                                form.setValue(\"entries.\".concat(entryIndex, \".company\"), selectedData.businessUnit);\n                            }\n                            const currentDivision = form.getValues(\"entries.\".concat(entryIndex, \".division\"));\n                            if (currentDivision !== selectedData.customeCode) {\n                                form.setValue(\"entries.\".concat(entryIndex, \".division\"), selectedData.customeCode);\n                            }\n                            if (onLegrandDataChange) {\n                                onLegrandDataChange(entryIndex, selectedData.businessUnit || \"\", selectedData.customeCode || \"\");\n                            }\n                        }\n                    }\n                }\n                if (name === \"entries.\".concat(entryIndex, \".\").concat(fieldPrefix, \"Zipcode\") && currentZipcode) {\n                    const duplicateEntries = checkForDuplicates(\"zipcode\", currentZipcode);\n                    if (duplicateEntries.length > 1) {\n                        if (currentAlias !== \"\") {\n                            form.setValue(\"entries.\".concat(entryIndex, \".\").concat(fieldPrefix, \"Alias\"), \"\");\n                        }\n                        if (currentAddress !== \"\") {\n                            form.setValue(\"entries.\".concat(entryIndex, \".\").concat(fieldPrefix, \"Address\"), \"\");\n                        }\n                    } else {\n                        const selectedData = duplicateEntries[0];\n                        if (selectedData) {\n                            const uniqueKey = \"\".concat(selectedData.customeCode, \"-\").concat(selectedData.aliasShippingNames || selectedData.legalName, \"-\").concat(selectedData.shippingBillingAddress);\n                            if (currentAlias !== uniqueKey) {\n                                form.setValue(\"entries.\".concat(entryIndex, \".\").concat(fieldPrefix, \"Alias\"), uniqueKey);\n                            }\n                            if (currentAddress !== selectedData.shippingBillingAddress) {\n                                form.setValue(\"entries.\".concat(entryIndex, \".\").concat(fieldPrefix, \"Address\"), selectedData.shippingBillingAddress);\n                            }\n                            const currentCompany = form.getValues(\"entries.\".concat(entryIndex, \".company\"));\n                            if (currentCompany !== selectedData.businessUnit) {\n                                form.setValue(\"entries.\".concat(entryIndex, \".company\"), selectedData.businessUnit);\n                            }\n                            const currentDivision = form.getValues(\"entries.\".concat(entryIndex, \".division\"));\n                            if (currentDivision !== selectedData.customeCode) {\n                                form.setValue(\"entries.\".concat(entryIndex, \".division\"), selectedData.customeCode);\n                            }\n                            if (onLegrandDataChange) {\n                                onLegrandDataChange(entryIndex, selectedData.businessUnit || \"\", selectedData.customeCode || \"\");\n                            }\n                        }\n                    }\n                }\n            }, 300); // 300ms debounce\n        });\n        return ()=>{\n            clearTimeout(timeoutId);\n            subscription.unsubscribe();\n        };\n    }, [\n        legrandData,\n        form,\n        entryIndex,\n        onLegrandDataChange,\n        checkForDuplicates,\n        getBaseName,\n        clearOtherBlocks,\n        fieldPrefix\n    ]);\n    const getAddressOptions = ()=>{\n        const currentAlias = form.getValues(\"entries.\".concat(entryIndex, \".\").concat(fieldPrefix, \"Alias\"));\n        const currentZipcode = form.getValues(\"entries.\".concat(entryIndex, \".\").concat(fieldPrefix, \"Zipcode\"));\n        if (currentAlias) {\n            const selectedAliasData = legrandData.find((data)=>{\n                const uniqueKey = \"\".concat(data.customeCode, \"-\").concat(data.aliasShippingNames || data.legalName, \"-\").concat(data.shippingBillingAddress);\n                return uniqueKey === currentAlias;\n            });\n            if (selectedAliasData) {\n                var _aliasOptions_find;\n                const selectedDisplayName = ((_aliasOptions_find = aliasOptions.find((option)=>option.value === currentAlias)) === null || _aliasOptions_find === void 0 ? void 0 : _aliasOptions_find.displayName) || \"\";\n                const sameDisplayNameEntries = legrandData.filter((data)=>{\n                    const location = data.location || extractCityFromAddress(data.shippingBillingAddress || \"\");\n                    const legalName = data.legalName || \"Unknown Company\";\n                    const aliasName = data.aliasShippingNames && data.aliasShippingNames !== \"NONE\" ? data.aliasShippingNames : null;\n                    let displayName = \"\";\n                    if (aliasName) {\n                        displayName = \"\".concat(legalName, \" (\").concat(aliasName, \")\");\n                    } else {\n                        displayName = legalName;\n                    }\n                    const sameNameEntries = legrandData.filter((d)=>getBaseName(d) === displayName);\n                    if (sameNameEntries.length > 1) {\n                        if (location) {\n                            displayName = \"\".concat(displayName, \"_\").concat(location);\n                        } else if (data.zipPostal) {\n                            displayName = \"\".concat(displayName, \"_\").concat(data.zipPostal);\n                        }\n                    }\n                    return displayName === selectedDisplayName;\n                });\n                if (sameDisplayNameEntries.length > 1) {\n                    return sameDisplayNameEntries.map((data)=>data.shippingBillingAddress).filter((address)=>address).filter((address, index, self)=>self.indexOf(address) === index).sort();\n                } else {\n                    return selectedAliasData.shippingBillingAddress ? [\n                        selectedAliasData.shippingBillingAddress\n                    ] : [];\n                }\n            }\n        }\n        if (currentZipcode) {\n            const duplicateEntries = checkForDuplicates(\"zipcode\", currentZipcode);\n            if (duplicateEntries.length > 1) {\n                return duplicateEntries.map((data)=>data.shippingBillingAddress).filter((address)=>address).filter((address, index, self)=>self.indexOf(address) === index).sort();\n            }\n        }\n        const uniqueAddresses = new Set();\n        legrandData.forEach((data)=>{\n            if (data.shippingBillingAddress) {\n                uniqueAddresses.add(data.shippingBillingAddress);\n            }\n        });\n        return Array.from(uniqueAddresses).sort();\n    };\n    const getZipcodeOptions = ()=>{\n        const currentAlias = form.getValues(\"entries.\".concat(entryIndex, \".\").concat(fieldPrefix, \"Alias\"));\n        const currentAddress = form.getValues(\"entries.\".concat(entryIndex, \".\").concat(fieldPrefix, \"Address\"));\n        if (currentAlias) {\n            const selectedAliasData = legrandData.find((data)=>{\n                const uniqueKey = \"\".concat(data.customeCode, \"-\").concat(data.aliasShippingNames || data.legalName, \"-\").concat(data.shippingBillingAddress);\n                return uniqueKey === currentAlias;\n            });\n            if (selectedAliasData) {\n                var _aliasOptions_find;\n                const selectedDisplayName = ((_aliasOptions_find = aliasOptions.find((option)=>option.value === currentAlias)) === null || _aliasOptions_find === void 0 ? void 0 : _aliasOptions_find.displayName) || \"\";\n                const sameDisplayNameEntries = legrandData.filter((data)=>{\n                    const location = data.location || extractCityFromAddress(data.shippingBillingAddress || \"\");\n                    const legalName = data.legalName || \"Unknown Company\";\n                    const aliasName = data.aliasShippingNames && data.aliasShippingNames !== \"NONE\" ? data.aliasShippingNames : null;\n                    let displayName = \"\";\n                    if (aliasName) {\n                        displayName = \"\".concat(legalName, \" (\").concat(aliasName, \")\");\n                    } else {\n                        displayName = legalName;\n                    }\n                    const sameNameEntries = legrandData.filter((d)=>getBaseName(d) === displayName);\n                    if (sameNameEntries.length > 1) {\n                        if (location) {\n                            displayName = \"\".concat(displayName, \"_\").concat(location);\n                        } else if (data.zipPostal) {\n                            displayName = \"\".concat(displayName, \"_\").concat(data.zipPostal);\n                        }\n                    }\n                    return displayName === selectedDisplayName;\n                });\n                if (sameDisplayNameEntries.length > 1) {\n                    return sameDisplayNameEntries.map((data)=>data.zipPostal).filter((zipcode)=>zipcode).filter((zipcode, index, self)=>self.indexOf(zipcode) === index).sort();\n                } else {\n                    return selectedAliasData.zipPostal ? [\n                        selectedAliasData.zipPostal\n                    ] : [];\n                }\n            }\n        }\n        if (currentAddress) {\n            const duplicateEntries = checkForDuplicates(\"address\", currentAddress);\n            if (duplicateEntries.length > 1) {\n                return duplicateEntries.map((data)=>data.zipPostal).filter((zipcode)=>zipcode).filter((zipcode, index, self)=>self.indexOf(zipcode) === index).sort();\n            }\n        }\n        const uniqueZipcodes = new Set();\n        legrandData.forEach((data)=>{\n            if (data.zipPostal) {\n                uniqueZipcodes.add(data.zipPostal);\n            }\n        });\n        return Array.from(uniqueZipcodes).sort();\n    };\n    const getCurrentAliasOptions = ()=>{\n        const currentAddress = form.getValues(\"entries.\".concat(entryIndex, \".\").concat(fieldPrefix, \"Address\"));\n        const currentZipcode = form.getValues(\"entries.\".concat(entryIndex, \".\").concat(fieldPrefix, \"Zipcode\"));\n        if (currentAddress) {\n            const duplicateEntries = checkForDuplicates(\"address\", currentAddress);\n            if (duplicateEntries.length > 1) {\n                return getFilteredAliasOptions();\n            }\n        }\n        if (currentZipcode) {\n            const duplicateEntries = checkForDuplicates(\"zipcode\", currentZipcode);\n            if (duplicateEntries.length > 1) {\n                return getFilteredAliasOptions();\n            }\n        }\n        return aliasOptions;\n    };\n    const getFilteredAliasOptions = ()=>{\n        const currentAddress = form.getValues(\"entries.\".concat(entryIndex, \".\").concat(fieldPrefix, \"Address\"));\n        const currentZipcode = form.getValues(\"entries.\".concat(entryIndex, \".\").concat(fieldPrefix, \"Zipcode\"));\n        let filteredData = legrandData;\n        if (currentAddress) {\n            const duplicateEntries = checkForDuplicates(\"address\", currentAddress);\n            if (duplicateEntries.length > 1) {\n                filteredData = duplicateEntries;\n            }\n        }\n        if (currentZipcode) {\n            const duplicateEntries = checkForDuplicates(\"zipcode\", currentZipcode);\n            if (duplicateEntries.length > 1) {\n                filteredData = filteredData.filter((data)=>duplicateEntries.some((dup)=>dup.id === data.id));\n            }\n        }\n        const options = [];\n        filteredData.forEach((data)=>{\n            const location = data.location || extractCityFromAddress(data.shippingBillingAddress || \"\");\n            const zipcode = data.zipPostal || \"\";\n            let displayName = \"\";\n            let type = \"company\";\n            const legalName = data.legalName || \"Unknown Company\";\n            const aliasName = data.aliasShippingNames && data.aliasShippingNames !== \"NONE\" ? data.aliasShippingNames : null;\n            if (aliasName) {\n                displayName = \"\".concat(legalName, \" (\").concat(aliasName, \")\");\n                type = \"alias\";\n            } else {\n                displayName = legalName;\n                type = \"company\";\n            }\n            const uniqueKey = \"\".concat(data.customeCode, \"-\").concat(data.aliasShippingNames || data.legalName, \"-\").concat(data.shippingBillingAddress);\n            const sameNameEntries = filteredData.filter((d)=>{\n                const dLegalName = d.legalName || \"Unknown Company\";\n                const dAliasName = d.aliasShippingNames && d.aliasShippingNames !== \"NONE\" ? d.aliasShippingNames : null;\n                let dDisplayName = \"\";\n                if (dAliasName) {\n                    dDisplayName = \"\".concat(dLegalName, \" (\").concat(dAliasName, \")\");\n                } else {\n                    dDisplayName = dLegalName;\n                }\n                return dDisplayName === displayName;\n            });\n            let finalDisplayName = displayName;\n            if (sameNameEntries.length > 1) {\n                if (location) {\n                    finalDisplayName = \"\".concat(displayName, \"_\").concat(location);\n                } else if (zipcode) {\n                    finalDisplayName = \"\".concat(displayName, \"_\").concat(zipcode);\n                }\n            }\n            options.push({\n                value: uniqueKey,\n                label: finalDisplayName,\n                type: type,\n                displayName: finalDisplayName\n            });\n        });\n        const uniqueOptions = options.filter((option, index, self)=>index === self.findIndex((o)=>o.value === option.value));\n        return uniqueOptions.sort((a, b)=>a.label.localeCompare(b.label));\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-gray-50 rounded-lg p-3 max-w-sm mb-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-2 mb-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MapPin_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            className: \"w-4 h-4 text-purple-600\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\LegrandDetailsComponent.tsx\",\n                            lineNumber: 715,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-sm font-semibold text-gray-900\",\n                            children: \"LEGRAND Details\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\LegrandDetailsComponent.tsx\",\n                            lineNumber: 716,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\LegrandDetailsComponent.tsx\",\n                    lineNumber: 714,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-sm text-gray-500\",\n                    children: \"Loading LEGRAND data...\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\LegrandDetailsComponent.tsx\",\n                    lineNumber: 718,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\LegrandDetailsComponent.tsx\",\n            lineNumber: 713,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-gray-50 rounded-lg p-3 mb-3 max-w-lg\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-2 mb-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MapPin_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        className: \"w-4 h-4 text-purple-600\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\LegrandDetailsComponent.tsx\",\n                        lineNumber: 726,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-sm font-semibold text-gray-900\",\n                        children: blockTitle\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\LegrandDetailsComponent.tsx\",\n                        lineNumber: 727,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\LegrandDetailsComponent.tsx\",\n                lineNumber: 725,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 gap-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"[&_div[class*='flex'][class*='items-center']]:!h-10 [&_input]:!text-sm [&_label]:!text-sm\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_SearchSelect__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            form: form,\n                            name: \"entries.\".concat(entryIndex, \".\").concat(fieldPrefix, \"Alias\"),\n                            label: \"Alias/Company\",\n                            placeholder: \"Select Alias or Company\",\n                            isEntryPage: true,\n                            className: \"text-sm\",\n                            options: getCurrentAliasOptions().map((option)=>({\n                                    value: option.value,\n                                    label: option.displayName,\n                                    badge: option.type === \"alias\" ? \"Alias\" : \"Company\",\n                                    badgeColor: option.type === \"alias\" ? \"bg-blue-500\" : \"bg-green-500\"\n                                }))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\LegrandDetailsComponent.tsx\",\n                            lineNumber: 734,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\LegrandDetailsComponent.tsx\",\n                        lineNumber: 733,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"[&_div[class*='flex'][class*='items-center']]:!h-10 [&_input]:!text-sm [&_label]:!text-sm\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_SearchSelect__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            form: form,\n                            name: \"entries.\".concat(entryIndex, \".\").concat(fieldPrefix, \"Address\"),\n                            label: \"Address\",\n                            placeholder: \"Select Address\",\n                            isEntryPage: true,\n                            className: \"text-sm\",\n                            options: getAddressOptions().map((address)=>({\n                                    value: address,\n                                    label: address\n                                }))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\LegrandDetailsComponent.tsx\",\n                            lineNumber: 752,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\LegrandDetailsComponent.tsx\",\n                        lineNumber: 751,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"[&_div[class*='flex'][class*='items-center']]:!h-10 [&_input]:!text-sm [&_label]:!text-sm\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_SearchSelect__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            form: form,\n                            name: \"entries.\".concat(entryIndex, \".\").concat(fieldPrefix, \"Zipcode\"),\n                            label: \"Zipcode\",\n                            placeholder: \"Select Zipcode\",\n                            isEntryPage: true,\n                            className: \"text-sm\",\n                            options: getZipcodeOptions().map((zipcode)=>({\n                                    value: zipcode,\n                                    label: zipcode\n                                }))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\LegrandDetailsComponent.tsx\",\n                            lineNumber: 768,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\LegrandDetailsComponent.tsx\",\n                        lineNumber: 767,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\LegrandDetailsComponent.tsx\",\n                lineNumber: 731,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\LegrandDetailsComponent.tsx\",\n        lineNumber: 724,\n        columnNumber: 5\n    }, undefined);\n};\n_s(LegrandDetailsComponent, \"H5bSj8u8Zr1GBtKe3mQTF371UWM=\");\n_c = LegrandDetailsComponent;\n/* harmony default export */ __webpack_exports__[\"default\"] = (LegrandDetailsComponent);\nvar _c;\n$RefreshReg$(_c, \"LegrandDetailsComponent\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/user/trackSheets/LegrandDetailsComponent.tsx\n"));

/***/ })

});