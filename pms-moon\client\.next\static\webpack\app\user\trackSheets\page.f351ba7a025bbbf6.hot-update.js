"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/user/trackSheets/page",{

/***/ "(app-pages-browser)/./app/user/trackSheets/LegrandDetailsComponent.tsx":
/*!**********************************************************!*\
  !*** ./app/user/trackSheets/LegrandDetailsComponent.tsx ***!
  \**********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _app_component_SearchSelect__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/app/_component/SearchSelect */ \"(app-pages-browser)/./app/_component/SearchSelect.tsx\");\n/* harmony import */ var _barrel_optimize_names_MapPin_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=MapPin!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _lib_routePath__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/routePath */ \"(app-pages-browser)/./lib/routePath.ts\");\n/* harmony import */ var _lib_helpers__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/helpers */ \"(app-pages-browser)/./lib/helpers.ts\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react-hot-toast */ \"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst LegrandDetailsComponent = (param)=>{\n    let { form, entryIndex, onLegrandDataChange, blockTitle = \"LEGRAND Details\", fieldPrefix = \"legrand\" } = param;\n    _s();\n    const [legrandData, setLegrandData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const fetchLegrandData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async ()=>{\n        try {\n            setLoading(true);\n            const response = await (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_4__.getAllData)(_lib_routePath__WEBPACK_IMPORTED_MODULE_3__.legrandMapping_routes.GET_LEGRAND_MAPPINGS);\n            setLegrandData(response || []);\n        } catch (error) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_5__[\"default\"].error(\"Error fetching LEGRAND mapping data:\", error);\n            setLegrandData([]);\n        } finally{\n            setLoading(false);\n        }\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchLegrandData();\n    }, [\n        fetchLegrandData\n    ]);\n    const extractCityFromAddress = (address)=>{\n        if (!address) return \"\";\n        const parts = address.split(\",\");\n        if (parts.length >= 2) {\n            const cityPart = parts[parts.length - 2].trim();\n            const city = cityPart.replace(/\\d+/g, \"\").trim();\n            return city;\n        }\n        const words = address.split(\" \");\n        for (const word of words){\n            if (word.length > 2 && !/^\\d+$/.test(word) && !word.includes(\"ROAD\") && !word.includes(\"STREET\") && !word.includes(\"AVE\")) {\n                return word;\n            }\n        }\n        return \"\";\n    };\n    const getAliasOptions = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        if (!legrandData.length) return [];\n        const options = [];\n        // Pre-compute base names to avoid repeated calculations\n        const baseNameMap = new Map();\n        const nameCountMap = new Map();\n        // First pass: compute base names and count occurrences\n        legrandData.forEach((data)=>{\n            const legalName = data.legalName || \"Unknown Company\";\n            const aliasName = data.aliasShippingNames && data.aliasShippingNames !== \"NONE\" ? data.aliasShippingNames : null;\n            const baseName = aliasName ? \"\".concat(legalName, \" (\").concat(aliasName, \")\") : legalName;\n            baseNameMap.set(data.id, baseName);\n            nameCountMap.set(baseName, (nameCountMap.get(baseName) || 0) + 1);\n        });\n        // Second pass: build options with optimized duplicate checking\n        legrandData.forEach((data)=>{\n            const location = data.location || extractCityFromAddress(data.shippingBillingAddress || \"\");\n            const zipcode = data.zipPostal || \"\";\n            const legalName = data.legalName || \"Unknown Company\";\n            const aliasName = data.aliasShippingNames && data.aliasShippingNames !== \"NONE\" ? data.aliasShippingNames : null;\n            let displayName = baseNameMap.get(data.id) || legalName;\n            const type = aliasName ? \"alias\" : \"company\";\n            // Only add location/zipcode suffix if there are duplicates\n            const nameCount = nameCountMap.get(displayName) || 1;\n            if (nameCount > 1) {\n                if (location) {\n                    displayName = \"\".concat(displayName, \"_\").concat(location);\n                } else if (zipcode) {\n                    displayName = \"\".concat(displayName, \"_\").concat(zipcode);\n                }\n            }\n            const uniqueKey = \"\".concat(data.customeCode, \"-\").concat(data.aliasShippingNames || data.legalName, \"-\").concat(data.shippingBillingAddress);\n            options.push({\n                value: uniqueKey,\n                label: displayName,\n                type: type,\n                displayName: displayName\n            });\n        });\n        // Remove duplicates and sort\n        const uniqueOptions = options.filter((option, index, self)=>index === self.findIndex((o)=>o.value === option.value));\n        return uniqueOptions.sort((a, b)=>a.label.localeCompare(b.label));\n    }, [\n        legrandData\n    ]);\n    const getBaseName = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((data)=>{\n        const legalName = data.legalName || \"Unknown Company\";\n        const aliasName = data.aliasShippingNames && data.aliasShippingNames !== \"NONE\" ? data.aliasShippingNames : null;\n        if (aliasName) {\n            return \"\".concat(legalName, \" (\").concat(aliasName, \")\");\n        } else {\n            return legalName;\n        }\n    }, []);\n    const checkForDuplicates = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((field, value)=>{\n        let matchingEntries = [];\n        if (field === \"alias\") {\n            const exactMatch = legrandData.find((data)=>{\n                const uniqueKey = \"\".concat(data.customeCode, \"-\").concat(data.aliasShippingNames || data.legalName, \"-\").concat(data.shippingBillingAddress);\n                return uniqueKey === value;\n            });\n            if (exactMatch) {\n                const baseName = getBaseName(exactMatch);\n                const sameBaseNameEntries = legrandData.filter((data)=>getBaseName(data) === baseName);\n                if (sameBaseNameEntries.length === 1) {\n                    matchingEntries = [\n                        exactMatch\n                    ];\n                } else {\n                    var _getAliasOptions_find;\n                    const selectedDisplayName = ((_getAliasOptions_find = getAliasOptions().find((option)=>option.value === value)) === null || _getAliasOptions_find === void 0 ? void 0 : _getAliasOptions_find.displayName) || \"\";\n                    const sameDisplayNameEntries = legrandData.filter((data)=>{\n                        const location = data.location || extractCityFromAddress(data.shippingBillingAddress || \"\");\n                        let displayName = getBaseName(data);\n                        const sameNameEntries = legrandData.filter((d)=>getBaseName(d) === displayName);\n                        if (sameNameEntries.length > 1) {\n                            if (location) {\n                                displayName = \"\".concat(displayName, \"_\").concat(location);\n                            } else if (data.zipPostal) {\n                                displayName = \"\".concat(displayName, \"_\").concat(data.zipPostal);\n                            }\n                        }\n                        return displayName === selectedDisplayName;\n                    });\n                    matchingEntries = sameDisplayNameEntries;\n                }\n            }\n        } else if (field === \"address\") {\n            matchingEntries = legrandData.filter((data)=>data.shippingBillingAddress === value);\n        } else if (field === \"zipcode\") {\n            matchingEntries = legrandData.filter((data)=>data.zipPostal === value);\n        }\n        return matchingEntries;\n    }, [\n        legrandData,\n        getBaseName,\n        getAliasOptions\n    ]);\n    const clearOtherBlocks = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((currentPrefix)=>{\n        const prefixes = [\n            \"shipper\",\n            \"consignee\",\n            \"billto\"\n        ];\n        const otherPrefixes = prefixes.filter((p)=>p !== currentPrefix);\n        otherPrefixes.forEach((prefix)=>{\n            const currentAlias = form.getValues(\"entries.\".concat(entryIndex, \".\").concat(prefix, \"Alias\"));\n            const currentAddress = form.getValues(\"entries.\".concat(entryIndex, \".\").concat(prefix, \"Address\"));\n            const currentZipcode = form.getValues(\"entries.\".concat(entryIndex, \".\").concat(prefix, \"Zipcode\"));\n            if (currentAlias || currentAddress || currentZipcode) {\n                form.setValue(\"entries.\".concat(entryIndex, \".\").concat(prefix, \"Alias\"), \"\");\n                form.setValue(\"entries.\".concat(entryIndex, \".\").concat(prefix, \"Address\"), \"\");\n                form.setValue(\"entries.\".concat(entryIndex, \".\").concat(prefix, \"Zipcode\"), \"\");\n            }\n        });\n    }, [\n        form,\n        entryIndex\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        if (!legrandData.length) return;\n        const subscription = form.watch((_, param)=>{\n            let { name, type } = param;\n            if (!name || !name.includes(\"entries.\".concat(entryIndex, \".\").concat(fieldPrefix)) || type !== \"change\") return;\n            const currentAlias = form.getValues(\"entries.\".concat(entryIndex, \".\").concat(fieldPrefix, \"Alias\"));\n            const currentAddress = form.getValues(\"entries.\".concat(entryIndex, \".\").concat(fieldPrefix, \"Address\"));\n            const currentZipcode = form.getValues(\"entries.\".concat(entryIndex, \".\").concat(fieldPrefix, \"Zipcode\"));\n            if (currentAlias || currentAddress || currentZipcode) {\n                clearOtherBlocks(fieldPrefix);\n            }\n            if (name === \"entries.\".concat(entryIndex, \".\").concat(fieldPrefix, \"Alias\") && currentAlias) {\n                const selectedData = legrandData.find((data)=>{\n                    const uniqueKey = \"\".concat(data.customeCode, \"-\").concat(data.aliasShippingNames || data.legalName, \"-\").concat(data.shippingBillingAddress);\n                    return uniqueKey === currentAlias;\n                });\n                if (selectedData) {\n                    const duplicateEntries = checkForDuplicates(\"alias\", currentAlias);\n                    if (duplicateEntries.length > 1) {\n                        if (currentAddress) {\n                            const addressMatchesAlias = duplicateEntries.some((entry)=>entry.shippingBillingAddress === currentAddress);\n                            if (addressMatchesAlias) {\n                                const specificEntry = duplicateEntries.find((entry)=>entry.shippingBillingAddress === currentAddress);\n                                if (specificEntry && currentZipcode !== specificEntry.zipPostal) {\n                                    form.setValue(\"entries.\".concat(entryIndex, \".\").concat(fieldPrefix, \"Zipcode\"), specificEntry.zipPostal);\n                                }\n                            } else {\n                                form.setValue(\"entries.\".concat(entryIndex, \".\").concat(fieldPrefix, \"Address\"), \"\");\n                                if (currentZipcode !== \"\") {\n                                    form.setValue(\"entries.\".concat(entryIndex, \".\").concat(fieldPrefix, \"Zipcode\"), \"\");\n                                }\n                            }\n                        } else {\n                            if (currentZipcode) {\n                                const zipcodeMatchesAlias = duplicateEntries.some((entry)=>entry.zipPostal === currentZipcode);\n                                if (zipcodeMatchesAlias) {} else {\n                                    form.setValue(\"entries.\".concat(entryIndex, \".\").concat(fieldPrefix, \"Zipcode\"), \"\");\n                                }\n                            } else {}\n                        }\n                    } else {\n                        if (currentAddress !== selectedData.shippingBillingAddress) {\n                            form.setValue(\"entries.\".concat(entryIndex, \".\").concat(fieldPrefix, \"Address\"), selectedData.shippingBillingAddress);\n                        }\n                        if (currentZipcode !== selectedData.zipPostal) {\n                            form.setValue(\"entries.\".concat(entryIndex, \".\").concat(fieldPrefix, \"Zipcode\"), selectedData.zipPostal);\n                        }\n                    }\n                    const currentCompany = form.getValues(\"entries.\".concat(entryIndex, \".company\"));\n                    if (currentCompany !== selectedData.businessUnit) {\n                        form.setValue(\"entries.\".concat(entryIndex, \".company\"), selectedData.businessUnit);\n                    }\n                    const allDivisions = [];\n                    duplicateEntries.forEach((entry)=>{\n                        if (entry.customeCode) {\n                            if (entry.customeCode.includes(\"/\")) {\n                                const splitDivisions = entry.customeCode.split(\"/\").map((d)=>d.trim());\n                                allDivisions.push(...splitDivisions);\n                            } else {\n                                allDivisions.push(entry.customeCode);\n                            }\n                        }\n                    });\n                    const uniqueDivisions = Array.from(new Set(allDivisions.filter((code)=>code)));\n                    if (uniqueDivisions.length === 1) {\n                        const currentDivision = form.getValues(\"entries.\".concat(entryIndex, \".division\"));\n                        if (currentDivision !== uniqueDivisions[0]) {\n                            form.setValue(\"entries.\".concat(entryIndex, \".division\"), uniqueDivisions[0]);\n                        }\n                    } else {\n                        const currentDivision = form.getValues(\"entries.\".concat(entryIndex, \".division\"));\n                        if (currentDivision !== \"\") {\n                            form.setValue(\"entries.\".concat(entryIndex, \".division\"), \"\");\n                        }\n                    }\n                    if (onLegrandDataChange) {\n                        const divisionCodeToPass = uniqueDivisions.length === 1 ? uniqueDivisions[0] : \"\";\n                        onLegrandDataChange(entryIndex, selectedData.businessUnit || \"\", divisionCodeToPass);\n                    }\n                }\n            }\n            if (name === \"entries.\".concat(entryIndex, \".\").concat(fieldPrefix, \"Address\") && currentAddress) {\n                const duplicateEntries = checkForDuplicates(\"address\", currentAddress);\n                if (duplicateEntries.length > 1) {\n                    if (currentAlias) {\n                        const selectedAliasData = legrandData.find((data)=>{\n                            const uniqueKey = \"\".concat(data.customeCode, \"-\").concat(data.aliasShippingNames || data.legalName, \"-\").concat(data.shippingBillingAddress);\n                            return uniqueKey === currentAlias;\n                        });\n                        if (selectedAliasData) {\n                            const baseName = getBaseName(selectedAliasData);\n                            const addressMatchesBaseName = legrandData.some((data)=>getBaseName(data) === baseName && data.shippingBillingAddress === currentAddress);\n                            if (addressMatchesBaseName) {\n                                const addressSpecificEntry = legrandData.find((data)=>getBaseName(data) === baseName && data.shippingBillingAddress === currentAddress);\n                                if (addressSpecificEntry) {\n                                    const newUniqueKey = \"\".concat(addressSpecificEntry.customeCode, \"-\").concat(addressSpecificEntry.aliasShippingNames || addressSpecificEntry.legalName, \"-\").concat(addressSpecificEntry.shippingBillingAddress);\n                                    if (currentAlias !== newUniqueKey) {\n                                        form.setValue(\"entries.\".concat(entryIndex, \".\").concat(fieldPrefix, \"Alias\"), newUniqueKey);\n                                    }\n                                    if (currentZipcode !== addressSpecificEntry.zipPostal) {\n                                        form.setValue(\"entries.\".concat(entryIndex, \".\").concat(fieldPrefix, \"Zipcode\"), addressSpecificEntry.zipPostal);\n                                    }\n                                    const currentCompany = form.getValues(\"entries.\".concat(entryIndex, \".company\"));\n                                    if (currentCompany !== addressSpecificEntry.businessUnit) {\n                                        form.setValue(\"entries.\".concat(entryIndex, \".company\"), addressSpecificEntry.businessUnit);\n                                    }\n                                    const currentDivision = form.getValues(\"entries.\".concat(entryIndex, \".division\"));\n                                    if (currentDivision !== addressSpecificEntry.customeCode) {\n                                        form.setValue(\"entries.\".concat(entryIndex, \".division\"), addressSpecificEntry.customeCode);\n                                    }\n                                    if (onLegrandDataChange) {\n                                        onLegrandDataChange(entryIndex, addressSpecificEntry.businessUnit || \"\", addressSpecificEntry.customeCode || \"\");\n                                    }\n                                    return;\n                                }\n                            } else {\n                                form.setValue(\"entries.\".concat(entryIndex, \".\").concat(fieldPrefix, \"Alias\"), \"\");\n                            }\n                        }\n                    }\n                    const uniqueZipcodes = Array.from(new Set(duplicateEntries.map((entry)=>entry.zipPostal).filter((zip)=>zip)));\n                    if (uniqueZipcodes.length === 1) {\n                        if (currentZipcode !== uniqueZipcodes[0]) {\n                            form.setValue(\"entries.\".concat(entryIndex, \".\").concat(fieldPrefix, \"Zipcode\"), uniqueZipcodes[0]);\n                        }\n                    } else {\n                        if (currentZipcode !== \"\") {\n                            form.setValue(\"entries.\".concat(entryIndex, \".\").concat(fieldPrefix, \"Zipcode\"), \"\");\n                        }\n                    }\n                } else {\n                    const selectedData = duplicateEntries[0];\n                    if (selectedData) {\n                        const uniqueKey = \"\".concat(selectedData.customeCode, \"-\").concat(selectedData.aliasShippingNames || selectedData.legalName, \"-\").concat(selectedData.shippingBillingAddress);\n                        if (currentAlias !== uniqueKey) {\n                            form.setValue(\"entries.\".concat(entryIndex, \".\").concat(fieldPrefix, \"Alias\"), uniqueKey);\n                        }\n                        if (currentZipcode !== selectedData.zipPostal) {\n                            form.setValue(\"entries.\".concat(entryIndex, \".\").concat(fieldPrefix, \"Zipcode\"), selectedData.zipPostal);\n                        }\n                        const currentCompany = form.getValues(\"entries.\".concat(entryIndex, \".company\"));\n                        if (currentCompany !== selectedData.businessUnit) {\n                            form.setValue(\"entries.\".concat(entryIndex, \".company\"), selectedData.businessUnit);\n                        }\n                        const currentDivision = form.getValues(\"entries.\".concat(entryIndex, \".division\"));\n                        if (currentDivision !== selectedData.customeCode) {\n                            form.setValue(\"entries.\".concat(entryIndex, \".division\"), selectedData.customeCode);\n                        }\n                        if (onLegrandDataChange) {\n                            onLegrandDataChange(entryIndex, selectedData.businessUnit || \"\", selectedData.customeCode || \"\");\n                        }\n                    }\n                }\n            }\n            if (name === \"entries.\".concat(entryIndex, \".\").concat(fieldPrefix, \"Zipcode\") && currentZipcode) {\n                const duplicateEntries = checkForDuplicates(\"zipcode\", currentZipcode);\n                if (duplicateEntries.length > 1) {\n                    if (currentAlias !== \"\") {\n                        form.setValue(\"entries.\".concat(entryIndex, \".\").concat(fieldPrefix, \"Alias\"), \"\");\n                    }\n                    if (currentAddress !== \"\") {\n                        form.setValue(\"entries.\".concat(entryIndex, \".\").concat(fieldPrefix, \"Address\"), \"\");\n                    }\n                } else {\n                    const selectedData = duplicateEntries[0];\n                    if (selectedData) {\n                        const uniqueKey = \"\".concat(selectedData.customeCode, \"-\").concat(selectedData.aliasShippingNames || selectedData.legalName, \"-\").concat(selectedData.shippingBillingAddress);\n                        if (currentAlias !== uniqueKey) {\n                            form.setValue(\"entries.\".concat(entryIndex, \".\").concat(fieldPrefix, \"Alias\"), uniqueKey);\n                        }\n                        if (currentAddress !== selectedData.shippingBillingAddress) {\n                            form.setValue(\"entries.\".concat(entryIndex, \".\").concat(fieldPrefix, \"Address\"), selectedData.shippingBillingAddress);\n                        }\n                        const currentCompany = form.getValues(\"entries.\".concat(entryIndex, \".company\"));\n                        if (currentCompany !== selectedData.businessUnit) {\n                            form.setValue(\"entries.\".concat(entryIndex, \".company\"), selectedData.businessUnit);\n                        }\n                        const currentDivision = form.getValues(\"entries.\".concat(entryIndex, \".division\"));\n                        if (currentDivision !== selectedData.customeCode) {\n                            form.setValue(\"entries.\".concat(entryIndex, \".division\"), selectedData.customeCode);\n                        }\n                        if (onLegrandDataChange) {\n                            onLegrandDataChange(entryIndex, selectedData.businessUnit || \"\", selectedData.customeCode || \"\");\n                        }\n                    }\n                }\n            }\n        });\n        return ()=>subscription.unsubscribe();\n    }, [\n        legrandData,\n        form,\n        entryIndex,\n        onLegrandDataChange,\n        checkForDuplicates,\n        getBaseName,\n        clearOtherBlocks,\n        fieldPrefix\n    ]);\n    const getAddressOptions = ()=>{\n        const currentAlias = form.getValues(\"entries.\".concat(entryIndex, \".\").concat(fieldPrefix, \"Alias\"));\n        const currentZipcode = form.getValues(\"entries.\".concat(entryIndex, \".\").concat(fieldPrefix, \"Zipcode\"));\n        if (currentAlias) {\n            const selectedAliasData = legrandData.find((data)=>{\n                const uniqueKey = \"\".concat(data.customeCode, \"-\").concat(data.aliasShippingNames || data.legalName, \"-\").concat(data.shippingBillingAddress);\n                return uniqueKey === currentAlias;\n            });\n            if (selectedAliasData) {\n                var _getAliasOptions_find;\n                const selectedDisplayName = ((_getAliasOptions_find = getAliasOptions().find((option)=>option.value === currentAlias)) === null || _getAliasOptions_find === void 0 ? void 0 : _getAliasOptions_find.displayName) || \"\";\n                const sameDisplayNameEntries = legrandData.filter((data)=>{\n                    const location = data.location || extractCityFromAddress(data.shippingBillingAddress || \"\");\n                    const zipcode = data.zipPostal || \"\";\n                    const legalName = data.legalName || \"Unknown Company\";\n                    const aliasName = data.aliasShippingNames && data.aliasShippingNames !== \"NONE\" ? data.aliasShippingNames : null;\n                    let displayName = \"\";\n                    if (aliasName) {\n                        displayName = \"\".concat(legalName, \" (\").concat(aliasName, \")\");\n                    } else {\n                        displayName = legalName;\n                    }\n                    const baseName = displayName;\n                    const sameNameEntries = legrandData.filter((d)=>getBaseName(d) === displayName);\n                    if (sameNameEntries.length > 1) {\n                        if (location) {\n                            displayName = \"\".concat(displayName, \"_\").concat(location);\n                        } else if (data.zipPostal) {\n                            displayName = \"\".concat(displayName, \"_\").concat(data.zipPostal);\n                        }\n                    }\n                    return displayName === selectedDisplayName;\n                });\n                if (sameDisplayNameEntries.length > 1) {\n                    return sameDisplayNameEntries.map((data)=>data.shippingBillingAddress).filter((address)=>address).filter((address, index, self)=>self.indexOf(address) === index).sort();\n                } else {\n                    return selectedAliasData.shippingBillingAddress ? [\n                        selectedAliasData.shippingBillingAddress\n                    ] : [];\n                }\n            }\n        }\n        if (currentZipcode) {\n            const duplicateEntries = checkForDuplicates(\"zipcode\", currentZipcode);\n            if (duplicateEntries.length > 1) {\n                return duplicateEntries.map((data)=>data.shippingBillingAddress).filter((address)=>address).filter((address, index, self)=>self.indexOf(address) === index).sort();\n            }\n        }\n        const uniqueAddresses = new Set();\n        legrandData.forEach((data)=>{\n            if (data.shippingBillingAddress) {\n                uniqueAddresses.add(data.shippingBillingAddress);\n            }\n        });\n        return Array.from(uniqueAddresses).sort();\n    };\n    const getZipcodeOptions = ()=>{\n        const currentAlias = form.getValues(\"entries.\".concat(entryIndex, \".\").concat(fieldPrefix, \"Alias\"));\n        const currentAddress = form.getValues(\"entries.\".concat(entryIndex, \".\").concat(fieldPrefix, \"Address\"));\n        if (currentAlias) {\n            const selectedAliasData = legrandData.find((data)=>{\n                const uniqueKey = \"\".concat(data.customeCode, \"-\").concat(data.aliasShippingNames || data.legalName, \"-\").concat(data.shippingBillingAddress);\n                return uniqueKey === currentAlias;\n            });\n            if (selectedAliasData) {\n                var _getAliasOptions_find;\n                const selectedDisplayName = ((_getAliasOptions_find = getAliasOptions().find((option)=>option.value === currentAlias)) === null || _getAliasOptions_find === void 0 ? void 0 : _getAliasOptions_find.displayName) || \"\";\n                const sameDisplayNameEntries = legrandData.filter((data)=>{\n                    const location = data.location || extractCityFromAddress(data.shippingBillingAddress || \"\");\n                    const zipcode = data.zipPostal || \"\";\n                    const legalName = data.legalName || \"Unknown Company\";\n                    const aliasName = data.aliasShippingNames && data.aliasShippingNames !== \"NONE\" ? data.aliasShippingNames : null;\n                    let displayName = \"\";\n                    if (aliasName) {\n                        displayName = \"\".concat(legalName, \" (\").concat(aliasName, \")\");\n                    } else {\n                        displayName = legalName;\n                    }\n                    const sameNameEntries = legrandData.filter((d)=>getBaseName(d) === displayName);\n                    if (sameNameEntries.length > 1) {\n                        if (location) {\n                            displayName = \"\".concat(displayName, \"_\").concat(location);\n                        } else if (data.zipPostal) {\n                            displayName = \"\".concat(displayName, \"_\").concat(data.zipPostal);\n                        }\n                    }\n                    return displayName === selectedDisplayName;\n                });\n                if (sameDisplayNameEntries.length > 1) {\n                    return sameDisplayNameEntries.map((data)=>data.zipPostal).filter((zipcode)=>zipcode).filter((zipcode, index, self)=>self.indexOf(zipcode) === index).sort();\n                } else {\n                    return selectedAliasData.zipPostal ? [\n                        selectedAliasData.zipPostal\n                    ] : [];\n                }\n            }\n        }\n        if (currentAddress) {\n            const duplicateEntries = checkForDuplicates(\"address\", currentAddress);\n            if (duplicateEntries.length > 1) {\n                return duplicateEntries.map((data)=>data.zipPostal).filter((zipcode)=>zipcode).filter((zipcode, index, self)=>self.indexOf(zipcode) === index).sort();\n            }\n        }\n        const uniqueZipcodes = new Set();\n        legrandData.forEach((data)=>{\n            if (data.zipPostal) {\n                uniqueZipcodes.add(data.zipPostal);\n            }\n        });\n        return Array.from(uniqueZipcodes).sort();\n    };\n    const getCurrentAliasOptions = ()=>{\n        const currentAddress = form.getValues(\"entries.\".concat(entryIndex, \".\").concat(fieldPrefix, \"Address\"));\n        const currentZipcode = form.getValues(\"entries.\".concat(entryIndex, \".\").concat(fieldPrefix, \"Zipcode\"));\n        if (currentAddress) {\n            const duplicateEntries = checkForDuplicates(\"address\", currentAddress);\n            if (duplicateEntries.length > 1) {\n                return getFilteredAliasOptions();\n            }\n        }\n        if (currentZipcode) {\n            const duplicateEntries = checkForDuplicates(\"zipcode\", currentZipcode);\n            if (duplicateEntries.length > 1) {\n                return getFilteredAliasOptions();\n            }\n        }\n        return getAliasOptions();\n    };\n    const getFilteredAliasOptions = ()=>{\n        const currentAddress = form.getValues(\"entries.\".concat(entryIndex, \".\").concat(fieldPrefix, \"Address\"));\n        const currentZipcode = form.getValues(\"entries.\".concat(entryIndex, \".\").concat(fieldPrefix, \"Zipcode\"));\n        let filteredData = legrandData;\n        if (currentAddress) {\n            const duplicateEntries = checkForDuplicates(\"address\", currentAddress);\n            if (duplicateEntries.length > 1) {\n                filteredData = duplicateEntries;\n            }\n        }\n        if (currentZipcode) {\n            const duplicateEntries = checkForDuplicates(\"zipcode\", currentZipcode);\n            if (duplicateEntries.length > 1) {\n                filteredData = filteredData.filter((data)=>duplicateEntries.some((dup)=>dup.id === data.id));\n            }\n        }\n        const options = [];\n        filteredData.forEach((data)=>{\n            const location = data.location || extractCityFromAddress(data.shippingBillingAddress || \"\");\n            const zipcode = data.zipPostal || \"\";\n            let displayName = \"\";\n            let type = \"company\";\n            const legalName = data.legalName || \"Unknown Company\";\n            const aliasName = data.aliasShippingNames && data.aliasShippingNames !== \"NONE\" ? data.aliasShippingNames : null;\n            if (aliasName) {\n                displayName = \"\".concat(legalName, \" (\").concat(aliasName, \")\");\n                type = \"alias\";\n            } else {\n                displayName = legalName;\n                type = \"company\";\n            }\n            const uniqueKey = \"\".concat(data.customeCode, \"-\").concat(data.aliasShippingNames || data.legalName, \"-\").concat(data.shippingBillingAddress);\n            const sameNameEntries = filteredData.filter((d)=>{\n                const dLegalName = d.legalName || \"Unknown Company\";\n                const dAliasName = d.aliasShippingNames && d.aliasShippingNames !== \"NONE\" ? d.aliasShippingNames : null;\n                let dDisplayName = \"\";\n                if (dAliasName) {\n                    dDisplayName = \"\".concat(dLegalName, \" (\").concat(dAliasName, \")\");\n                } else {\n                    dDisplayName = dLegalName;\n                }\n                return dDisplayName === displayName;\n            });\n            let finalDisplayName = displayName;\n            if (sameNameEntries.length > 1) {\n                if (location) {\n                    finalDisplayName = \"\".concat(displayName, \"_\").concat(location);\n                } else if (zipcode) {\n                    finalDisplayName = \"\".concat(displayName, \"_\").concat(zipcode);\n                }\n            }\n            options.push({\n                value: uniqueKey,\n                label: finalDisplayName,\n                type: type,\n                displayName: finalDisplayName\n            });\n        });\n        const uniqueOptions = options.filter((option, index, self)=>index === self.findIndex((o)=>o.value === option.value));\n        return uniqueOptions.sort((a, b)=>a.label.localeCompare(b.label));\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-gray-50 rounded-lg p-3 max-w-sm mb-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-2 mb-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MapPin_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            className: \"w-4 h-4 text-purple-600\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\LegrandDetailsComponent.tsx\",\n                            lineNumber: 703,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-sm font-semibold text-gray-900\",\n                            children: \"LEGRAND Details\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\LegrandDetailsComponent.tsx\",\n                            lineNumber: 704,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\LegrandDetailsComponent.tsx\",\n                    lineNumber: 702,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-sm text-gray-500\",\n                    children: \"Loading LEGRAND data...\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\LegrandDetailsComponent.tsx\",\n                    lineNumber: 706,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\LegrandDetailsComponent.tsx\",\n            lineNumber: 701,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-gray-50 rounded-lg p-3 mb-3 max-w-lg\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-2 mb-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MapPin_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        className: \"w-4 h-4 text-purple-600\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\LegrandDetailsComponent.tsx\",\n                        lineNumber: 714,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-sm font-semibold text-gray-900\",\n                        children: blockTitle\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\LegrandDetailsComponent.tsx\",\n                        lineNumber: 715,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\LegrandDetailsComponent.tsx\",\n                lineNumber: 713,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 gap-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"[&_div[class*='flex'][class*='items-center']]:!h-10 [&_input]:!text-sm [&_label]:!text-sm\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_SearchSelect__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            form: form,\n                            name: \"entries.\".concat(entryIndex, \".\").concat(fieldPrefix, \"Alias\"),\n                            label: \"Alias/Company\",\n                            placeholder: \"Select Alias or Company\",\n                            isEntryPage: true,\n                            className: \"text-sm\",\n                            options: getCurrentAliasOptions().map((option)=>({\n                                    value: option.value,\n                                    label: option.displayName,\n                                    badge: option.type === \"alias\" ? \"Alias\" : \"Company\",\n                                    badgeColor: option.type === \"alias\" ? \"bg-blue-500\" : \"bg-green-500\"\n                                }))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\LegrandDetailsComponent.tsx\",\n                            lineNumber: 722,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\LegrandDetailsComponent.tsx\",\n                        lineNumber: 721,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"[&_div[class*='flex'][class*='items-center']]:!h-10 [&_input]:!text-sm [&_label]:!text-sm\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_SearchSelect__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            form: form,\n                            name: \"entries.\".concat(entryIndex, \".\").concat(fieldPrefix, \"Address\"),\n                            label: \"Address\",\n                            placeholder: \"Select Address\",\n                            isEntryPage: true,\n                            className: \"text-sm\",\n                            options: getAddressOptions().map((address)=>({\n                                    value: address,\n                                    label: address\n                                }))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\LegrandDetailsComponent.tsx\",\n                            lineNumber: 740,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\LegrandDetailsComponent.tsx\",\n                        lineNumber: 739,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"[&_div[class*='flex'][class*='items-center']]:!h-10 [&_input]:!text-sm [&_label]:!text-sm\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_SearchSelect__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            form: form,\n                            name: \"entries.\".concat(entryIndex, \".\").concat(fieldPrefix, \"Zipcode\"),\n                            label: \"Zipcode\",\n                            placeholder: \"Select Zipcode\",\n                            isEntryPage: true,\n                            className: \"text-sm\",\n                            options: getZipcodeOptions().map((zipcode)=>({\n                                    value: zipcode,\n                                    label: zipcode\n                                }))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\LegrandDetailsComponent.tsx\",\n                            lineNumber: 756,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\LegrandDetailsComponent.tsx\",\n                        lineNumber: 755,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\LegrandDetailsComponent.tsx\",\n                lineNumber: 719,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\LegrandDetailsComponent.tsx\",\n        lineNumber: 712,\n        columnNumber: 5\n    }, undefined);\n};\n_s(LegrandDetailsComponent, \"v1XeI2k9nAJ9lUHQeiOcGbzqFrw=\");\n_c = LegrandDetailsComponent;\n/* harmony default export */ __webpack_exports__[\"default\"] = (LegrandDetailsComponent);\nvar _c;\n$RefreshReg$(_c, \"LegrandDetailsComponent\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2FwcC91c2VyL3RyYWNrU2hlZXRzL0xlZ3JhbmREZXRhaWxzQ29tcG9uZW50LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQUF5RTtBQUVoQjtBQUNuQjtBQUNrQjtBQUNiO0FBQ1A7QUEwQnBDLE1BQU1VLDBCQUFrRTtRQUFDLEVBQ3ZFQyxJQUFJLEVBQ0pDLFVBQVUsRUFDVkMsbUJBQW1CLEVBQ25CQyxhQUFhLGlCQUFpQixFQUM5QkMsY0FBYyxTQUFTLEVBQ3hCOztJQUNDLE1BQU0sQ0FBQ0MsYUFBYUMsZUFBZSxHQUFHaEIsK0NBQVFBLENBQXVCLEVBQUU7SUFDdkUsTUFBTSxDQUFDaUIsU0FBU0MsV0FBVyxHQUFHbEIsK0NBQVFBLENBQUM7SUFFdkMsTUFBTW1CLG1CQUFtQmxCLGtEQUFXQSxDQUFDO1FBQ25DLElBQUk7WUFDRmlCLFdBQVc7WUFDWCxNQUFNRSxXQUFXLE1BQU1iLHdEQUFVQSxDQUFDRCxpRUFBcUJBLENBQUNlLG9CQUFvQjtZQUM1RUwsZUFBZUksWUFBWSxFQUFFO1FBQy9CLEVBQUUsT0FBT0UsT0FBTztZQUNkZCx1REFBS0EsQ0FBQ2MsS0FBSyxDQUFDLHdDQUF3Q0E7WUFDcEROLGVBQWUsRUFBRTtRQUNuQixTQUFVO1lBQ1JFLFdBQVc7UUFDYjtJQUNGLEdBQUcsRUFBRTtJQUVMZixnREFBU0EsQ0FBQztRQUNSZ0I7SUFDRixHQUFHO1FBQUNBO0tBQWlCO0lBRXJCLE1BQU1JLHlCQUF5QixDQUFDQztRQUM5QixJQUFJLENBQUNBLFNBQVMsT0FBTztRQUVyQixNQUFNQyxRQUFRRCxRQUFRRSxLQUFLLENBQUM7UUFDNUIsSUFBSUQsTUFBTUUsTUFBTSxJQUFJLEdBQUc7WUFDckIsTUFBTUMsV0FBV0gsS0FBSyxDQUFDQSxNQUFNRSxNQUFNLEdBQUcsRUFBRSxDQUFDRSxJQUFJO1lBQzdDLE1BQU1DLE9BQU9GLFNBQVNHLE9BQU8sQ0FBQyxRQUFRLElBQUlGLElBQUk7WUFDOUMsT0FBT0M7UUFDVDtRQUVBLE1BQU1FLFFBQVFSLFFBQVFFLEtBQUssQ0FBQztRQUM1QixLQUFLLE1BQU1PLFFBQVFELE1BQU87WUFDeEIsSUFBSUMsS0FBS04sTUFBTSxHQUFHLEtBQUssQ0FBQyxRQUFRTyxJQUFJLENBQUNELFNBQVMsQ0FBQ0EsS0FBS0UsUUFBUSxDQUFDLFdBQVcsQ0FBQ0YsS0FBS0UsUUFBUSxDQUFDLGFBQWEsQ0FBQ0YsS0FBS0UsUUFBUSxDQUFDLFFBQVE7Z0JBQ3pILE9BQU9GO1lBQ1Q7UUFDRjtRQUVBLE9BQU87SUFDVDtJQUVBLE1BQU1HLGtCQUFrQmxDLDhDQUFPQSxDQUFDO1FBQzlCLElBQUksQ0FBQ2EsWUFBWVksTUFBTSxFQUFFLE9BQU8sRUFBRTtRQUVsQyxNQUFNVSxVQUtELEVBQUU7UUFFUCx3REFBd0Q7UUFDeEQsTUFBTUMsY0FBYyxJQUFJQztRQUN4QixNQUFNQyxlQUFlLElBQUlEO1FBRXpCLHVEQUF1RDtRQUN2RHhCLFlBQVkwQixPQUFPLENBQUNDLENBQUFBO1lBQ2xCLE1BQU1DLFlBQVlELEtBQUtDLFNBQVMsSUFBSTtZQUNwQyxNQUFNQyxZQUFZRixLQUFLRyxrQkFBa0IsSUFBSUgsS0FBS0csa0JBQWtCLEtBQUssU0FBU0gsS0FBS0csa0JBQWtCLEdBQUc7WUFDNUcsTUFBTUMsV0FBV0YsWUFBWSxHQUFpQkEsT0FBZEQsV0FBVSxNQUFjLE9BQVZDLFdBQVUsT0FBS0Q7WUFFN0RMLFlBQVlTLEdBQUcsQ0FBQ0wsS0FBS00sRUFBRSxFQUFFRjtZQUN6Qk4sYUFBYU8sR0FBRyxDQUFDRCxVQUFVLENBQUNOLGFBQWFTLEdBQUcsQ0FBQ0gsYUFBYSxLQUFLO1FBQ2pFO1FBRUEsK0RBQStEO1FBQy9EL0IsWUFBWTBCLE9BQU8sQ0FBQ0MsQ0FBQUE7WUFDbEIsTUFBTVEsV0FBV1IsS0FBS1EsUUFBUSxJQUFJM0IsdUJBQXVCbUIsS0FBS1Msc0JBQXNCLElBQUk7WUFDeEYsTUFBTUMsVUFBVVYsS0FBS1csU0FBUyxJQUFJO1lBQ2xDLE1BQU1WLFlBQVlELEtBQUtDLFNBQVMsSUFBSTtZQUNwQyxNQUFNQyxZQUFZRixLQUFLRyxrQkFBa0IsSUFBSUgsS0FBS0csa0JBQWtCLEtBQUssU0FBU0gsS0FBS0csa0JBQWtCLEdBQUc7WUFFNUcsSUFBSVMsY0FBY2hCLFlBQVlXLEdBQUcsQ0FBQ1AsS0FBS00sRUFBRSxLQUFLTDtZQUM5QyxNQUFNWSxPQUE0QlgsWUFBWSxVQUFVO1lBRXhELDJEQUEyRDtZQUMzRCxNQUFNWSxZQUFZaEIsYUFBYVMsR0FBRyxDQUFDSyxnQkFBZ0I7WUFDbkQsSUFBSUUsWUFBWSxHQUFHO2dCQUNqQixJQUFJTixVQUFVO29CQUNaSSxjQUFjLEdBQWtCSixPQUFmSSxhQUFZLEtBQVksT0FBVEo7Z0JBQ2xDLE9BQU8sSUFBSUUsU0FBUztvQkFDbEJFLGNBQWMsR0FBa0JGLE9BQWZFLGFBQVksS0FBVyxPQUFSRjtnQkFDbEM7WUFDRjtZQUVBLE1BQU1LLFlBQVksR0FBdUJmLE9BQXBCQSxLQUFLZ0IsV0FBVyxFQUFDLEtBQWdEaEIsT0FBN0NBLEtBQUtHLGtCQUFrQixJQUFJSCxLQUFLQyxTQUFTLEVBQUMsS0FBK0IsT0FBNUJELEtBQUtTLHNCQUFzQjtZQUVqSGQsUUFBUXNCLElBQUksQ0FBQztnQkFDWEMsT0FBT0g7Z0JBQ1BJLE9BQU9QO2dCQUNQQyxNQUFNQTtnQkFDTkQsYUFBYUE7WUFDZjtRQUNGO1FBRUEsNkJBQTZCO1FBQzdCLE1BQU1RLGdCQUFnQnpCLFFBQVEwQixNQUFNLENBQUMsQ0FBQ0MsUUFBUUMsT0FBT0MsT0FDbkRELFVBQVVDLEtBQUtDLFNBQVMsQ0FBQ0MsQ0FBQUEsSUFBS0EsRUFBRVIsS0FBSyxLQUFLSSxPQUFPSixLQUFLO1FBR3hELE9BQU9FLGNBQWNPLElBQUksQ0FBQyxDQUFDQyxHQUFHQyxJQUFNRCxFQUFFVCxLQUFLLENBQUNXLGFBQWEsQ0FBQ0QsRUFBRVYsS0FBSztJQUNuRSxHQUFHO1FBQUM5QztLQUFZO0lBRWhCLE1BQU0wRCxjQUFjeEUsa0RBQVdBLENBQUMsQ0FBQ3lDO1FBQy9CLE1BQU1DLFlBQVlELEtBQUtDLFNBQVMsSUFBSTtRQUNwQyxNQUFNQyxZQUFZRixLQUFLRyxrQkFBa0IsSUFBSUgsS0FBS0csa0JBQWtCLEtBQUssU0FBU0gsS0FBS0csa0JBQWtCLEdBQUc7UUFFNUcsSUFBSUQsV0FBVztZQUNiLE9BQU8sR0FBaUJBLE9BQWRELFdBQVUsTUFBYyxPQUFWQyxXQUFVO1FBQ3BDLE9BQU87WUFDTCxPQUFPRDtRQUNUO0lBQ0YsR0FBRyxFQUFFO0lBRUwsTUFBTStCLHFCQUFxQnpFLGtEQUFXQSxDQUFDLENBQUMwRSxPQUF3Q2Y7UUFDOUUsSUFBSWdCLGtCQUF3QyxFQUFFO1FBRTlDLElBQUlELFVBQVUsU0FBUztZQUNyQixNQUFNRSxhQUFhOUQsWUFBWStELElBQUksQ0FBQ3BDLENBQUFBO2dCQUNsQyxNQUFNZSxZQUFZLEdBQXVCZixPQUFwQkEsS0FBS2dCLFdBQVcsRUFBQyxLQUFnRGhCLE9BQTdDQSxLQUFLRyxrQkFBa0IsSUFBSUgsS0FBS0MsU0FBUyxFQUFDLEtBQStCLE9BQTVCRCxLQUFLUyxzQkFBc0I7Z0JBQ2pILE9BQU9NLGNBQWNHO1lBQ3ZCO1lBRUEsSUFBSWlCLFlBQVk7Z0JBQ2QsTUFBTS9CLFdBQVcyQixZQUFZSTtnQkFDN0IsTUFBTUUsc0JBQXNCaEUsWUFBWWdELE1BQU0sQ0FBQ3JCLENBQUFBLE9BQVErQixZQUFZL0IsVUFBVUk7Z0JBRTdFLElBQUlpQyxvQkFBb0JwRCxNQUFNLEtBQUssR0FBRztvQkFDcENpRCxrQkFBa0I7d0JBQUNDO3FCQUFXO2dCQUNoQyxPQUFPO3dCQUN1QnpDO29CQUE1QixNQUFNNEMsc0JBQXNCNUMsRUFBQUEsd0JBQUFBLGtCQUFrQjBDLElBQUksQ0FBQ2QsQ0FBQUEsU0FBVUEsT0FBT0osS0FBSyxLQUFLQSxvQkFBbER4Qiw0Q0FBQUEsc0JBQTBEa0IsV0FBVyxLQUFJO29CQUVyRyxNQUFNMkIseUJBQXlCbEUsWUFBWWdELE1BQU0sQ0FBQ3JCLENBQUFBO3dCQUNoRCxNQUFNUSxXQUFXUixLQUFLUSxRQUFRLElBQUkzQix1QkFBdUJtQixLQUFLUyxzQkFBc0IsSUFBSTt3QkFDeEYsSUFBSUcsY0FBY21CLFlBQVkvQjt3QkFFOUIsTUFBTXdDLGtCQUFrQm5FLFlBQVlnRCxNQUFNLENBQUNvQixDQUFBQSxJQUFLVixZQUFZVSxPQUFPN0I7d0JBQ25FLElBQUk0QixnQkFBZ0J2RCxNQUFNLEdBQUcsR0FBRzs0QkFDOUIsSUFBSXVCLFVBQVU7Z0NBQ1pJLGNBQWMsR0FBa0JKLE9BQWZJLGFBQVksS0FBWSxPQUFUSjs0QkFDbEMsT0FBTyxJQUFJUixLQUFLVyxTQUFTLEVBQUU7Z0NBQ3pCQyxjQUFjLEdBQWtCWixPQUFmWSxhQUFZLEtBQWtCLE9BQWZaLEtBQUtXLFNBQVM7NEJBQ2hEO3dCQUNGO3dCQUVBLE9BQU9DLGdCQUFnQjBCO29CQUN6QjtvQkFFQUosa0JBQWtCSztnQkFDcEI7WUFDRjtRQUNGLE9BQU8sSUFBSU4sVUFBVSxXQUFXO1lBQzlCQyxrQkFBa0I3RCxZQUFZZ0QsTUFBTSxDQUFDckIsQ0FBQUEsT0FBUUEsS0FBS1Msc0JBQXNCLEtBQUtTO1FBQy9FLE9BQU8sSUFBSWUsVUFBVSxXQUFXO1lBQzlCQyxrQkFBa0I3RCxZQUFZZ0QsTUFBTSxDQUFDckIsQ0FBQUEsT0FBUUEsS0FBS1csU0FBUyxLQUFLTztRQUNsRTtRQUVBLE9BQU9nQjtJQUNULEdBQUc7UUFBQzdEO1FBQWEwRDtRQUFhckM7S0FBZ0I7SUFFOUMsTUFBTWdELG1CQUFtQm5GLGtEQUFXQSxDQUFDLENBQUNvRjtRQUNwQyxNQUFNQyxXQUFXO1lBQUM7WUFBVztZQUFhO1NBQVM7UUFDbkQsTUFBTUMsZ0JBQWdCRCxTQUFTdkIsTUFBTSxDQUFDeUIsQ0FBQUEsSUFBS0EsTUFBTUg7UUFFakRFLGNBQWM5QyxPQUFPLENBQUNnRCxDQUFBQTtZQUNwQixNQUFNQyxlQUFlaEYsS0FBS2lGLFNBQVMsQ0FBQyxXQUF5QkYsT0FBZDlFLFlBQVcsS0FBVSxPQUFQOEUsUUFBTztZQUNwRSxNQUFNRyxpQkFBaUJsRixLQUFLaUYsU0FBUyxDQUFDLFdBQXlCRixPQUFkOUUsWUFBVyxLQUFVLE9BQVA4RSxRQUFPO1lBQ3RFLE1BQU1JLGlCQUFpQm5GLEtBQUtpRixTQUFTLENBQUMsV0FBeUJGLE9BQWQ5RSxZQUFXLEtBQVUsT0FBUDhFLFFBQU87WUFFdEUsSUFBSUMsZ0JBQWdCRSxrQkFBa0JDLGdCQUFnQjtnQkFDcERuRixLQUFLb0YsUUFBUSxDQUFDLFdBQXlCTCxPQUFkOUUsWUFBVyxLQUFVLE9BQVA4RSxRQUFPLFVBQVE7Z0JBQ3REL0UsS0FBS29GLFFBQVEsQ0FBQyxXQUF5QkwsT0FBZDlFLFlBQVcsS0FBVSxPQUFQOEUsUUFBTyxZQUFVO2dCQUN4RC9FLEtBQUtvRixRQUFRLENBQUMsV0FBeUJMLE9BQWQ5RSxZQUFXLEtBQVUsT0FBUDhFLFFBQU8sWUFBVTtZQUMxRDtRQUNGO0lBQ0YsR0FBRztRQUFDL0U7UUFBTUM7S0FBVztJQUVyQlQsOENBQU9BLENBQUM7UUFDTixJQUFJLENBQUNhLFlBQVlZLE1BQU0sRUFBRTtRQUV6QixNQUFNb0UsZUFBZXJGLEtBQUtzRixLQUFLLENBQUMsQ0FBQ0M7Z0JBQUcsRUFBRUMsSUFBSSxFQUFFM0MsSUFBSSxFQUFFO1lBQ2hELElBQUksQ0FBQzJDLFFBQVEsQ0FBQ0EsS0FBSy9ELFFBQVEsQ0FBQyxXQUF5QnJCLE9BQWRILFlBQVcsS0FBZSxPQUFaRyxpQkFBa0J5QyxTQUFTLFVBQVU7WUFFMUYsTUFBTW1DLGVBQWVoRixLQUFLaUYsU0FBUyxDQUFDLFdBQXlCN0UsT0FBZEgsWUFBVyxLQUFlLE9BQVpHLGFBQVk7WUFDekUsTUFBTThFLGlCQUFpQmxGLEtBQUtpRixTQUFTLENBQUMsV0FBeUI3RSxPQUFkSCxZQUFXLEtBQWUsT0FBWkcsYUFBWTtZQUMzRSxNQUFNK0UsaUJBQWlCbkYsS0FBS2lGLFNBQVMsQ0FBQyxXQUF5QjdFLE9BQWRILFlBQVcsS0FBZSxPQUFaRyxhQUFZO1lBRTNFLElBQUk0RSxnQkFBZ0JFLGtCQUFrQkMsZ0JBQWdCO2dCQUNwRFQsaUJBQWlCdEU7WUFDbkI7WUFFQSxJQUFJb0YsU0FBUyxXQUF5QnBGLE9BQWRILFlBQVcsS0FBZSxPQUFaRyxhQUFZLFlBQVU0RSxjQUFjO2dCQUN4RSxNQUFNUyxlQUFlcEYsWUFBWStELElBQUksQ0FBQ3BDLENBQUFBO29CQUNwQyxNQUFNZSxZQUFZLEdBQXVCZixPQUFwQkEsS0FBS2dCLFdBQVcsRUFBQyxLQUFnRGhCLE9BQTdDQSxLQUFLRyxrQkFBa0IsSUFBSUgsS0FBS0MsU0FBUyxFQUFDLEtBQStCLE9BQTVCRCxLQUFLUyxzQkFBc0I7b0JBQ2pILE9BQU9NLGNBQWNpQztnQkFDdkI7Z0JBRUEsSUFBSVMsY0FBYztvQkFDaEIsTUFBTUMsbUJBQW1CMUIsbUJBQW1CLFNBQVNnQjtvQkFFckQsSUFBSVUsaUJBQWlCekUsTUFBTSxHQUFHLEdBQUc7d0JBQy9CLElBQUlpRSxnQkFBZ0I7NEJBQ2xCLE1BQU1TLHNCQUFzQkQsaUJBQWlCRSxJQUFJLENBQUNDLENBQUFBLFFBQVNBLE1BQU1wRCxzQkFBc0IsS0FBS3lDOzRCQUU1RixJQUFJUyxxQkFBcUI7Z0NBQ3ZCLE1BQU1HLGdCQUFnQkosaUJBQWlCdEIsSUFBSSxDQUFDeUIsQ0FBQUEsUUFBU0EsTUFBTXBELHNCQUFzQixLQUFLeUM7Z0NBQ3RGLElBQUlZLGlCQUFpQlgsbUJBQW1CVyxjQUFjbkQsU0FBUyxFQUFFO29DQUMvRDNDLEtBQUtvRixRQUFRLENBQUMsV0FBeUJoRixPQUFkSCxZQUFXLEtBQWUsT0FBWkcsYUFBWSxZQUFVMEYsY0FBY25ELFNBQVM7Z0NBQ3RGOzRCQUNGLE9BQU87Z0NBQ0wzQyxLQUFLb0YsUUFBUSxDQUFDLFdBQXlCaEYsT0FBZEgsWUFBVyxLQUFlLE9BQVpHLGFBQVksWUFBVTtnQ0FDN0QsSUFBSStFLG1CQUFtQixJQUFJO29DQUN6Qm5GLEtBQUtvRixRQUFRLENBQUMsV0FBeUJoRixPQUFkSCxZQUFXLEtBQWUsT0FBWkcsYUFBWSxZQUFVO2dDQUMvRDs0QkFDRjt3QkFDRixPQUFPOzRCQUNMLElBQUkrRSxnQkFBZ0I7Z0NBQ2xCLE1BQU1ZLHNCQUFzQkwsaUJBQWlCRSxJQUFJLENBQUNDLENBQUFBLFFBQVNBLE1BQU1sRCxTQUFTLEtBQUt3QztnQ0FFL0UsSUFBSVkscUJBQXFCLENBQ3pCLE9BQU87b0NBQ0wvRixLQUFLb0YsUUFBUSxDQUFDLFdBQXlCaEYsT0FBZEgsWUFBVyxLQUFlLE9BQVpHLGFBQVksWUFBVTtnQ0FDL0Q7NEJBQ0YsT0FBTyxDQUNQO3dCQUNGO29CQUNGLE9BQU87d0JBRUwsSUFBSThFLG1CQUFtQk8sYUFBYWhELHNCQUFzQixFQUFFOzRCQUMxRHpDLEtBQUtvRixRQUFRLENBQUMsV0FBeUJoRixPQUFkSCxZQUFXLEtBQWUsT0FBWkcsYUFBWSxZQUFVcUYsYUFBYWhELHNCQUFzQjt3QkFDbEc7d0JBQ0EsSUFBSTBDLG1CQUFtQk0sYUFBYTlDLFNBQVMsRUFBRTs0QkFDN0MzQyxLQUFLb0YsUUFBUSxDQUFDLFdBQXlCaEYsT0FBZEgsWUFBVyxLQUFlLE9BQVpHLGFBQVksWUFBVXFGLGFBQWE5QyxTQUFTO3dCQUNyRjtvQkFDRjtvQkFFQSxNQUFNcUQsaUJBQWlCaEcsS0FBS2lGLFNBQVMsQ0FBQyxXQUFzQixPQUFYaEYsWUFBVztvQkFDNUQsSUFBSStGLG1CQUFtQlAsYUFBYVEsWUFBWSxFQUFFO3dCQUNoRGpHLEtBQUtvRixRQUFRLENBQUMsV0FBc0IsT0FBWG5GLFlBQVcsYUFBV3dGLGFBQWFRLFlBQVk7b0JBQzFFO29CQUVBLE1BQU1DLGVBQXlCLEVBQUU7b0JBQ2pDUixpQkFBaUIzRCxPQUFPLENBQUM4RCxDQUFBQTt3QkFDdkIsSUFBSUEsTUFBTTdDLFdBQVcsRUFBRTs0QkFDckIsSUFBSTZDLE1BQU03QyxXQUFXLENBQUN2QixRQUFRLENBQUMsTUFBTTtnQ0FDbkMsTUFBTTBFLGlCQUFpQk4sTUFBTTdDLFdBQVcsQ0FBQ2hDLEtBQUssQ0FBQyxLQUFLb0YsR0FBRyxDQUFDM0IsQ0FBQUEsSUFBS0EsRUFBRXRELElBQUk7Z0NBQ25FK0UsYUFBYWpELElBQUksSUFBSWtEOzRCQUN2QixPQUFPO2dDQUNMRCxhQUFhakQsSUFBSSxDQUFDNEMsTUFBTTdDLFdBQVc7NEJBQ3JDO3dCQUNGO29CQUNGO29CQUNBLE1BQU1xRCxrQkFBa0JDLE1BQU1DLElBQUksQ0FBQyxJQUFJQyxJQUFJTixhQUFhN0MsTUFBTSxDQUFDb0QsQ0FBQUEsT0FBUUE7b0JBRXZFLElBQUlKLGdCQUFnQnBGLE1BQU0sS0FBSyxHQUFHO3dCQUNoQyxNQUFNeUYsa0JBQWtCMUcsS0FBS2lGLFNBQVMsQ0FBQyxXQUFzQixPQUFYaEYsWUFBVzt3QkFDN0QsSUFBSXlHLG9CQUFvQkwsZUFBZSxDQUFDLEVBQUUsRUFBRTs0QkFDMUNyRyxLQUFLb0YsUUFBUSxDQUFDLFdBQXNCLE9BQVhuRixZQUFXLGNBQVlvRyxlQUFlLENBQUMsRUFBRTt3QkFDcEU7b0JBQ0YsT0FBTzt3QkFDTCxNQUFNSyxrQkFBa0IxRyxLQUFLaUYsU0FBUyxDQUFDLFdBQXNCLE9BQVhoRixZQUFXO3dCQUM3RCxJQUFJeUcsb0JBQW9CLElBQUk7NEJBQzFCMUcsS0FBS29GLFFBQVEsQ0FBQyxXQUFzQixPQUFYbkYsWUFBVyxjQUFZO3dCQUNsRDtvQkFDRjtvQkFFQSxJQUFJQyxxQkFBcUI7d0JBQ3ZCLE1BQU15RyxxQkFBcUJOLGdCQUFnQnBGLE1BQU0sS0FBSyxJQUFJb0YsZUFBZSxDQUFDLEVBQUUsR0FBRzt3QkFDL0VuRyxvQkFBb0JELFlBQVl3RixhQUFhUSxZQUFZLElBQUksSUFBSVU7b0JBQ25FO2dCQUNGO1lBQ0Y7WUFFQSxJQUFJbkIsU0FBUyxXQUF5QnBGLE9BQWRILFlBQVcsS0FBZSxPQUFaRyxhQUFZLGNBQVk4RSxnQkFBZ0I7Z0JBQzVFLE1BQU1RLG1CQUFtQjFCLG1CQUFtQixXQUFXa0I7Z0JBRXZELElBQUlRLGlCQUFpQnpFLE1BQU0sR0FBRyxHQUFHO29CQUMvQixJQUFJK0QsY0FBYzt3QkFDaEIsTUFBTTRCLG9CQUFvQnZHLFlBQVkrRCxJQUFJLENBQUNwQyxDQUFBQTs0QkFDekMsTUFBTWUsWUFBWSxHQUF1QmYsT0FBcEJBLEtBQUtnQixXQUFXLEVBQUMsS0FBZ0RoQixPQUE3Q0EsS0FBS0csa0JBQWtCLElBQUlILEtBQUtDLFNBQVMsRUFBQyxLQUErQixPQUE1QkQsS0FBS1Msc0JBQXNCOzRCQUNqSCxPQUFPTSxjQUFjaUM7d0JBQ3ZCO3dCQUVBLElBQUk0QixtQkFBbUI7NEJBQ3JCLE1BQU14RSxXQUFXMkIsWUFBWTZDOzRCQUU3QixNQUFNQyx5QkFBeUJ4RyxZQUFZdUYsSUFBSSxDQUFDNUQsQ0FBQUEsT0FDOUMrQixZQUFZL0IsVUFBVUksWUFBWUosS0FBS1Msc0JBQXNCLEtBQUt5Qzs0QkFHcEUsSUFBSTJCLHdCQUF3QjtnQ0FDMUIsTUFBTUMsdUJBQXVCekcsWUFBWStELElBQUksQ0FBQ3BDLENBQUFBLE9BQzVDK0IsWUFBWS9CLFVBQVVJLFlBQVlKLEtBQUtTLHNCQUFzQixLQUFLeUM7Z0NBR3BFLElBQUk0QixzQkFBc0I7b0NBQ3hCLE1BQU1DLGVBQWUsR0FBdUNELE9BQXBDQSxxQkFBcUI5RCxXQUFXLEVBQUMsS0FBZ0Y4RCxPQUE3RUEscUJBQXFCM0Usa0JBQWtCLElBQUkyRSxxQkFBcUI3RSxTQUFTLEVBQUMsS0FBK0MsT0FBNUM2RSxxQkFBcUJyRSxzQkFBc0I7b0NBQ3BMLElBQUl1QyxpQkFBaUIrQixjQUFjO3dDQUNqQy9HLEtBQUtvRixRQUFRLENBQUMsV0FBeUJoRixPQUFkSCxZQUFXLEtBQWUsT0FBWkcsYUFBWSxVQUFRMkc7b0NBQzdEO29DQUVBLElBQUk1QixtQkFBbUIyQixxQkFBcUJuRSxTQUFTLEVBQUU7d0NBQ3JEM0MsS0FBS29GLFFBQVEsQ0FBQyxXQUF5QmhGLE9BQWRILFlBQVcsS0FBZSxPQUFaRyxhQUFZLFlBQVUwRyxxQkFBcUJuRSxTQUFTO29DQUM3RjtvQ0FFQSxNQUFNcUQsaUJBQWlCaEcsS0FBS2lGLFNBQVMsQ0FBQyxXQUFzQixPQUFYaEYsWUFBVztvQ0FDNUQsSUFBSStGLG1CQUFtQmMscUJBQXFCYixZQUFZLEVBQUU7d0NBQ3hEakcsS0FBS29GLFFBQVEsQ0FBQyxXQUFzQixPQUFYbkYsWUFBVyxhQUFXNkcscUJBQXFCYixZQUFZO29DQUNsRjtvQ0FFQSxNQUFNUyxrQkFBa0IxRyxLQUFLaUYsU0FBUyxDQUFDLFdBQXNCLE9BQVhoRixZQUFXO29DQUM3RCxJQUFJeUcsb0JBQW9CSSxxQkFBcUI5RCxXQUFXLEVBQUU7d0NBQ3hEaEQsS0FBS29GLFFBQVEsQ0FBQyxXQUFzQixPQUFYbkYsWUFBVyxjQUFZNkcscUJBQXFCOUQsV0FBVztvQ0FDbEY7b0NBRUEsSUFBSTlDLHFCQUFxQjt3Q0FDdkJBLG9CQUFvQkQsWUFBWTZHLHFCQUFxQmIsWUFBWSxJQUFJLElBQUlhLHFCQUFxQjlELFdBQVcsSUFBSTtvQ0FDL0c7b0NBQ0E7Z0NBQ0Y7NEJBQ0YsT0FBTztnQ0FDTGhELEtBQUtvRixRQUFRLENBQUMsV0FBeUJoRixPQUFkSCxZQUFXLEtBQWUsT0FBWkcsYUFBWSxVQUFROzRCQUM3RDt3QkFDRjtvQkFDRjtvQkFFQSxNQUFNNEcsaUJBQWlCVixNQUFNQyxJQUFJLENBQUMsSUFBSUMsSUFBSWQsaUJBQWlCVSxHQUFHLENBQUNQLENBQUFBLFFBQVNBLE1BQU1sRCxTQUFTLEVBQUVVLE1BQU0sQ0FBQzRELENBQUFBLE1BQU9BO29CQUN2RyxJQUFJRCxlQUFlL0YsTUFBTSxLQUFLLEdBQUc7d0JBQy9CLElBQUlrRSxtQkFBbUI2QixjQUFjLENBQUMsRUFBRSxFQUFFOzRCQUN4Q2hILEtBQUtvRixRQUFRLENBQUMsV0FBeUJoRixPQUFkSCxZQUFXLEtBQWUsT0FBWkcsYUFBWSxZQUFVNEcsY0FBYyxDQUFDLEVBQUU7d0JBQ2hGO29CQUNGLE9BQU87d0JBQ0wsSUFBSTdCLG1CQUFtQixJQUFJOzRCQUN6Qm5GLEtBQUtvRixRQUFRLENBQUMsV0FBeUJoRixPQUFkSCxZQUFXLEtBQWUsT0FBWkcsYUFBWSxZQUFVO3dCQUMvRDtvQkFDRjtnQkFDRixPQUFPO29CQUNMLE1BQU1xRixlQUFlQyxnQkFBZ0IsQ0FBQyxFQUFFO29CQUN4QyxJQUFJRCxjQUFjO3dCQUNoQixNQUFNMUMsWUFBWSxHQUErQjBDLE9BQTVCQSxhQUFhekMsV0FBVyxFQUFDLEtBQWdFeUMsT0FBN0RBLGFBQWF0RCxrQkFBa0IsSUFBSXNELGFBQWF4RCxTQUFTLEVBQUMsS0FBdUMsT0FBcEN3RCxhQUFhaEQsc0JBQXNCO3dCQUVqSixJQUFJdUMsaUJBQWlCakMsV0FBVzs0QkFDOUIvQyxLQUFLb0YsUUFBUSxDQUFDLFdBQXlCaEYsT0FBZEgsWUFBVyxLQUFlLE9BQVpHLGFBQVksVUFBUTJDO3dCQUM3RDt3QkFDQSxJQUFJb0MsbUJBQW1CTSxhQUFhOUMsU0FBUyxFQUFFOzRCQUM3QzNDLEtBQUtvRixRQUFRLENBQUMsV0FBeUJoRixPQUFkSCxZQUFXLEtBQWUsT0FBWkcsYUFBWSxZQUFVcUYsYUFBYTlDLFNBQVM7d0JBQ3JGO3dCQUVBLE1BQU1xRCxpQkFBaUJoRyxLQUFLaUYsU0FBUyxDQUFDLFdBQXNCLE9BQVhoRixZQUFXO3dCQUM1RCxJQUFJK0YsbUJBQW1CUCxhQUFhUSxZQUFZLEVBQUU7NEJBQ2hEakcsS0FBS29GLFFBQVEsQ0FBQyxXQUFzQixPQUFYbkYsWUFBVyxhQUFXd0YsYUFBYVEsWUFBWTt3QkFDMUU7d0JBRUEsTUFBTVMsa0JBQWtCMUcsS0FBS2lGLFNBQVMsQ0FBQyxXQUFzQixPQUFYaEYsWUFBVzt3QkFDN0QsSUFBSXlHLG9CQUFvQmpCLGFBQWF6QyxXQUFXLEVBQUU7NEJBQ2hEaEQsS0FBS29GLFFBQVEsQ0FBQyxXQUFzQixPQUFYbkYsWUFBVyxjQUFZd0YsYUFBYXpDLFdBQVc7d0JBQzFFO3dCQUVBLElBQUk5QyxxQkFBcUI7NEJBQ3ZCQSxvQkFBb0JELFlBQVl3RixhQUFhUSxZQUFZLElBQUksSUFBSVIsYUFBYXpDLFdBQVcsSUFBSTt3QkFDL0Y7b0JBQ0Y7Z0JBQ0Y7WUFDRjtZQUVBLElBQUl3QyxTQUFTLFdBQXlCcEYsT0FBZEgsWUFBVyxLQUFlLE9BQVpHLGFBQVksY0FBWStFLGdCQUFnQjtnQkFDNUUsTUFBTU8sbUJBQW1CMUIsbUJBQW1CLFdBQVdtQjtnQkFFdkQsSUFBSU8saUJBQWlCekUsTUFBTSxHQUFHLEdBQUc7b0JBQy9CLElBQUkrRCxpQkFBaUIsSUFBSTt3QkFDdkJoRixLQUFLb0YsUUFBUSxDQUFDLFdBQXlCaEYsT0FBZEgsWUFBVyxLQUFlLE9BQVpHLGFBQVksVUFBUTtvQkFDN0Q7b0JBQ0EsSUFBSThFLG1CQUFtQixJQUFJO3dCQUN6QmxGLEtBQUtvRixRQUFRLENBQUMsV0FBeUJoRixPQUFkSCxZQUFXLEtBQWUsT0FBWkcsYUFBWSxZQUFVO29CQUMvRDtnQkFDRixPQUFPO29CQUNMLE1BQU1xRixlQUFlQyxnQkFBZ0IsQ0FBQyxFQUFFO29CQUN4QyxJQUFJRCxjQUFjO3dCQUNoQixNQUFNMUMsWUFBWSxHQUErQjBDLE9BQTVCQSxhQUFhekMsV0FBVyxFQUFDLEtBQWdFeUMsT0FBN0RBLGFBQWF0RCxrQkFBa0IsSUFBSXNELGFBQWF4RCxTQUFTLEVBQUMsS0FBdUMsT0FBcEN3RCxhQUFhaEQsc0JBQXNCO3dCQUVqSixJQUFJdUMsaUJBQWlCakMsV0FBVzs0QkFDOUIvQyxLQUFLb0YsUUFBUSxDQUFDLFdBQXlCaEYsT0FBZEgsWUFBVyxLQUFlLE9BQVpHLGFBQVksVUFBUTJDO3dCQUM3RDt3QkFDQSxJQUFJbUMsbUJBQW1CTyxhQUFhaEQsc0JBQXNCLEVBQUU7NEJBQzFEekMsS0FBS29GLFFBQVEsQ0FBQyxXQUF5QmhGLE9BQWRILFlBQVcsS0FBZSxPQUFaRyxhQUFZLFlBQVVxRixhQUFhaEQsc0JBQXNCO3dCQUNsRzt3QkFFQSxNQUFNdUQsaUJBQWlCaEcsS0FBS2lGLFNBQVMsQ0FBQyxXQUFzQixPQUFYaEYsWUFBVzt3QkFDNUQsSUFBSStGLG1CQUFtQlAsYUFBYVEsWUFBWSxFQUFFOzRCQUNoRGpHLEtBQUtvRixRQUFRLENBQUMsV0FBc0IsT0FBWG5GLFlBQVcsYUFBV3dGLGFBQWFRLFlBQVk7d0JBQzFFO3dCQUVBLE1BQU1TLGtCQUFrQjFHLEtBQUtpRixTQUFTLENBQUMsV0FBc0IsT0FBWGhGLFlBQVc7d0JBQzdELElBQUl5RyxvQkFBb0JqQixhQUFhekMsV0FBVyxFQUFFOzRCQUNoRGhELEtBQUtvRixRQUFRLENBQUMsV0FBc0IsT0FBWG5GLFlBQVcsY0FBWXdGLGFBQWF6QyxXQUFXO3dCQUMxRTt3QkFFQSxJQUFJOUMscUJBQXFCOzRCQUN2QkEsb0JBQW9CRCxZQUFZd0YsYUFBYVEsWUFBWSxJQUFJLElBQUlSLGFBQWF6QyxXQUFXLElBQUk7d0JBQy9GO29CQUNGO2dCQUNGO1lBQ0Y7UUFDRjtRQUVBLE9BQU8sSUFBTXFDLGFBQWE2QixXQUFXO0lBQ3ZDLEdBQUc7UUFBQzdHO1FBQWFMO1FBQU1DO1FBQVlDO1FBQXFCOEQ7UUFBb0JEO1FBQWFXO1FBQWtCdEU7S0FBWTtJQUV2SCxNQUFNK0csb0JBQW9CO1FBQ3hCLE1BQU1uQyxlQUFlaEYsS0FBS2lGLFNBQVMsQ0FBQyxXQUF5QjdFLE9BQWRILFlBQVcsS0FBZSxPQUFaRyxhQUFZO1FBQ3pFLE1BQU0rRSxpQkFBaUJuRixLQUFLaUYsU0FBUyxDQUFDLFdBQXlCN0UsT0FBZEgsWUFBVyxLQUFlLE9BQVpHLGFBQVk7UUFFM0UsSUFBSTRFLGNBQWM7WUFDaEIsTUFBTTRCLG9CQUFvQnZHLFlBQVkrRCxJQUFJLENBQUNwQyxDQUFBQTtnQkFDekMsTUFBTWUsWUFBWSxHQUF1QmYsT0FBcEJBLEtBQUtnQixXQUFXLEVBQUMsS0FBZ0RoQixPQUE3Q0EsS0FBS0csa0JBQWtCLElBQUlILEtBQUtDLFNBQVMsRUFBQyxLQUErQixPQUE1QkQsS0FBS1Msc0JBQXNCO2dCQUNqSCxPQUFPTSxjQUFjaUM7WUFDdkI7WUFFQSxJQUFJNEIsbUJBQW1CO29CQUNPbEY7Z0JBQTVCLE1BQU00QyxzQkFBc0I1QyxFQUFBQSx3QkFBQUEsa0JBQWtCMEMsSUFBSSxDQUFDZCxDQUFBQSxTQUFVQSxPQUFPSixLQUFLLEtBQUs4QiwyQkFBbER0RCw0Q0FBQUEsc0JBQWlFa0IsV0FBVyxLQUFJO2dCQUU1RyxNQUFNMkIseUJBQXlCbEUsWUFBWWdELE1BQU0sQ0FBQ3JCLENBQUFBO29CQUNoRCxNQUFNUSxXQUFXUixLQUFLUSxRQUFRLElBQUkzQix1QkFBdUJtQixLQUFLUyxzQkFBc0IsSUFBSTtvQkFDeEYsTUFBTUMsVUFBVVYsS0FBS1csU0FBUyxJQUFJO29CQUVsQyxNQUFNVixZQUFZRCxLQUFLQyxTQUFTLElBQUk7b0JBQ3BDLE1BQU1DLFlBQVlGLEtBQUtHLGtCQUFrQixJQUFJSCxLQUFLRyxrQkFBa0IsS0FBSyxTQUFTSCxLQUFLRyxrQkFBa0IsR0FBRztvQkFFNUcsSUFBSVMsY0FBYztvQkFDbEIsSUFBSVYsV0FBVzt3QkFDYlUsY0FBYyxHQUFpQlYsT0FBZEQsV0FBVSxNQUFjLE9BQVZDLFdBQVU7b0JBQzNDLE9BQU87d0JBQ0xVLGNBQWNYO29CQUNoQjtvQkFFQSxNQUFNRyxXQUFXUTtvQkFDakIsTUFBTTRCLGtCQUFrQm5FLFlBQVlnRCxNQUFNLENBQUNvQixDQUFBQSxJQUFLVixZQUFZVSxPQUFPN0I7b0JBQ25FLElBQUk0QixnQkFBZ0J2RCxNQUFNLEdBQUcsR0FBRzt3QkFDOUIsSUFBSXVCLFVBQVU7NEJBQ1pJLGNBQWMsR0FBa0JKLE9BQWZJLGFBQVksS0FBWSxPQUFUSjt3QkFDbEMsT0FBTyxJQUFJUixLQUFLVyxTQUFTLEVBQUU7NEJBQ3pCQyxjQUFjLEdBQWtCWixPQUFmWSxhQUFZLEtBQWtCLE9BQWZaLEtBQUtXLFNBQVM7d0JBQ2hEO29CQUNGO29CQUVBLE9BQU9DLGdCQUFnQjBCO2dCQUN6QjtnQkFFQSxJQUFJQyx1QkFBdUJ0RCxNQUFNLEdBQUcsR0FBRztvQkFDckMsT0FBT3NELHVCQUNKNkIsR0FBRyxDQUFDcEUsQ0FBQUEsT0FBUUEsS0FBS1Msc0JBQXNCLEVBQ3ZDWSxNQUFNLENBQUN2QyxDQUFBQSxVQUFXQSxTQUNsQnVDLE1BQU0sQ0FBQyxDQUFDdkMsU0FBU3lDLE9BQU9DLE9BQVNBLEtBQUs0RCxPQUFPLENBQUN0RyxhQUFheUMsT0FDM0RJLElBQUk7Z0JBQ1QsT0FBTztvQkFDTCxPQUFPaUQsa0JBQWtCbkUsc0JBQXNCLEdBQUc7d0JBQUNtRSxrQkFBa0JuRSxzQkFBc0I7cUJBQUMsR0FBRyxFQUFFO2dCQUNuRztZQUNGO1FBQ0Y7UUFFQSxJQUFJMEMsZ0JBQWdCO1lBQ2xCLE1BQU1PLG1CQUFtQjFCLG1CQUFtQixXQUFXbUI7WUFFdkQsSUFBSU8saUJBQWlCekUsTUFBTSxHQUFHLEdBQUc7Z0JBQy9CLE9BQU95RSxpQkFDSlUsR0FBRyxDQUFDcEUsQ0FBQUEsT0FBUUEsS0FBS1Msc0JBQXNCLEVBQ3ZDWSxNQUFNLENBQUN2QyxDQUFBQSxVQUFXQSxTQUNsQnVDLE1BQU0sQ0FBQyxDQUFDdkMsU0FBU3lDLE9BQU9DLE9BQVNBLEtBQUs0RCxPQUFPLENBQUN0RyxhQUFheUMsT0FDM0RJLElBQUk7WUFDVDtRQUNGO1FBRUEsTUFBTTBELGtCQUFrQixJQUFJYjtRQUM1Qm5HLFlBQVkwQixPQUFPLENBQUNDLENBQUFBO1lBQ2xCLElBQUlBLEtBQUtTLHNCQUFzQixFQUFFO2dCQUMvQjRFLGdCQUFnQkMsR0FBRyxDQUFDdEYsS0FBS1Msc0JBQXNCO1lBQ2pEO1FBQ0Y7UUFDQSxPQUFPNkQsTUFBTUMsSUFBSSxDQUFDYyxpQkFBaUIxRCxJQUFJO0lBQ3pDO0lBRUEsTUFBTTRELG9CQUFvQjtRQUN4QixNQUFNdkMsZUFBZWhGLEtBQUtpRixTQUFTLENBQUMsV0FBeUI3RSxPQUFkSCxZQUFXLEtBQWUsT0FBWkcsYUFBWTtRQUN6RSxNQUFNOEUsaUJBQWlCbEYsS0FBS2lGLFNBQVMsQ0FBQyxXQUF5QjdFLE9BQWRILFlBQVcsS0FBZSxPQUFaRyxhQUFZO1FBRTNFLElBQUk0RSxjQUFjO1lBQ2hCLE1BQU00QixvQkFBb0J2RyxZQUFZK0QsSUFBSSxDQUFDcEMsQ0FBQUE7Z0JBQ3pDLE1BQU1lLFlBQVksR0FBdUJmLE9BQXBCQSxLQUFLZ0IsV0FBVyxFQUFDLEtBQWdEaEIsT0FBN0NBLEtBQUtHLGtCQUFrQixJQUFJSCxLQUFLQyxTQUFTLEVBQUMsS0FBK0IsT0FBNUJELEtBQUtTLHNCQUFzQjtnQkFDakgsT0FBT00sY0FBY2lDO1lBQ3ZCO1lBRUEsSUFBSTRCLG1CQUFtQjtvQkFDT2xGO2dCQUE1QixNQUFNNEMsc0JBQXNCNUMsRUFBQUEsd0JBQUFBLGtCQUFrQjBDLElBQUksQ0FBQ2QsQ0FBQUEsU0FBVUEsT0FBT0osS0FBSyxLQUFLOEIsMkJBQWxEdEQsNENBQUFBLHNCQUFpRWtCLFdBQVcsS0FBSTtnQkFFNUcsTUFBTTJCLHlCQUF5QmxFLFlBQVlnRCxNQUFNLENBQUNyQixDQUFBQTtvQkFDaEQsTUFBTVEsV0FBV1IsS0FBS1EsUUFBUSxJQUFJM0IsdUJBQXVCbUIsS0FBS1Msc0JBQXNCLElBQUk7b0JBQ3hGLE1BQU1DLFVBQVVWLEtBQUtXLFNBQVMsSUFBSTtvQkFFbEMsTUFBTVYsWUFBWUQsS0FBS0MsU0FBUyxJQUFJO29CQUNwQyxNQUFNQyxZQUFZRixLQUFLRyxrQkFBa0IsSUFBSUgsS0FBS0csa0JBQWtCLEtBQUssU0FBU0gsS0FBS0csa0JBQWtCLEdBQUc7b0JBRTVHLElBQUlTLGNBQWM7b0JBQ2xCLElBQUlWLFdBQVc7d0JBQ2JVLGNBQWMsR0FBaUJWLE9BQWRELFdBQVUsTUFBYyxPQUFWQyxXQUFVO29CQUMzQyxPQUFPO3dCQUNMVSxjQUFjWDtvQkFDaEI7b0JBQ0EsTUFBTXVDLGtCQUFrQm5FLFlBQVlnRCxNQUFNLENBQUNvQixDQUFBQSxJQUFLVixZQUFZVSxPQUFPN0I7b0JBQ25FLElBQUk0QixnQkFBZ0J2RCxNQUFNLEdBQUcsR0FBRzt3QkFDOUIsSUFBSXVCLFVBQVU7NEJBQ1pJLGNBQWMsR0FBa0JKLE9BQWZJLGFBQVksS0FBWSxPQUFUSjt3QkFDbEMsT0FBTyxJQUFJUixLQUFLVyxTQUFTLEVBQUU7NEJBQ3pCQyxjQUFjLEdBQWtCWixPQUFmWSxhQUFZLEtBQWtCLE9BQWZaLEtBQUtXLFNBQVM7d0JBQ2hEO29CQUNGO29CQUVBLE9BQU9DLGdCQUFnQjBCO2dCQUN6QjtnQkFFQSxJQUFJQyx1QkFBdUJ0RCxNQUFNLEdBQUcsR0FBRztvQkFDckMsT0FBT3NELHVCQUNKNkIsR0FBRyxDQUFDcEUsQ0FBQUEsT0FBUUEsS0FBS1csU0FBUyxFQUMxQlUsTUFBTSxDQUFDWCxDQUFBQSxVQUFXQSxTQUNsQlcsTUFBTSxDQUFDLENBQUNYLFNBQVNhLE9BQU9DLE9BQVNBLEtBQUs0RCxPQUFPLENBQUMxRSxhQUFhYSxPQUMzREksSUFBSTtnQkFDVCxPQUFPO29CQUNMLE9BQU9pRCxrQkFBa0JqRSxTQUFTLEdBQUc7d0JBQUNpRSxrQkFBa0JqRSxTQUFTO3FCQUFDLEdBQUcsRUFBRTtnQkFDekU7WUFDRjtRQUNGO1FBRUEsSUFBSXVDLGdCQUFnQjtZQUNsQixNQUFNUSxtQkFBbUIxQixtQkFBbUIsV0FBV2tCO1lBRXZELElBQUlRLGlCQUFpQnpFLE1BQU0sR0FBRyxHQUFHO2dCQUMvQixPQUFPeUUsaUJBQ0pVLEdBQUcsQ0FBQ3BFLENBQUFBLE9BQVFBLEtBQUtXLFNBQVMsRUFDMUJVLE1BQU0sQ0FBQ1gsQ0FBQUEsVUFBV0EsU0FDbEJXLE1BQU0sQ0FBQyxDQUFDWCxTQUFTYSxPQUFPQyxPQUFTQSxLQUFLNEQsT0FBTyxDQUFDMUUsYUFBYWEsT0FDM0RJLElBQUk7WUFDVDtRQUNGO1FBRUEsTUFBTXFELGlCQUFpQixJQUFJUjtRQUMzQm5HLFlBQVkwQixPQUFPLENBQUNDLENBQUFBO1lBQ2xCLElBQUlBLEtBQUtXLFNBQVMsRUFBRTtnQkFDbEJxRSxlQUFlTSxHQUFHLENBQUN0RixLQUFLVyxTQUFTO1lBQ25DO1FBQ0Y7UUFDQSxPQUFPMkQsTUFBTUMsSUFBSSxDQUFDUyxnQkFBZ0JyRCxJQUFJO0lBQ3hDO0lBRUEsTUFBTTZELHlCQUF5QjtRQUM3QixNQUFNdEMsaUJBQWlCbEYsS0FBS2lGLFNBQVMsQ0FBQyxXQUF5QjdFLE9BQWRILFlBQVcsS0FBZSxPQUFaRyxhQUFZO1FBQzNFLE1BQU0rRSxpQkFBaUJuRixLQUFLaUYsU0FBUyxDQUFDLFdBQXlCN0UsT0FBZEgsWUFBVyxLQUFlLE9BQVpHLGFBQVk7UUFFM0UsSUFBSThFLGdCQUFnQjtZQUNsQixNQUFNUSxtQkFBbUIxQixtQkFBbUIsV0FBV2tCO1lBQ3ZELElBQUlRLGlCQUFpQnpFLE1BQU0sR0FBRyxHQUFHO2dCQUMvQixPQUFPd0c7WUFDVDtRQUNGO1FBRUEsSUFBSXRDLGdCQUFnQjtZQUNsQixNQUFNTyxtQkFBbUIxQixtQkFBbUIsV0FBV21CO1lBQ3ZELElBQUlPLGlCQUFpQnpFLE1BQU0sR0FBRyxHQUFHO2dCQUMvQixPQUFPd0c7WUFDVDtRQUNGO1FBRUEsT0FBTy9GO0lBQ1Q7SUFFQSxNQUFNK0YsMEJBQTBCO1FBQzlCLE1BQU12QyxpQkFBaUJsRixLQUFLaUYsU0FBUyxDQUFDLFdBQXlCN0UsT0FBZEgsWUFBVyxLQUFlLE9BQVpHLGFBQVk7UUFDM0UsTUFBTStFLGlCQUFpQm5GLEtBQUtpRixTQUFTLENBQUMsV0FBeUI3RSxPQUFkSCxZQUFXLEtBQWUsT0FBWkcsYUFBWTtRQUUzRSxJQUFJc0gsZUFBZXJIO1FBRW5CLElBQUk2RSxnQkFBZ0I7WUFDbEIsTUFBTVEsbUJBQW1CMUIsbUJBQW1CLFdBQVdrQjtZQUN2RCxJQUFJUSxpQkFBaUJ6RSxNQUFNLEdBQUcsR0FBRztnQkFDL0J5RyxlQUFlaEM7WUFDakI7UUFDRjtRQUVBLElBQUlQLGdCQUFnQjtZQUNsQixNQUFNTyxtQkFBbUIxQixtQkFBbUIsV0FBV21CO1lBQ3ZELElBQUlPLGlCQUFpQnpFLE1BQU0sR0FBRyxHQUFHO2dCQUMvQnlHLGVBQWVBLGFBQWFyRSxNQUFNLENBQUNyQixDQUFBQSxPQUNqQzBELGlCQUFpQkUsSUFBSSxDQUFDK0IsQ0FBQUEsTUFBT0EsSUFBSXJGLEVBQUUsS0FBS04sS0FBS00sRUFBRTtZQUVuRDtRQUNGO1FBRUEsTUFBTVgsVUFLRCxFQUFFO1FBRVArRixhQUFhM0YsT0FBTyxDQUFDQyxDQUFBQTtZQUNuQixNQUFNUSxXQUFXUixLQUFLUSxRQUFRLElBQUkzQix1QkFBdUJtQixLQUFLUyxzQkFBc0IsSUFBSTtZQUN4RixNQUFNQyxVQUFVVixLQUFLVyxTQUFTLElBQUk7WUFFbEMsSUFBSUMsY0FBYztZQUNsQixJQUFJQyxPQUE0QjtZQUVoQyxNQUFNWixZQUFZRCxLQUFLQyxTQUFTLElBQUk7WUFDcEMsTUFBTUMsWUFBWUYsS0FBS0csa0JBQWtCLElBQUlILEtBQUtHLGtCQUFrQixLQUFLLFNBQVNILEtBQUtHLGtCQUFrQixHQUFHO1lBRTVHLElBQUlELFdBQVc7Z0JBQ2JVLGNBQWMsR0FBaUJWLE9BQWRELFdBQVUsTUFBYyxPQUFWQyxXQUFVO2dCQUN6Q1csT0FBTztZQUNULE9BQU87Z0JBQ0xELGNBQWNYO2dCQUNkWSxPQUFPO1lBQ1Q7WUFFQSxNQUFNRSxZQUFZLEdBQXVCZixPQUFwQkEsS0FBS2dCLFdBQVcsRUFBQyxLQUFnRGhCLE9BQTdDQSxLQUFLRyxrQkFBa0IsSUFBSUgsS0FBS0MsU0FBUyxFQUFDLEtBQStCLE9BQTVCRCxLQUFLUyxzQkFBc0I7WUFFakgsTUFBTStCLGtCQUFrQmtELGFBQWFyRSxNQUFNLENBQUNvQixDQUFBQTtnQkFDMUMsTUFBTW1ELGFBQWFuRCxFQUFFeEMsU0FBUyxJQUFJO2dCQUNsQyxNQUFNNEYsYUFBYXBELEVBQUV0QyxrQkFBa0IsSUFBSXNDLEVBQUV0QyxrQkFBa0IsS0FBSyxTQUFTc0MsRUFBRXRDLGtCQUFrQixHQUFHO2dCQUVwRyxJQUFJMkYsZUFBZTtnQkFDbkIsSUFBSUQsWUFBWTtvQkFDZEMsZUFBZSxHQUFrQkQsT0FBZkQsWUFBVyxNQUFlLE9BQVhDLFlBQVc7Z0JBQzlDLE9BQU87b0JBQ0xDLGVBQWVGO2dCQUNqQjtnQkFFQSxPQUFPRSxpQkFBaUJsRjtZQUMxQjtZQUVBLElBQUltRixtQkFBbUJuRjtZQUN2QixJQUFJNEIsZ0JBQWdCdkQsTUFBTSxHQUFHLEdBQUc7Z0JBQzlCLElBQUl1QixVQUFVO29CQUNadUYsbUJBQW1CLEdBQWtCdkYsT0FBZkksYUFBWSxLQUFZLE9BQVRKO2dCQUN2QyxPQUFPLElBQUlFLFNBQVM7b0JBQ2xCcUYsbUJBQW1CLEdBQWtCckYsT0FBZkUsYUFBWSxLQUFXLE9BQVJGO2dCQUN2QztZQUNGO1lBRUFmLFFBQVFzQixJQUFJLENBQUM7Z0JBQ1hDLE9BQU9IO2dCQUNQSSxPQUFPNEU7Z0JBQ1BsRixNQUFNQTtnQkFDTkQsYUFBYW1GO1lBQ2Y7UUFDRjtRQUVBLE1BQU0zRSxnQkFBZ0J6QixRQUFRMEIsTUFBTSxDQUFDLENBQUNDLFFBQVFDLE9BQU9DLE9BQ25ERCxVQUFVQyxLQUFLQyxTQUFTLENBQUNDLENBQUFBLElBQUtBLEVBQUVSLEtBQUssS0FBS0ksT0FBT0osS0FBSztRQUd4RCxPQUFPRSxjQUFjTyxJQUFJLENBQUMsQ0FBQ0MsR0FBR0MsSUFBTUQsRUFBRVQsS0FBSyxDQUFDVyxhQUFhLENBQUNELEVBQUVWLEtBQUs7SUFDbkU7SUFFQSxJQUFJNUMsU0FBUztRQUNYLHFCQUNFLDhEQUFDeUg7WUFBSUMsV0FBVTs7OEJBQ2IsOERBQUNEO29CQUFJQyxXQUFVOztzQ0FDYiw4REFBQ3RJLGtGQUFNQTs0QkFBQ3NJLFdBQVU7Ozs7OztzQ0FDbEIsOERBQUNDOzRCQUFHRCxXQUFVO3NDQUFzQzs7Ozs7Ozs7Ozs7OzhCQUV0RCw4REFBQ0Q7b0JBQUlDLFdBQVU7OEJBQXdCOzs7Ozs7Ozs7Ozs7SUFHN0M7SUFFQSxxQkFDRSw4REFBQ0Q7UUFBSUMsV0FBVTs7MEJBQ2IsOERBQUNEO2dCQUFJQyxXQUFVOztrQ0FDYiw4REFBQ3RJLGtGQUFNQTt3QkFBQ3NJLFdBQVU7Ozs7OztrQ0FDbEIsOERBQUNDO3dCQUFHRCxXQUFVO2tDQUF1QzlIOzs7Ozs7Ozs7Ozs7MEJBSXZELDhEQUFDNkg7Z0JBQUlDLFdBQVU7O2tDQUViLDhEQUFDRDt3QkFBSUMsV0FBVTtrQ0FDYiw0RUFBQ3ZJLG1FQUFZQTs0QkFDWE0sTUFBTUE7NEJBQ053RixNQUFNLFdBQXlCcEYsT0FBZEgsWUFBVyxLQUFlLE9BQVpHLGFBQVk7NEJBQzNDK0MsT0FBTTs0QkFDTmdGLGFBQVk7NEJBQ1pDLGFBQWE7NEJBQ2JILFdBQVU7NEJBQ1Z0RyxTQUFTNkYseUJBQXlCcEIsR0FBRyxDQUFDLENBQUM5QyxTQUFZO29DQUNqREosT0FBT0ksT0FBT0osS0FBSztvQ0FDbkJDLE9BQU9HLE9BQU9WLFdBQVc7b0NBQ3pCeUYsT0FBTy9FLE9BQU9ULElBQUksS0FBSyxVQUFVLFVBQVU7b0NBQzNDeUYsWUFBWWhGLE9BQU9ULElBQUksS0FBSyxVQUFVLGdCQUFnQjtnQ0FDeEQ7Ozs7Ozs7Ozs7O2tDQUtKLDhEQUFDbUY7d0JBQUlDLFdBQVU7a0NBQ2IsNEVBQUN2SSxtRUFBWUE7NEJBQ1hNLE1BQU1BOzRCQUNOd0YsTUFBTSxXQUF5QnBGLE9BQWRILFlBQVcsS0FBZSxPQUFaRyxhQUFZOzRCQUMzQytDLE9BQU07NEJBQ05nRixhQUFZOzRCQUNaQyxhQUFhOzRCQUNiSCxXQUFVOzRCQUNWdEcsU0FBU3dGLG9CQUFvQmYsR0FBRyxDQUFDLENBQUN0RixVQUFhO29DQUM3Q29DLE9BQU9wQztvQ0FDUHFDLE9BQU9yQztnQ0FDVDs7Ozs7Ozs7Ozs7a0NBS0osOERBQUNrSDt3QkFBSUMsV0FBVTtrQ0FDYiw0RUFBQ3ZJLG1FQUFZQTs0QkFDWE0sTUFBTUE7NEJBQ053RixNQUFNLFdBQXlCcEYsT0FBZEgsWUFBVyxLQUFlLE9BQVpHLGFBQVk7NEJBQzNDK0MsT0FBTTs0QkFDTmdGLGFBQVk7NEJBQ1pDLGFBQWE7NEJBQ2JILFdBQVU7NEJBQ1Z0RyxTQUFTNEYsb0JBQW9CbkIsR0FBRyxDQUFDLENBQUMxRCxVQUFhO29DQUM3Q1EsT0FBT1I7b0NBQ1BTLE9BQU9UO2dDQUNUOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQU1aO0dBbnVCTTNDO0tBQUFBO0FBcXVCTiwrREFBZUEsdUJBQXVCQSxFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL2FwcC91c2VyL3RyYWNrU2hlZXRzL0xlZ3JhbmREZXRhaWxzQ29tcG9uZW50LnRzeD81NDE4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBSZWFjdCwgeyB1c2VTdGF0ZSwgdXNlQ2FsbGJhY2ssIHVzZU1lbW8sIHVzZUVmZmVjdCB9IGZyb20gXCJyZWFjdFwiO1xyXG5pbXBvcnQgeyBVc2VGb3JtUmV0dXJuIH0gZnJvbSBcInJlYWN0LWhvb2stZm9ybVwiO1xyXG5pbXBvcnQgU2VhcmNoU2VsZWN0IGZyb20gXCJAL2FwcC9fY29tcG9uZW50L1NlYXJjaFNlbGVjdFwiO1xyXG5pbXBvcnQgeyBNYXBQaW4gfSBmcm9tIFwibHVjaWRlLXJlYWN0XCI7XHJcbmltcG9ydCB7IGxlZ3JhbmRNYXBwaW5nX3JvdXRlcyB9IGZyb20gXCJAL2xpYi9yb3V0ZVBhdGhcIjtcclxuaW1wb3J0IHsgZ2V0QWxsRGF0YSB9IGZyb20gXCJAL2xpYi9oZWxwZXJzXCI7XHJcbmltcG9ydCB0b2FzdCBmcm9tIFwicmVhY3QtaG90LXRvYXN0XCI7XHJcblxyXG5pbnRlcmZhY2UgTGVncmFuZE1hcHBpbmdEYXRhIHtcclxuICBpZDogc3RyaW5nO1xyXG4gIGJ1c2luZXNzVW5pdDogc3RyaW5nIHwgbnVsbDtcclxuICBsZWdhbE5hbWU6IHN0cmluZyB8IG51bGw7XHJcbiAgY3VzdG9tZUNvZGU6IHN0cmluZyB8IG51bGw7XHJcbiAgc2hpcHBpbmdCaWxsaW5nTmFtZTogc3RyaW5nIHwgbnVsbDtcclxuICBzaGlwcGluZ0JpbGxpbmdBZGRyZXNzOiBzdHJpbmcgfCBudWxsO1xyXG4gIGxvY2F0aW9uOiBzdHJpbmcgfCBudWxsO1xyXG4gIHppcFBvc3RhbDogc3RyaW5nIHwgbnVsbDtcclxuICBhbGlhc0NpdHk6IHN0cmluZyB8IG51bGw7XHJcbiAgYWxpYXNTaGlwcGluZ05hbWVzOiBzdHJpbmcgfCBudWxsO1xyXG4gIGNvcnBvcmF0aW9uSWQ6IG51bWJlciB8IG51bGw7XHJcbiAgY3JlYXRlZEF0OiBzdHJpbmc7XHJcbiAgdXBkYXRlZEF0OiBzdHJpbmc7XHJcbn1cclxuXHJcbmludGVyZmFjZSBMZWdyYW5kRGV0YWlsc0NvbXBvbmVudFByb3BzIHtcclxuICBmb3JtOiBVc2VGb3JtUmV0dXJuPGFueT47XHJcbiAgZW50cnlJbmRleDogbnVtYmVyO1xyXG4gIG9uTGVncmFuZERhdGFDaGFuZ2U/OiAoZW50cnlJbmRleDogbnVtYmVyLCBidXNpbmVzc1VuaXQ6IHN0cmluZywgZGl2aXNpb25Db2RlOiBzdHJpbmcpID0+IHZvaWQ7XHJcbiAgYmxvY2tUaXRsZT86IHN0cmluZztcclxuICBmaWVsZFByZWZpeD86IHN0cmluZztcclxufVxyXG5cclxuY29uc3QgTGVncmFuZERldGFpbHNDb21wb25lbnQ6IFJlYWN0LkZDPExlZ3JhbmREZXRhaWxzQ29tcG9uZW50UHJvcHM+ID0gKHtcclxuICBmb3JtLFxyXG4gIGVudHJ5SW5kZXgsXHJcbiAgb25MZWdyYW5kRGF0YUNoYW5nZSxcclxuICBibG9ja1RpdGxlID0gXCJMRUdSQU5EIERldGFpbHNcIixcclxuICBmaWVsZFByZWZpeCA9IFwibGVncmFuZFwiXHJcbn0pID0+IHtcclxuICBjb25zdCBbbGVncmFuZERhdGEsIHNldExlZ3JhbmREYXRhXSA9IHVzZVN0YXRlPExlZ3JhbmRNYXBwaW5nRGF0YVtdPihbXSk7XHJcbiAgY29uc3QgW2xvYWRpbmcsIHNldExvYWRpbmddID0gdXNlU3RhdGUodHJ1ZSk7XHJcblxyXG4gIGNvbnN0IGZldGNoTGVncmFuZERhdGEgPSB1c2VDYWxsYmFjayhhc3luYyAoKSA9PiB7XHJcbiAgICB0cnkge1xyXG4gICAgICBzZXRMb2FkaW5nKHRydWUpO1xyXG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGdldEFsbERhdGEobGVncmFuZE1hcHBpbmdfcm91dGVzLkdFVF9MRUdSQU5EX01BUFBJTkdTKTtcclxuICAgICAgc2V0TGVncmFuZERhdGEocmVzcG9uc2UgfHwgW10pO1xyXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgdG9hc3QuZXJyb3IoXCJFcnJvciBmZXRjaGluZyBMRUdSQU5EIG1hcHBpbmcgZGF0YTpcIiwgZXJyb3IpO1xyXG4gICAgICBzZXRMZWdyYW5kRGF0YShbXSk7XHJcbiAgICB9IGZpbmFsbHkge1xyXG4gICAgICBzZXRMb2FkaW5nKGZhbHNlKTtcclxuICAgIH1cclxuICB9LCBbXSk7XHJcblxyXG4gIHVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgICBmZXRjaExlZ3JhbmREYXRhKCk7XHJcbiAgfSwgW2ZldGNoTGVncmFuZERhdGFdKTtcclxuXHJcbiAgY29uc3QgZXh0cmFjdENpdHlGcm9tQWRkcmVzcyA9IChhZGRyZXNzOiBzdHJpbmcpOiBzdHJpbmcgPT4ge1xyXG4gICAgaWYgKCFhZGRyZXNzKSByZXR1cm4gJyc7XHJcblxyXG4gICAgY29uc3QgcGFydHMgPSBhZGRyZXNzLnNwbGl0KCcsJyk7XHJcbiAgICBpZiAocGFydHMubGVuZ3RoID49IDIpIHtcclxuICAgICAgY29uc3QgY2l0eVBhcnQgPSBwYXJ0c1twYXJ0cy5sZW5ndGggLSAyXS50cmltKCk7XHJcbiAgICAgIGNvbnN0IGNpdHkgPSBjaXR5UGFydC5yZXBsYWNlKC9cXGQrL2csICcnKS50cmltKCk7XHJcbiAgICAgIHJldHVybiBjaXR5O1xyXG4gICAgfVxyXG5cclxuICAgIGNvbnN0IHdvcmRzID0gYWRkcmVzcy5zcGxpdCgnICcpO1xyXG4gICAgZm9yIChjb25zdCB3b3JkIG9mIHdvcmRzKSB7XHJcbiAgICAgIGlmICh3b3JkLmxlbmd0aCA+IDIgJiYgIS9eXFxkKyQvLnRlc3Qod29yZCkgJiYgIXdvcmQuaW5jbHVkZXMoJ1JPQUQnKSAmJiAhd29yZC5pbmNsdWRlcygnU1RSRUVUJykgJiYgIXdvcmQuaW5jbHVkZXMoJ0FWRScpKSB7XHJcbiAgICAgICAgcmV0dXJuIHdvcmQ7XHJcbiAgICAgIH1cclxuICAgIH1cclxuXHJcbiAgICByZXR1cm4gJyc7XHJcbiAgfTtcclxuXHJcbiAgY29uc3QgZ2V0QWxpYXNPcHRpb25zID0gdXNlTWVtbygoKSA9PiB7XHJcbiAgICBpZiAoIWxlZ3JhbmREYXRhLmxlbmd0aCkgcmV0dXJuIFtdO1xyXG5cclxuICAgIGNvbnN0IG9wdGlvbnM6IEFycmF5PHtcclxuICAgICAgdmFsdWU6IHN0cmluZztcclxuICAgICAgbGFiZWw6IHN0cmluZztcclxuICAgICAgdHlwZTogJ2FsaWFzJyB8ICdjb21wYW55JztcclxuICAgICAgZGlzcGxheU5hbWU6IHN0cmluZztcclxuICAgIH0+ID0gW107XHJcblxyXG4gICAgLy8gUHJlLWNvbXB1dGUgYmFzZSBuYW1lcyB0byBhdm9pZCByZXBlYXRlZCBjYWxjdWxhdGlvbnNcclxuICAgIGNvbnN0IGJhc2VOYW1lTWFwID0gbmV3IE1hcDxzdHJpbmcsIHN0cmluZz4oKTtcclxuICAgIGNvbnN0IG5hbWVDb3VudE1hcCA9IG5ldyBNYXA8c3RyaW5nLCBudW1iZXI+KCk7XHJcblxyXG4gICAgLy8gRmlyc3QgcGFzczogY29tcHV0ZSBiYXNlIG5hbWVzIGFuZCBjb3VudCBvY2N1cnJlbmNlc1xyXG4gICAgbGVncmFuZERhdGEuZm9yRWFjaChkYXRhID0+IHtcclxuICAgICAgY29uc3QgbGVnYWxOYW1lID0gZGF0YS5sZWdhbE5hbWUgfHwgJ1Vua25vd24gQ29tcGFueSc7XHJcbiAgICAgIGNvbnN0IGFsaWFzTmFtZSA9IGRhdGEuYWxpYXNTaGlwcGluZ05hbWVzICYmIGRhdGEuYWxpYXNTaGlwcGluZ05hbWVzICE9PSAnTk9ORScgPyBkYXRhLmFsaWFzU2hpcHBpbmdOYW1lcyA6IG51bGw7XHJcbiAgICAgIGNvbnN0IGJhc2VOYW1lID0gYWxpYXNOYW1lID8gYCR7bGVnYWxOYW1lfSAoJHthbGlhc05hbWV9KWAgOiBsZWdhbE5hbWU7XHJcblxyXG4gICAgICBiYXNlTmFtZU1hcC5zZXQoZGF0YS5pZCwgYmFzZU5hbWUpO1xyXG4gICAgICBuYW1lQ291bnRNYXAuc2V0KGJhc2VOYW1lLCAobmFtZUNvdW50TWFwLmdldChiYXNlTmFtZSkgfHwgMCkgKyAxKTtcclxuICAgIH0pO1xyXG5cclxuICAgIC8vIFNlY29uZCBwYXNzOiBidWlsZCBvcHRpb25zIHdpdGggb3B0aW1pemVkIGR1cGxpY2F0ZSBjaGVja2luZ1xyXG4gICAgbGVncmFuZERhdGEuZm9yRWFjaChkYXRhID0+IHtcclxuICAgICAgY29uc3QgbG9jYXRpb24gPSBkYXRhLmxvY2F0aW9uIHx8IGV4dHJhY3RDaXR5RnJvbUFkZHJlc3MoZGF0YS5zaGlwcGluZ0JpbGxpbmdBZGRyZXNzIHx8ICcnKTtcclxuICAgICAgY29uc3QgemlwY29kZSA9IGRhdGEuemlwUG9zdGFsIHx8ICcnO1xyXG4gICAgICBjb25zdCBsZWdhbE5hbWUgPSBkYXRhLmxlZ2FsTmFtZSB8fCAnVW5rbm93biBDb21wYW55JztcclxuICAgICAgY29uc3QgYWxpYXNOYW1lID0gZGF0YS5hbGlhc1NoaXBwaW5nTmFtZXMgJiYgZGF0YS5hbGlhc1NoaXBwaW5nTmFtZXMgIT09ICdOT05FJyA/IGRhdGEuYWxpYXNTaGlwcGluZ05hbWVzIDogbnVsbDtcclxuXHJcbiAgICAgIGxldCBkaXNwbGF5TmFtZSA9IGJhc2VOYW1lTWFwLmdldChkYXRhLmlkKSB8fCBsZWdhbE5hbWU7XHJcbiAgICAgIGNvbnN0IHR5cGU6ICdhbGlhcycgfCAnY29tcGFueScgPSBhbGlhc05hbWUgPyAnYWxpYXMnIDogJ2NvbXBhbnknO1xyXG5cclxuICAgICAgLy8gT25seSBhZGQgbG9jYXRpb24vemlwY29kZSBzdWZmaXggaWYgdGhlcmUgYXJlIGR1cGxpY2F0ZXNcclxuICAgICAgY29uc3QgbmFtZUNvdW50ID0gbmFtZUNvdW50TWFwLmdldChkaXNwbGF5TmFtZSkgfHwgMTtcclxuICAgICAgaWYgKG5hbWVDb3VudCA+IDEpIHtcclxuICAgICAgICBpZiAobG9jYXRpb24pIHtcclxuICAgICAgICAgIGRpc3BsYXlOYW1lID0gYCR7ZGlzcGxheU5hbWV9XyR7bG9jYXRpb259YDtcclxuICAgICAgICB9IGVsc2UgaWYgKHppcGNvZGUpIHtcclxuICAgICAgICAgIGRpc3BsYXlOYW1lID0gYCR7ZGlzcGxheU5hbWV9XyR7emlwY29kZX1gO1xyXG4gICAgICAgIH1cclxuICAgICAgfVxyXG5cclxuICAgICAgY29uc3QgdW5pcXVlS2V5ID0gYCR7ZGF0YS5jdXN0b21lQ29kZX0tJHtkYXRhLmFsaWFzU2hpcHBpbmdOYW1lcyB8fCBkYXRhLmxlZ2FsTmFtZX0tJHtkYXRhLnNoaXBwaW5nQmlsbGluZ0FkZHJlc3N9YDtcclxuXHJcbiAgICAgIG9wdGlvbnMucHVzaCh7XHJcbiAgICAgICAgdmFsdWU6IHVuaXF1ZUtleSxcclxuICAgICAgICBsYWJlbDogZGlzcGxheU5hbWUsXHJcbiAgICAgICAgdHlwZTogdHlwZSxcclxuICAgICAgICBkaXNwbGF5TmFtZTogZGlzcGxheU5hbWVcclxuICAgICAgfSk7XHJcbiAgICB9KTtcclxuXHJcbiAgICAvLyBSZW1vdmUgZHVwbGljYXRlcyBhbmQgc29ydFxyXG4gICAgY29uc3QgdW5pcXVlT3B0aW9ucyA9IG9wdGlvbnMuZmlsdGVyKChvcHRpb24sIGluZGV4LCBzZWxmKSA9PlxyXG4gICAgICBpbmRleCA9PT0gc2VsZi5maW5kSW5kZXgobyA9PiBvLnZhbHVlID09PSBvcHRpb24udmFsdWUpXHJcbiAgICApO1xyXG5cclxuICAgIHJldHVybiB1bmlxdWVPcHRpb25zLnNvcnQoKGEsIGIpID0+IGEubGFiZWwubG9jYWxlQ29tcGFyZShiLmxhYmVsKSk7XHJcbiAgfSwgW2xlZ3JhbmREYXRhXSk7XHJcblxyXG4gIGNvbnN0IGdldEJhc2VOYW1lID0gdXNlQ2FsbGJhY2soKGRhdGE6IExlZ3JhbmRNYXBwaW5nRGF0YSk6IHN0cmluZyA9PiB7XHJcbiAgICBjb25zdCBsZWdhbE5hbWUgPSBkYXRhLmxlZ2FsTmFtZSB8fCAnVW5rbm93biBDb21wYW55JztcclxuICAgIGNvbnN0IGFsaWFzTmFtZSA9IGRhdGEuYWxpYXNTaGlwcGluZ05hbWVzICYmIGRhdGEuYWxpYXNTaGlwcGluZ05hbWVzICE9PSAnTk9ORScgPyBkYXRhLmFsaWFzU2hpcHBpbmdOYW1lcyA6IG51bGw7XHJcblxyXG4gICAgaWYgKGFsaWFzTmFtZSkge1xyXG4gICAgICByZXR1cm4gYCR7bGVnYWxOYW1lfSAoJHthbGlhc05hbWV9KWA7XHJcbiAgICB9IGVsc2Uge1xyXG4gICAgICByZXR1cm4gbGVnYWxOYW1lO1xyXG4gICAgfVxyXG4gIH0sIFtdKTtcclxuXHJcbiAgY29uc3QgY2hlY2tGb3JEdXBsaWNhdGVzID0gdXNlQ2FsbGJhY2soKGZpZWxkOiAnYWxpYXMnIHwgJ2FkZHJlc3MnIHwgJ3ppcGNvZGUnLCB2YWx1ZTogc3RyaW5nKSA9PiB7XHJcbiAgICBsZXQgbWF0Y2hpbmdFbnRyaWVzOiBMZWdyYW5kTWFwcGluZ0RhdGFbXSA9IFtdO1xyXG5cclxuICAgIGlmIChmaWVsZCA9PT0gJ2FsaWFzJykge1xyXG4gICAgICBjb25zdCBleGFjdE1hdGNoID0gbGVncmFuZERhdGEuZmluZChkYXRhID0+IHtcclxuICAgICAgICBjb25zdCB1bmlxdWVLZXkgPSBgJHtkYXRhLmN1c3RvbWVDb2RlfS0ke2RhdGEuYWxpYXNTaGlwcGluZ05hbWVzIHx8IGRhdGEubGVnYWxOYW1lfS0ke2RhdGEuc2hpcHBpbmdCaWxsaW5nQWRkcmVzc31gO1xyXG4gICAgICAgIHJldHVybiB1bmlxdWVLZXkgPT09IHZhbHVlO1xyXG4gICAgICB9KTtcclxuXHJcbiAgICAgIGlmIChleGFjdE1hdGNoKSB7XHJcbiAgICAgICAgY29uc3QgYmFzZU5hbWUgPSBnZXRCYXNlTmFtZShleGFjdE1hdGNoKTtcclxuICAgICAgICBjb25zdCBzYW1lQmFzZU5hbWVFbnRyaWVzID0gbGVncmFuZERhdGEuZmlsdGVyKGRhdGEgPT4gZ2V0QmFzZU5hbWUoZGF0YSkgPT09IGJhc2VOYW1lKTtcclxuXHJcbiAgICAgICAgaWYgKHNhbWVCYXNlTmFtZUVudHJpZXMubGVuZ3RoID09PSAxKSB7XHJcbiAgICAgICAgICBtYXRjaGluZ0VudHJpZXMgPSBbZXhhY3RNYXRjaF07IFxyXG4gICAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgICBjb25zdCBzZWxlY3RlZERpc3BsYXlOYW1lID0gZ2V0QWxpYXNPcHRpb25zKCkuZmluZChvcHRpb24gPT4gb3B0aW9uLnZhbHVlID09PSB2YWx1ZSk/LmRpc3BsYXlOYW1lIHx8ICcnO1xyXG5cclxuICAgICAgICAgIGNvbnN0IHNhbWVEaXNwbGF5TmFtZUVudHJpZXMgPSBsZWdyYW5kRGF0YS5maWx0ZXIoZGF0YSA9PiB7XHJcbiAgICAgICAgICAgIGNvbnN0IGxvY2F0aW9uID0gZGF0YS5sb2NhdGlvbiB8fCBleHRyYWN0Q2l0eUZyb21BZGRyZXNzKGRhdGEuc2hpcHBpbmdCaWxsaW5nQWRkcmVzcyB8fCAnJyk7XHJcbiAgICAgICAgICAgIGxldCBkaXNwbGF5TmFtZSA9IGdldEJhc2VOYW1lKGRhdGEpO1xyXG5cclxuICAgICAgICAgICAgY29uc3Qgc2FtZU5hbWVFbnRyaWVzID0gbGVncmFuZERhdGEuZmlsdGVyKGQgPT4gZ2V0QmFzZU5hbWUoZCkgPT09IGRpc3BsYXlOYW1lKTtcclxuICAgICAgICAgICAgaWYgKHNhbWVOYW1lRW50cmllcy5sZW5ndGggPiAxKSB7XHJcbiAgICAgICAgICAgICAgaWYgKGxvY2F0aW9uKSB7XHJcbiAgICAgICAgICAgICAgICBkaXNwbGF5TmFtZSA9IGAke2Rpc3BsYXlOYW1lfV8ke2xvY2F0aW9ufWA7XHJcbiAgICAgICAgICAgICAgfSBlbHNlIGlmIChkYXRhLnppcFBvc3RhbCkge1xyXG4gICAgICAgICAgICAgICAgZGlzcGxheU5hbWUgPSBgJHtkaXNwbGF5TmFtZX1fJHtkYXRhLnppcFBvc3RhbH1gO1xyXG4gICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgfVxyXG5cclxuICAgICAgICAgICAgcmV0dXJuIGRpc3BsYXlOYW1lID09PSBzZWxlY3RlZERpc3BsYXlOYW1lO1xyXG4gICAgICAgICAgfSk7XHJcblxyXG4gICAgICAgICAgbWF0Y2hpbmdFbnRyaWVzID0gc2FtZURpc3BsYXlOYW1lRW50cmllcztcclxuICAgICAgICB9XHJcbiAgICAgIH1cclxuICAgIH0gZWxzZSBpZiAoZmllbGQgPT09ICdhZGRyZXNzJykge1xyXG4gICAgICBtYXRjaGluZ0VudHJpZXMgPSBsZWdyYW5kRGF0YS5maWx0ZXIoZGF0YSA9PiBkYXRhLnNoaXBwaW5nQmlsbGluZ0FkZHJlc3MgPT09IHZhbHVlKTtcclxuICAgIH0gZWxzZSBpZiAoZmllbGQgPT09ICd6aXBjb2RlJykge1xyXG4gICAgICBtYXRjaGluZ0VudHJpZXMgPSBsZWdyYW5kRGF0YS5maWx0ZXIoZGF0YSA9PiBkYXRhLnppcFBvc3RhbCA9PT0gdmFsdWUpO1xyXG4gICAgfVxyXG5cclxuICAgIHJldHVybiBtYXRjaGluZ0VudHJpZXM7XHJcbiAgfSwgW2xlZ3JhbmREYXRhLCBnZXRCYXNlTmFtZSwgZ2V0QWxpYXNPcHRpb25zXSk7XHJcblxyXG4gIGNvbnN0IGNsZWFyT3RoZXJCbG9ja3MgPSB1c2VDYWxsYmFjaygoY3VycmVudFByZWZpeDogc3RyaW5nKSA9PiB7XHJcbiAgICBjb25zdCBwcmVmaXhlcyA9IFsnc2hpcHBlcicsICdjb25zaWduZWUnLCAnYmlsbHRvJ107XHJcbiAgICBjb25zdCBvdGhlclByZWZpeGVzID0gcHJlZml4ZXMuZmlsdGVyKHAgPT4gcCAhPT0gY3VycmVudFByZWZpeCk7XHJcblxyXG4gICAgb3RoZXJQcmVmaXhlcy5mb3JFYWNoKHByZWZpeCA9PiB7XHJcbiAgICAgIGNvbnN0IGN1cnJlbnRBbGlhcyA9IGZvcm0uZ2V0VmFsdWVzKGBlbnRyaWVzLiR7ZW50cnlJbmRleH0uJHtwcmVmaXh9QWxpYXNgKTtcclxuICAgICAgY29uc3QgY3VycmVudEFkZHJlc3MgPSBmb3JtLmdldFZhbHVlcyhgZW50cmllcy4ke2VudHJ5SW5kZXh9LiR7cHJlZml4fUFkZHJlc3NgKTtcclxuICAgICAgY29uc3QgY3VycmVudFppcGNvZGUgPSBmb3JtLmdldFZhbHVlcyhgZW50cmllcy4ke2VudHJ5SW5kZXh9LiR7cHJlZml4fVppcGNvZGVgKTtcclxuXHJcbiAgICAgIGlmIChjdXJyZW50QWxpYXMgfHwgY3VycmVudEFkZHJlc3MgfHwgY3VycmVudFppcGNvZGUpIHtcclxuICAgICAgICBmb3JtLnNldFZhbHVlKGBlbnRyaWVzLiR7ZW50cnlJbmRleH0uJHtwcmVmaXh9QWxpYXNgLCAnJyk7XHJcbiAgICAgICAgZm9ybS5zZXRWYWx1ZShgZW50cmllcy4ke2VudHJ5SW5kZXh9LiR7cHJlZml4fUFkZHJlc3NgLCAnJyk7XHJcbiAgICAgICAgZm9ybS5zZXRWYWx1ZShgZW50cmllcy4ke2VudHJ5SW5kZXh9LiR7cHJlZml4fVppcGNvZGVgLCAnJyk7XHJcbiAgICAgIH1cclxuICAgIH0pO1xyXG4gIH0sIFtmb3JtLCBlbnRyeUluZGV4XSk7XHJcblxyXG4gIHVzZU1lbW8oKCkgPT4ge1xyXG4gICAgaWYgKCFsZWdyYW5kRGF0YS5sZW5ndGgpIHJldHVybjtcclxuXHJcbiAgICBjb25zdCBzdWJzY3JpcHRpb24gPSBmb3JtLndhdGNoKChfLCB7IG5hbWUsIHR5cGUgfSkgPT4ge1xyXG4gICAgICBpZiAoIW5hbWUgfHwgIW5hbWUuaW5jbHVkZXMoYGVudHJpZXMuJHtlbnRyeUluZGV4fS4ke2ZpZWxkUHJlZml4fWApIHx8IHR5cGUgIT09ICdjaGFuZ2UnKSByZXR1cm47XHJcblxyXG4gICAgICBjb25zdCBjdXJyZW50QWxpYXMgPSBmb3JtLmdldFZhbHVlcyhgZW50cmllcy4ke2VudHJ5SW5kZXh9LiR7ZmllbGRQcmVmaXh9QWxpYXNgKTtcclxuICAgICAgY29uc3QgY3VycmVudEFkZHJlc3MgPSBmb3JtLmdldFZhbHVlcyhgZW50cmllcy4ke2VudHJ5SW5kZXh9LiR7ZmllbGRQcmVmaXh9QWRkcmVzc2ApO1xyXG4gICAgICBjb25zdCBjdXJyZW50WmlwY29kZSA9IGZvcm0uZ2V0VmFsdWVzKGBlbnRyaWVzLiR7ZW50cnlJbmRleH0uJHtmaWVsZFByZWZpeH1aaXBjb2RlYCk7XHJcblxyXG4gICAgICBpZiAoY3VycmVudEFsaWFzIHx8IGN1cnJlbnRBZGRyZXNzIHx8IGN1cnJlbnRaaXBjb2RlKSB7XHJcbiAgICAgICAgY2xlYXJPdGhlckJsb2NrcyhmaWVsZFByZWZpeCk7XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIGlmIChuYW1lID09PSBgZW50cmllcy4ke2VudHJ5SW5kZXh9LiR7ZmllbGRQcmVmaXh9QWxpYXNgICYmIGN1cnJlbnRBbGlhcykge1xyXG4gICAgICAgIGNvbnN0IHNlbGVjdGVkRGF0YSA9IGxlZ3JhbmREYXRhLmZpbmQoZGF0YSA9PiB7XHJcbiAgICAgICAgICBjb25zdCB1bmlxdWVLZXkgPSBgJHtkYXRhLmN1c3RvbWVDb2RlfS0ke2RhdGEuYWxpYXNTaGlwcGluZ05hbWVzIHx8IGRhdGEubGVnYWxOYW1lfS0ke2RhdGEuc2hpcHBpbmdCaWxsaW5nQWRkcmVzc31gO1xyXG4gICAgICAgICAgcmV0dXJuIHVuaXF1ZUtleSA9PT0gY3VycmVudEFsaWFzO1xyXG4gICAgICAgIH0pO1xyXG5cclxuICAgICAgICBpZiAoc2VsZWN0ZWREYXRhKSB7XHJcbiAgICAgICAgICBjb25zdCBkdXBsaWNhdGVFbnRyaWVzID0gY2hlY2tGb3JEdXBsaWNhdGVzKCdhbGlhcycsIGN1cnJlbnRBbGlhcyk7XHJcblxyXG4gICAgICAgICAgaWYgKGR1cGxpY2F0ZUVudHJpZXMubGVuZ3RoID4gMSkge1xyXG4gICAgICAgICAgICBpZiAoY3VycmVudEFkZHJlc3MpIHtcclxuICAgICAgICAgICAgICBjb25zdCBhZGRyZXNzTWF0Y2hlc0FsaWFzID0gZHVwbGljYXRlRW50cmllcy5zb21lKGVudHJ5ID0+IGVudHJ5LnNoaXBwaW5nQmlsbGluZ0FkZHJlc3MgPT09IGN1cnJlbnRBZGRyZXNzKTtcclxuXHJcbiAgICAgICAgICAgICAgaWYgKGFkZHJlc3NNYXRjaGVzQWxpYXMpIHtcclxuICAgICAgICAgICAgICAgIGNvbnN0IHNwZWNpZmljRW50cnkgPSBkdXBsaWNhdGVFbnRyaWVzLmZpbmQoZW50cnkgPT4gZW50cnkuc2hpcHBpbmdCaWxsaW5nQWRkcmVzcyA9PT0gY3VycmVudEFkZHJlc3MpO1xyXG4gICAgICAgICAgICAgICAgaWYgKHNwZWNpZmljRW50cnkgJiYgY3VycmVudFppcGNvZGUgIT09IHNwZWNpZmljRW50cnkuemlwUG9zdGFsKSB7XHJcbiAgICAgICAgICAgICAgICAgIGZvcm0uc2V0VmFsdWUoYGVudHJpZXMuJHtlbnRyeUluZGV4fS4ke2ZpZWxkUHJlZml4fVppcGNvZGVgLCBzcGVjaWZpY0VudHJ5LnppcFBvc3RhbCk7XHJcbiAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgICAgICAgIGZvcm0uc2V0VmFsdWUoYGVudHJpZXMuJHtlbnRyeUluZGV4fS4ke2ZpZWxkUHJlZml4fUFkZHJlc3NgLCAnJyk7XHJcbiAgICAgICAgICAgICAgICBpZiAoY3VycmVudFppcGNvZGUgIT09ICcnKSB7XHJcbiAgICAgICAgICAgICAgICAgIGZvcm0uc2V0VmFsdWUoYGVudHJpZXMuJHtlbnRyeUluZGV4fS4ke2ZpZWxkUHJlZml4fVppcGNvZGVgLCAnJyk7XHJcbiAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgICAgIGlmIChjdXJyZW50WmlwY29kZSkge1xyXG4gICAgICAgICAgICAgICAgY29uc3QgemlwY29kZU1hdGNoZXNBbGlhcyA9IGR1cGxpY2F0ZUVudHJpZXMuc29tZShlbnRyeSA9PiBlbnRyeS56aXBQb3N0YWwgPT09IGN1cnJlbnRaaXBjb2RlKTtcclxuXHJcbiAgICAgICAgICAgICAgICBpZiAoemlwY29kZU1hdGNoZXNBbGlhcykge1xyXG4gICAgICAgICAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgICAgICAgICAgZm9ybS5zZXRWYWx1ZShgZW50cmllcy4ke2VudHJ5SW5kZXh9LiR7ZmllbGRQcmVmaXh9WmlwY29kZWAsICcnKTtcclxuICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgfVxyXG4gICAgICAgICAgfSBlbHNlIHtcclxuXHJcbiAgICAgICAgICAgIGlmIChjdXJyZW50QWRkcmVzcyAhPT0gc2VsZWN0ZWREYXRhLnNoaXBwaW5nQmlsbGluZ0FkZHJlc3MpIHtcclxuICAgICAgICAgICAgICBmb3JtLnNldFZhbHVlKGBlbnRyaWVzLiR7ZW50cnlJbmRleH0uJHtmaWVsZFByZWZpeH1BZGRyZXNzYCwgc2VsZWN0ZWREYXRhLnNoaXBwaW5nQmlsbGluZ0FkZHJlc3MpO1xyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgICAgIGlmIChjdXJyZW50WmlwY29kZSAhPT0gc2VsZWN0ZWREYXRhLnppcFBvc3RhbCkge1xyXG4gICAgICAgICAgICAgIGZvcm0uc2V0VmFsdWUoYGVudHJpZXMuJHtlbnRyeUluZGV4fS4ke2ZpZWxkUHJlZml4fVppcGNvZGVgLCBzZWxlY3RlZERhdGEuemlwUG9zdGFsKTtcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgICAgfVxyXG5cclxuICAgICAgICAgIGNvbnN0IGN1cnJlbnRDb21wYW55ID0gZm9ybS5nZXRWYWx1ZXMoYGVudHJpZXMuJHtlbnRyeUluZGV4fS5jb21wYW55YCk7XHJcbiAgICAgICAgICBpZiAoY3VycmVudENvbXBhbnkgIT09IHNlbGVjdGVkRGF0YS5idXNpbmVzc1VuaXQpIHtcclxuICAgICAgICAgICAgZm9ybS5zZXRWYWx1ZShgZW50cmllcy4ke2VudHJ5SW5kZXh9LmNvbXBhbnlgLCBzZWxlY3RlZERhdGEuYnVzaW5lc3NVbml0KTtcclxuICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICBjb25zdCBhbGxEaXZpc2lvbnM6IHN0cmluZ1tdID0gW107XHJcbiAgICAgICAgICBkdXBsaWNhdGVFbnRyaWVzLmZvckVhY2goZW50cnkgPT4ge1xyXG4gICAgICAgICAgICBpZiAoZW50cnkuY3VzdG9tZUNvZGUpIHtcclxuICAgICAgICAgICAgICBpZiAoZW50cnkuY3VzdG9tZUNvZGUuaW5jbHVkZXMoJy8nKSkge1xyXG4gICAgICAgICAgICAgICAgY29uc3Qgc3BsaXREaXZpc2lvbnMgPSBlbnRyeS5jdXN0b21lQ29kZS5zcGxpdCgnLycpLm1hcChkID0+IGQudHJpbSgpKTtcclxuICAgICAgICAgICAgICAgIGFsbERpdmlzaW9ucy5wdXNoKC4uLnNwbGl0RGl2aXNpb25zKTtcclxuICAgICAgICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgICAgICAgYWxsRGl2aXNpb25zLnB1c2goZW50cnkuY3VzdG9tZUNvZGUpO1xyXG4gICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgfVxyXG4gICAgICAgICAgfSk7XHJcbiAgICAgICAgICBjb25zdCB1bmlxdWVEaXZpc2lvbnMgPSBBcnJheS5mcm9tKG5ldyBTZXQoYWxsRGl2aXNpb25zLmZpbHRlcihjb2RlID0+IGNvZGUpKSk7XHJcblxyXG4gICAgICAgICAgaWYgKHVuaXF1ZURpdmlzaW9ucy5sZW5ndGggPT09IDEpIHtcclxuICAgICAgICAgICAgY29uc3QgY3VycmVudERpdmlzaW9uID0gZm9ybS5nZXRWYWx1ZXMoYGVudHJpZXMuJHtlbnRyeUluZGV4fS5kaXZpc2lvbmApO1xyXG4gICAgICAgICAgICBpZiAoY3VycmVudERpdmlzaW9uICE9PSB1bmlxdWVEaXZpc2lvbnNbMF0pIHtcclxuICAgICAgICAgICAgICBmb3JtLnNldFZhbHVlKGBlbnRyaWVzLiR7ZW50cnlJbmRleH0uZGl2aXNpb25gLCB1bmlxdWVEaXZpc2lvbnNbMF0pO1xyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgICBjb25zdCBjdXJyZW50RGl2aXNpb24gPSBmb3JtLmdldFZhbHVlcyhgZW50cmllcy4ke2VudHJ5SW5kZXh9LmRpdmlzaW9uYCk7XHJcbiAgICAgICAgICAgIGlmIChjdXJyZW50RGl2aXNpb24gIT09ICcnKSB7XHJcbiAgICAgICAgICAgICAgZm9ybS5zZXRWYWx1ZShgZW50cmllcy4ke2VudHJ5SW5kZXh9LmRpdmlzaW9uYCwgJycpO1xyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgaWYgKG9uTGVncmFuZERhdGFDaGFuZ2UpIHtcclxuICAgICAgICAgICAgY29uc3QgZGl2aXNpb25Db2RlVG9QYXNzID0gdW5pcXVlRGl2aXNpb25zLmxlbmd0aCA9PT0gMSA/IHVuaXF1ZURpdmlzaW9uc1swXSA6ICcnO1xyXG4gICAgICAgICAgICBvbkxlZ3JhbmREYXRhQ2hhbmdlKGVudHJ5SW5kZXgsIHNlbGVjdGVkRGF0YS5idXNpbmVzc1VuaXQgfHwgJycsIGRpdmlzaW9uQ29kZVRvUGFzcyk7XHJcbiAgICAgICAgICB9XHJcbiAgICAgICAgfVxyXG4gICAgICB9XHJcblxyXG4gICAgICBpZiAobmFtZSA9PT0gYGVudHJpZXMuJHtlbnRyeUluZGV4fS4ke2ZpZWxkUHJlZml4fUFkZHJlc3NgICYmIGN1cnJlbnRBZGRyZXNzKSB7XHJcbiAgICAgICAgY29uc3QgZHVwbGljYXRlRW50cmllcyA9IGNoZWNrRm9yRHVwbGljYXRlcygnYWRkcmVzcycsIGN1cnJlbnRBZGRyZXNzKTtcclxuXHJcbiAgICAgICAgaWYgKGR1cGxpY2F0ZUVudHJpZXMubGVuZ3RoID4gMSkge1xyXG4gICAgICAgICAgaWYgKGN1cnJlbnRBbGlhcykge1xyXG4gICAgICAgICAgICBjb25zdCBzZWxlY3RlZEFsaWFzRGF0YSA9IGxlZ3JhbmREYXRhLmZpbmQoZGF0YSA9PiB7XHJcbiAgICAgICAgICAgICAgY29uc3QgdW5pcXVlS2V5ID0gYCR7ZGF0YS5jdXN0b21lQ29kZX0tJHtkYXRhLmFsaWFzU2hpcHBpbmdOYW1lcyB8fCBkYXRhLmxlZ2FsTmFtZX0tJHtkYXRhLnNoaXBwaW5nQmlsbGluZ0FkZHJlc3N9YDtcclxuICAgICAgICAgICAgICByZXR1cm4gdW5pcXVlS2V5ID09PSBjdXJyZW50QWxpYXM7XHJcbiAgICAgICAgICAgIH0pO1xyXG5cclxuICAgICAgICAgICAgaWYgKHNlbGVjdGVkQWxpYXNEYXRhKSB7XHJcbiAgICAgICAgICAgICAgY29uc3QgYmFzZU5hbWUgPSBnZXRCYXNlTmFtZShzZWxlY3RlZEFsaWFzRGF0YSk7XHJcblxyXG4gICAgICAgICAgICAgIGNvbnN0IGFkZHJlc3NNYXRjaGVzQmFzZU5hbWUgPSBsZWdyYW5kRGF0YS5zb21lKGRhdGEgPT5cclxuICAgICAgICAgICAgICAgIGdldEJhc2VOYW1lKGRhdGEpID09PSBiYXNlTmFtZSAmJiBkYXRhLnNoaXBwaW5nQmlsbGluZ0FkZHJlc3MgPT09IGN1cnJlbnRBZGRyZXNzXHJcbiAgICAgICAgICAgICAgKTtcclxuXHJcbiAgICAgICAgICAgICAgaWYgKGFkZHJlc3NNYXRjaGVzQmFzZU5hbWUpIHtcclxuICAgICAgICAgICAgICAgIGNvbnN0IGFkZHJlc3NTcGVjaWZpY0VudHJ5ID0gbGVncmFuZERhdGEuZmluZChkYXRhID0+XHJcbiAgICAgICAgICAgICAgICAgIGdldEJhc2VOYW1lKGRhdGEpID09PSBiYXNlTmFtZSAmJiBkYXRhLnNoaXBwaW5nQmlsbGluZ0FkZHJlc3MgPT09IGN1cnJlbnRBZGRyZXNzXHJcbiAgICAgICAgICAgICAgICApO1xyXG5cclxuICAgICAgICAgICAgICAgIGlmIChhZGRyZXNzU3BlY2lmaWNFbnRyeSkge1xyXG4gICAgICAgICAgICAgICAgICBjb25zdCBuZXdVbmlxdWVLZXkgPSBgJHthZGRyZXNzU3BlY2lmaWNFbnRyeS5jdXN0b21lQ29kZX0tJHthZGRyZXNzU3BlY2lmaWNFbnRyeS5hbGlhc1NoaXBwaW5nTmFtZXMgfHwgYWRkcmVzc1NwZWNpZmljRW50cnkubGVnYWxOYW1lfS0ke2FkZHJlc3NTcGVjaWZpY0VudHJ5LnNoaXBwaW5nQmlsbGluZ0FkZHJlc3N9YDtcclxuICAgICAgICAgICAgICAgICAgaWYgKGN1cnJlbnRBbGlhcyAhPT0gbmV3VW5pcXVlS2V5KSB7XHJcbiAgICAgICAgICAgICAgICAgICAgZm9ybS5zZXRWYWx1ZShgZW50cmllcy4ke2VudHJ5SW5kZXh9LiR7ZmllbGRQcmVmaXh9QWxpYXNgLCBuZXdVbmlxdWVLZXkpO1xyXG4gICAgICAgICAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgICAgICAgICBpZiAoY3VycmVudFppcGNvZGUgIT09IGFkZHJlc3NTcGVjaWZpY0VudHJ5LnppcFBvc3RhbCkge1xyXG4gICAgICAgICAgICAgICAgICAgIGZvcm0uc2V0VmFsdWUoYGVudHJpZXMuJHtlbnRyeUluZGV4fS4ke2ZpZWxkUHJlZml4fVppcGNvZGVgLCBhZGRyZXNzU3BlY2lmaWNFbnRyeS56aXBQb3N0YWwpO1xyXG4gICAgICAgICAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgICAgICAgICBjb25zdCBjdXJyZW50Q29tcGFueSA9IGZvcm0uZ2V0VmFsdWVzKGBlbnRyaWVzLiR7ZW50cnlJbmRleH0uY29tcGFueWApO1xyXG4gICAgICAgICAgICAgICAgICBpZiAoY3VycmVudENvbXBhbnkgIT09IGFkZHJlc3NTcGVjaWZpY0VudHJ5LmJ1c2luZXNzVW5pdCkge1xyXG4gICAgICAgICAgICAgICAgICAgIGZvcm0uc2V0VmFsdWUoYGVudHJpZXMuJHtlbnRyeUluZGV4fS5jb21wYW55YCwgYWRkcmVzc1NwZWNpZmljRW50cnkuYnVzaW5lc3NVbml0KTtcclxuICAgICAgICAgICAgICAgICAgfVxyXG5cclxuICAgICAgICAgICAgICAgICAgY29uc3QgY3VycmVudERpdmlzaW9uID0gZm9ybS5nZXRWYWx1ZXMoYGVudHJpZXMuJHtlbnRyeUluZGV4fS5kaXZpc2lvbmApO1xyXG4gICAgICAgICAgICAgICAgICBpZiAoY3VycmVudERpdmlzaW9uICE9PSBhZGRyZXNzU3BlY2lmaWNFbnRyeS5jdXN0b21lQ29kZSkge1xyXG4gICAgICAgICAgICAgICAgICAgIGZvcm0uc2V0VmFsdWUoYGVudHJpZXMuJHtlbnRyeUluZGV4fS5kaXZpc2lvbmAsIGFkZHJlc3NTcGVjaWZpY0VudHJ5LmN1c3RvbWVDb2RlKTtcclxuICAgICAgICAgICAgICAgICAgfVxyXG5cclxuICAgICAgICAgICAgICAgICAgaWYgKG9uTGVncmFuZERhdGFDaGFuZ2UpIHtcclxuICAgICAgICAgICAgICAgICAgICBvbkxlZ3JhbmREYXRhQ2hhbmdlKGVudHJ5SW5kZXgsIGFkZHJlc3NTcGVjaWZpY0VudHJ5LmJ1c2luZXNzVW5pdCB8fCAnJywgYWRkcmVzc1NwZWNpZmljRW50cnkuY3VzdG9tZUNvZGUgfHwgJycpO1xyXG4gICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgIHJldHVybjsgXHJcbiAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgICAgICAgIGZvcm0uc2V0VmFsdWUoYGVudHJpZXMuJHtlbnRyeUluZGV4fS4ke2ZpZWxkUHJlZml4fUFsaWFzYCwgJycpO1xyXG4gICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgfVxyXG4gICAgICAgICAgfVxyXG5cclxuICAgICAgICAgIGNvbnN0IHVuaXF1ZVppcGNvZGVzID0gQXJyYXkuZnJvbShuZXcgU2V0KGR1cGxpY2F0ZUVudHJpZXMubWFwKGVudHJ5ID0+IGVudHJ5LnppcFBvc3RhbCkuZmlsdGVyKHppcCA9PiB6aXApKSk7XHJcbiAgICAgICAgICBpZiAodW5pcXVlWmlwY29kZXMubGVuZ3RoID09PSAxKSB7XHJcbiAgICAgICAgICAgIGlmIChjdXJyZW50WmlwY29kZSAhPT0gdW5pcXVlWmlwY29kZXNbMF0pIHtcclxuICAgICAgICAgICAgICBmb3JtLnNldFZhbHVlKGBlbnRyaWVzLiR7ZW50cnlJbmRleH0uJHtmaWVsZFByZWZpeH1aaXBjb2RlYCwgdW5pcXVlWmlwY29kZXNbMF0pO1xyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgICBpZiAoY3VycmVudFppcGNvZGUgIT09ICcnKSB7XHJcbiAgICAgICAgICAgICAgZm9ybS5zZXRWYWx1ZShgZW50cmllcy4ke2VudHJ5SW5kZXh9LiR7ZmllbGRQcmVmaXh9WmlwY29kZWAsICcnKTtcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgICAgfVxyXG4gICAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgICBjb25zdCBzZWxlY3RlZERhdGEgPSBkdXBsaWNhdGVFbnRyaWVzWzBdO1xyXG4gICAgICAgICAgaWYgKHNlbGVjdGVkRGF0YSkge1xyXG4gICAgICAgICAgICBjb25zdCB1bmlxdWVLZXkgPSBgJHtzZWxlY3RlZERhdGEuY3VzdG9tZUNvZGV9LSR7c2VsZWN0ZWREYXRhLmFsaWFzU2hpcHBpbmdOYW1lcyB8fCBzZWxlY3RlZERhdGEubGVnYWxOYW1lfS0ke3NlbGVjdGVkRGF0YS5zaGlwcGluZ0JpbGxpbmdBZGRyZXNzfWA7XHJcblxyXG4gICAgICAgICAgICBpZiAoY3VycmVudEFsaWFzICE9PSB1bmlxdWVLZXkpIHtcclxuICAgICAgICAgICAgICBmb3JtLnNldFZhbHVlKGBlbnRyaWVzLiR7ZW50cnlJbmRleH0uJHtmaWVsZFByZWZpeH1BbGlhc2AsIHVuaXF1ZUtleSk7XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgaWYgKGN1cnJlbnRaaXBjb2RlICE9PSBzZWxlY3RlZERhdGEuemlwUG9zdGFsKSB7XHJcbiAgICAgICAgICAgICAgZm9ybS5zZXRWYWx1ZShgZW50cmllcy4ke2VudHJ5SW5kZXh9LiR7ZmllbGRQcmVmaXh9WmlwY29kZWAsIHNlbGVjdGVkRGF0YS56aXBQb3N0YWwpO1xyXG4gICAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgICBjb25zdCBjdXJyZW50Q29tcGFueSA9IGZvcm0uZ2V0VmFsdWVzKGBlbnRyaWVzLiR7ZW50cnlJbmRleH0uY29tcGFueWApO1xyXG4gICAgICAgICAgICBpZiAoY3VycmVudENvbXBhbnkgIT09IHNlbGVjdGVkRGF0YS5idXNpbmVzc1VuaXQpIHtcclxuICAgICAgICAgICAgICBmb3JtLnNldFZhbHVlKGBlbnRyaWVzLiR7ZW50cnlJbmRleH0uY29tcGFueWAsIHNlbGVjdGVkRGF0YS5idXNpbmVzc1VuaXQpO1xyXG4gICAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgICBjb25zdCBjdXJyZW50RGl2aXNpb24gPSBmb3JtLmdldFZhbHVlcyhgZW50cmllcy4ke2VudHJ5SW5kZXh9LmRpdmlzaW9uYCk7XHJcbiAgICAgICAgICAgIGlmIChjdXJyZW50RGl2aXNpb24gIT09IHNlbGVjdGVkRGF0YS5jdXN0b21lQ29kZSkge1xyXG4gICAgICAgICAgICAgIGZvcm0uc2V0VmFsdWUoYGVudHJpZXMuJHtlbnRyeUluZGV4fS5kaXZpc2lvbmAsIHNlbGVjdGVkRGF0YS5jdXN0b21lQ29kZSk7XHJcbiAgICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAgIGlmIChvbkxlZ3JhbmREYXRhQ2hhbmdlKSB7XHJcbiAgICAgICAgICAgICAgb25MZWdyYW5kRGF0YUNoYW5nZShlbnRyeUluZGV4LCBzZWxlY3RlZERhdGEuYnVzaW5lc3NVbml0IHx8ICcnLCBzZWxlY3RlZERhdGEuY3VzdG9tZUNvZGUgfHwgJycpO1xyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgICB9XHJcbiAgICAgICAgfVxyXG4gICAgICB9XHJcblxyXG4gICAgICBpZiAobmFtZSA9PT0gYGVudHJpZXMuJHtlbnRyeUluZGV4fS4ke2ZpZWxkUHJlZml4fVppcGNvZGVgICYmIGN1cnJlbnRaaXBjb2RlKSB7XHJcbiAgICAgICAgY29uc3QgZHVwbGljYXRlRW50cmllcyA9IGNoZWNrRm9yRHVwbGljYXRlcygnemlwY29kZScsIGN1cnJlbnRaaXBjb2RlKTtcclxuXHJcbiAgICAgICAgaWYgKGR1cGxpY2F0ZUVudHJpZXMubGVuZ3RoID4gMSkge1xyXG4gICAgICAgICAgaWYgKGN1cnJlbnRBbGlhcyAhPT0gJycpIHtcclxuICAgICAgICAgICAgZm9ybS5zZXRWYWx1ZShgZW50cmllcy4ke2VudHJ5SW5kZXh9LiR7ZmllbGRQcmVmaXh9QWxpYXNgLCAnJyk7XHJcbiAgICAgICAgICB9XHJcbiAgICAgICAgICBpZiAoY3VycmVudEFkZHJlc3MgIT09ICcnKSB7XHJcbiAgICAgICAgICAgIGZvcm0uc2V0VmFsdWUoYGVudHJpZXMuJHtlbnRyeUluZGV4fS4ke2ZpZWxkUHJlZml4fUFkZHJlc3NgLCAnJyk7XHJcbiAgICAgICAgICB9XHJcbiAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgIGNvbnN0IHNlbGVjdGVkRGF0YSA9IGR1cGxpY2F0ZUVudHJpZXNbMF07XHJcbiAgICAgICAgICBpZiAoc2VsZWN0ZWREYXRhKSB7XHJcbiAgICAgICAgICAgIGNvbnN0IHVuaXF1ZUtleSA9IGAke3NlbGVjdGVkRGF0YS5jdXN0b21lQ29kZX0tJHtzZWxlY3RlZERhdGEuYWxpYXNTaGlwcGluZ05hbWVzIHx8IHNlbGVjdGVkRGF0YS5sZWdhbE5hbWV9LSR7c2VsZWN0ZWREYXRhLnNoaXBwaW5nQmlsbGluZ0FkZHJlc3N9YDtcclxuXHJcbiAgICAgICAgICAgIGlmIChjdXJyZW50QWxpYXMgIT09IHVuaXF1ZUtleSkge1xyXG4gICAgICAgICAgICAgIGZvcm0uc2V0VmFsdWUoYGVudHJpZXMuJHtlbnRyeUluZGV4fS4ke2ZpZWxkUHJlZml4fUFsaWFzYCwgdW5pcXVlS2V5KTtcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICBpZiAoY3VycmVudEFkZHJlc3MgIT09IHNlbGVjdGVkRGF0YS5zaGlwcGluZ0JpbGxpbmdBZGRyZXNzKSB7XHJcbiAgICAgICAgICAgICAgZm9ybS5zZXRWYWx1ZShgZW50cmllcy4ke2VudHJ5SW5kZXh9LiR7ZmllbGRQcmVmaXh9QWRkcmVzc2AsIHNlbGVjdGVkRGF0YS5zaGlwcGluZ0JpbGxpbmdBZGRyZXNzKTtcclxuICAgICAgICAgICAgfVxyXG5cclxuICAgICAgICAgICAgY29uc3QgY3VycmVudENvbXBhbnkgPSBmb3JtLmdldFZhbHVlcyhgZW50cmllcy4ke2VudHJ5SW5kZXh9LmNvbXBhbnlgKTtcclxuICAgICAgICAgICAgaWYgKGN1cnJlbnRDb21wYW55ICE9PSBzZWxlY3RlZERhdGEuYnVzaW5lc3NVbml0KSB7XHJcbiAgICAgICAgICAgICAgZm9ybS5zZXRWYWx1ZShgZW50cmllcy4ke2VudHJ5SW5kZXh9LmNvbXBhbnlgLCBzZWxlY3RlZERhdGEuYnVzaW5lc3NVbml0KTtcclxuICAgICAgICAgICAgfVxyXG5cclxuICAgICAgICAgICAgY29uc3QgY3VycmVudERpdmlzaW9uID0gZm9ybS5nZXRWYWx1ZXMoYGVudHJpZXMuJHtlbnRyeUluZGV4fS5kaXZpc2lvbmApO1xyXG4gICAgICAgICAgICBpZiAoY3VycmVudERpdmlzaW9uICE9PSBzZWxlY3RlZERhdGEuY3VzdG9tZUNvZGUpIHtcclxuICAgICAgICAgICAgICBmb3JtLnNldFZhbHVlKGBlbnRyaWVzLiR7ZW50cnlJbmRleH0uZGl2aXNpb25gLCBzZWxlY3RlZERhdGEuY3VzdG9tZUNvZGUpO1xyXG4gICAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgICBpZiAob25MZWdyYW5kRGF0YUNoYW5nZSkge1xyXG4gICAgICAgICAgICAgIG9uTGVncmFuZERhdGFDaGFuZ2UoZW50cnlJbmRleCwgc2VsZWN0ZWREYXRhLmJ1c2luZXNzVW5pdCB8fCAnJywgc2VsZWN0ZWREYXRhLmN1c3RvbWVDb2RlIHx8ICcnKTtcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgICAgfVxyXG4gICAgICAgIH1cclxuICAgICAgfVxyXG4gICAgfSk7XHJcblxyXG4gICAgcmV0dXJuICgpID0+IHN1YnNjcmlwdGlvbi51bnN1YnNjcmliZSgpO1xyXG4gIH0sIFtsZWdyYW5kRGF0YSwgZm9ybSwgZW50cnlJbmRleCwgb25MZWdyYW5kRGF0YUNoYW5nZSwgY2hlY2tGb3JEdXBsaWNhdGVzLCBnZXRCYXNlTmFtZSwgY2xlYXJPdGhlckJsb2NrcywgZmllbGRQcmVmaXhdKTtcclxuXHJcbiAgY29uc3QgZ2V0QWRkcmVzc09wdGlvbnMgPSAoKSA9PiB7XHJcbiAgICBjb25zdCBjdXJyZW50QWxpYXMgPSBmb3JtLmdldFZhbHVlcyhgZW50cmllcy4ke2VudHJ5SW5kZXh9LiR7ZmllbGRQcmVmaXh9QWxpYXNgKTtcclxuICAgIGNvbnN0IGN1cnJlbnRaaXBjb2RlID0gZm9ybS5nZXRWYWx1ZXMoYGVudHJpZXMuJHtlbnRyeUluZGV4fS4ke2ZpZWxkUHJlZml4fVppcGNvZGVgKTtcclxuXHJcbiAgICBpZiAoY3VycmVudEFsaWFzKSB7XHJcbiAgICAgIGNvbnN0IHNlbGVjdGVkQWxpYXNEYXRhID0gbGVncmFuZERhdGEuZmluZChkYXRhID0+IHtcclxuICAgICAgICBjb25zdCB1bmlxdWVLZXkgPSBgJHtkYXRhLmN1c3RvbWVDb2RlfS0ke2RhdGEuYWxpYXNTaGlwcGluZ05hbWVzIHx8IGRhdGEubGVnYWxOYW1lfS0ke2RhdGEuc2hpcHBpbmdCaWxsaW5nQWRkcmVzc31gO1xyXG4gICAgICAgIHJldHVybiB1bmlxdWVLZXkgPT09IGN1cnJlbnRBbGlhcztcclxuICAgICAgfSk7XHJcblxyXG4gICAgICBpZiAoc2VsZWN0ZWRBbGlhc0RhdGEpIHtcclxuICAgICAgICBjb25zdCBzZWxlY3RlZERpc3BsYXlOYW1lID0gZ2V0QWxpYXNPcHRpb25zKCkuZmluZChvcHRpb24gPT4gb3B0aW9uLnZhbHVlID09PSBjdXJyZW50QWxpYXMpPy5kaXNwbGF5TmFtZSB8fCAnJztcclxuXHJcbiAgICAgICAgY29uc3Qgc2FtZURpc3BsYXlOYW1lRW50cmllcyA9IGxlZ3JhbmREYXRhLmZpbHRlcihkYXRhID0+IHtcclxuICAgICAgICAgIGNvbnN0IGxvY2F0aW9uID0gZGF0YS5sb2NhdGlvbiB8fCBleHRyYWN0Q2l0eUZyb21BZGRyZXNzKGRhdGEuc2hpcHBpbmdCaWxsaW5nQWRkcmVzcyB8fCAnJyk7XHJcbiAgICAgICAgICBjb25zdCB6aXBjb2RlID0gZGF0YS56aXBQb3N0YWwgfHwgJyc7XHJcblxyXG4gICAgICAgICAgY29uc3QgbGVnYWxOYW1lID0gZGF0YS5sZWdhbE5hbWUgfHwgJ1Vua25vd24gQ29tcGFueSc7XHJcbiAgICAgICAgICBjb25zdCBhbGlhc05hbWUgPSBkYXRhLmFsaWFzU2hpcHBpbmdOYW1lcyAmJiBkYXRhLmFsaWFzU2hpcHBpbmdOYW1lcyAhPT0gJ05PTkUnID8gZGF0YS5hbGlhc1NoaXBwaW5nTmFtZXMgOiBudWxsO1xyXG5cclxuICAgICAgICAgIGxldCBkaXNwbGF5TmFtZSA9ICcnO1xyXG4gICAgICAgICAgaWYgKGFsaWFzTmFtZSkge1xyXG4gICAgICAgICAgICBkaXNwbGF5TmFtZSA9IGAke2xlZ2FsTmFtZX0gKCR7YWxpYXNOYW1lfSlgO1xyXG4gICAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgICAgZGlzcGxheU5hbWUgPSBsZWdhbE5hbWU7XHJcbiAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgY29uc3QgYmFzZU5hbWUgPSBkaXNwbGF5TmFtZTtcclxuICAgICAgICAgIGNvbnN0IHNhbWVOYW1lRW50cmllcyA9IGxlZ3JhbmREYXRhLmZpbHRlcihkID0+IGdldEJhc2VOYW1lKGQpID09PSBkaXNwbGF5TmFtZSk7XHJcbiAgICAgICAgICBpZiAoc2FtZU5hbWVFbnRyaWVzLmxlbmd0aCA+IDEpIHtcclxuICAgICAgICAgICAgaWYgKGxvY2F0aW9uKSB7XHJcbiAgICAgICAgICAgICAgZGlzcGxheU5hbWUgPSBgJHtkaXNwbGF5TmFtZX1fJHtsb2NhdGlvbn1gO1xyXG4gICAgICAgICAgICB9IGVsc2UgaWYgKGRhdGEuemlwUG9zdGFsKSB7XHJcbiAgICAgICAgICAgICAgZGlzcGxheU5hbWUgPSBgJHtkaXNwbGF5TmFtZX1fJHtkYXRhLnppcFBvc3RhbH1gO1xyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgcmV0dXJuIGRpc3BsYXlOYW1lID09PSBzZWxlY3RlZERpc3BsYXlOYW1lO1xyXG4gICAgICAgIH0pO1xyXG5cclxuICAgICAgICBpZiAoc2FtZURpc3BsYXlOYW1lRW50cmllcy5sZW5ndGggPiAxKSB7XHJcbiAgICAgICAgICByZXR1cm4gc2FtZURpc3BsYXlOYW1lRW50cmllc1xyXG4gICAgICAgICAgICAubWFwKGRhdGEgPT4gZGF0YS5zaGlwcGluZ0JpbGxpbmdBZGRyZXNzKVxyXG4gICAgICAgICAgICAuZmlsdGVyKGFkZHJlc3MgPT4gYWRkcmVzcylcclxuICAgICAgICAgICAgLmZpbHRlcigoYWRkcmVzcywgaW5kZXgsIHNlbGYpID0+IHNlbGYuaW5kZXhPZihhZGRyZXNzKSA9PT0gaW5kZXgpXHJcbiAgICAgICAgICAgIC5zb3J0KCk7XHJcbiAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgIHJldHVybiBzZWxlY3RlZEFsaWFzRGF0YS5zaGlwcGluZ0JpbGxpbmdBZGRyZXNzID8gW3NlbGVjdGVkQWxpYXNEYXRhLnNoaXBwaW5nQmlsbGluZ0FkZHJlc3NdIDogW107XHJcbiAgICAgICAgfVxyXG4gICAgICB9XHJcbiAgICB9XHJcblxyXG4gICAgaWYgKGN1cnJlbnRaaXBjb2RlKSB7XHJcbiAgICAgIGNvbnN0IGR1cGxpY2F0ZUVudHJpZXMgPSBjaGVja0ZvckR1cGxpY2F0ZXMoJ3ppcGNvZGUnLCBjdXJyZW50WmlwY29kZSk7XHJcblxyXG4gICAgICBpZiAoZHVwbGljYXRlRW50cmllcy5sZW5ndGggPiAxKSB7XHJcbiAgICAgICAgcmV0dXJuIGR1cGxpY2F0ZUVudHJpZXNcclxuICAgICAgICAgIC5tYXAoZGF0YSA9PiBkYXRhLnNoaXBwaW5nQmlsbGluZ0FkZHJlc3MpXHJcbiAgICAgICAgICAuZmlsdGVyKGFkZHJlc3MgPT4gYWRkcmVzcylcclxuICAgICAgICAgIC5maWx0ZXIoKGFkZHJlc3MsIGluZGV4LCBzZWxmKSA9PiBzZWxmLmluZGV4T2YoYWRkcmVzcykgPT09IGluZGV4KSBcclxuICAgICAgICAgIC5zb3J0KCk7XHJcbiAgICAgIH1cclxuICAgIH1cclxuXHJcbiAgICBjb25zdCB1bmlxdWVBZGRyZXNzZXMgPSBuZXcgU2V0PHN0cmluZz4oKTtcclxuICAgIGxlZ3JhbmREYXRhLmZvckVhY2goZGF0YSA9PiB7XHJcbiAgICAgIGlmIChkYXRhLnNoaXBwaW5nQmlsbGluZ0FkZHJlc3MpIHtcclxuICAgICAgICB1bmlxdWVBZGRyZXNzZXMuYWRkKGRhdGEuc2hpcHBpbmdCaWxsaW5nQWRkcmVzcyk7XHJcbiAgICAgIH1cclxuICAgIH0pO1xyXG4gICAgcmV0dXJuIEFycmF5LmZyb20odW5pcXVlQWRkcmVzc2VzKS5zb3J0KCk7XHJcbiAgfTtcclxuXHJcbiAgY29uc3QgZ2V0WmlwY29kZU9wdGlvbnMgPSAoKSA9PiB7XHJcbiAgICBjb25zdCBjdXJyZW50QWxpYXMgPSBmb3JtLmdldFZhbHVlcyhgZW50cmllcy4ke2VudHJ5SW5kZXh9LiR7ZmllbGRQcmVmaXh9QWxpYXNgKTtcclxuICAgIGNvbnN0IGN1cnJlbnRBZGRyZXNzID0gZm9ybS5nZXRWYWx1ZXMoYGVudHJpZXMuJHtlbnRyeUluZGV4fS4ke2ZpZWxkUHJlZml4fUFkZHJlc3NgKTtcclxuXHJcbiAgICBpZiAoY3VycmVudEFsaWFzKSB7XHJcbiAgICAgIGNvbnN0IHNlbGVjdGVkQWxpYXNEYXRhID0gbGVncmFuZERhdGEuZmluZChkYXRhID0+IHtcclxuICAgICAgICBjb25zdCB1bmlxdWVLZXkgPSBgJHtkYXRhLmN1c3RvbWVDb2RlfS0ke2RhdGEuYWxpYXNTaGlwcGluZ05hbWVzIHx8IGRhdGEubGVnYWxOYW1lfS0ke2RhdGEuc2hpcHBpbmdCaWxsaW5nQWRkcmVzc31gO1xyXG4gICAgICAgIHJldHVybiB1bmlxdWVLZXkgPT09IGN1cnJlbnRBbGlhcztcclxuICAgICAgfSk7XHJcblxyXG4gICAgICBpZiAoc2VsZWN0ZWRBbGlhc0RhdGEpIHtcclxuICAgICAgICBjb25zdCBzZWxlY3RlZERpc3BsYXlOYW1lID0gZ2V0QWxpYXNPcHRpb25zKCkuZmluZChvcHRpb24gPT4gb3B0aW9uLnZhbHVlID09PSBjdXJyZW50QWxpYXMpPy5kaXNwbGF5TmFtZSB8fCAnJztcclxuXHJcbiAgICAgICAgY29uc3Qgc2FtZURpc3BsYXlOYW1lRW50cmllcyA9IGxlZ3JhbmREYXRhLmZpbHRlcihkYXRhID0+IHtcclxuICAgICAgICAgIGNvbnN0IGxvY2F0aW9uID0gZGF0YS5sb2NhdGlvbiB8fCBleHRyYWN0Q2l0eUZyb21BZGRyZXNzKGRhdGEuc2hpcHBpbmdCaWxsaW5nQWRkcmVzcyB8fCAnJyk7XHJcbiAgICAgICAgICBjb25zdCB6aXBjb2RlID0gZGF0YS56aXBQb3N0YWwgfHwgJyc7XHJcblxyXG4gICAgICAgICAgY29uc3QgbGVnYWxOYW1lID0gZGF0YS5sZWdhbE5hbWUgfHwgJ1Vua25vd24gQ29tcGFueSc7XHJcbiAgICAgICAgICBjb25zdCBhbGlhc05hbWUgPSBkYXRhLmFsaWFzU2hpcHBpbmdOYW1lcyAmJiBkYXRhLmFsaWFzU2hpcHBpbmdOYW1lcyAhPT0gJ05PTkUnID8gZGF0YS5hbGlhc1NoaXBwaW5nTmFtZXMgOiBudWxsO1xyXG5cclxuICAgICAgICAgIGxldCBkaXNwbGF5TmFtZSA9ICcnO1xyXG4gICAgICAgICAgaWYgKGFsaWFzTmFtZSkge1xyXG4gICAgICAgICAgICBkaXNwbGF5TmFtZSA9IGAke2xlZ2FsTmFtZX0gKCR7YWxpYXNOYW1lfSlgO1xyXG4gICAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgICAgZGlzcGxheU5hbWUgPSBsZWdhbE5hbWU7XHJcbiAgICAgICAgICB9XHJcbiAgICAgICAgICBjb25zdCBzYW1lTmFtZUVudHJpZXMgPSBsZWdyYW5kRGF0YS5maWx0ZXIoZCA9PiBnZXRCYXNlTmFtZShkKSA9PT0gZGlzcGxheU5hbWUpO1xyXG4gICAgICAgICAgaWYgKHNhbWVOYW1lRW50cmllcy5sZW5ndGggPiAxKSB7XHJcbiAgICAgICAgICAgIGlmIChsb2NhdGlvbikge1xyXG4gICAgICAgICAgICAgIGRpc3BsYXlOYW1lID0gYCR7ZGlzcGxheU5hbWV9XyR7bG9jYXRpb259YDtcclxuICAgICAgICAgICAgfSBlbHNlIGlmIChkYXRhLnppcFBvc3RhbCkge1xyXG4gICAgICAgICAgICAgIGRpc3BsYXlOYW1lID0gYCR7ZGlzcGxheU5hbWV9XyR7ZGF0YS56aXBQb3N0YWx9YDtcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgICAgfVxyXG5cclxuICAgICAgICAgIHJldHVybiBkaXNwbGF5TmFtZSA9PT0gc2VsZWN0ZWREaXNwbGF5TmFtZTtcclxuICAgICAgICB9KTtcclxuXHJcbiAgICAgICAgaWYgKHNhbWVEaXNwbGF5TmFtZUVudHJpZXMubGVuZ3RoID4gMSkge1xyXG4gICAgICAgICAgcmV0dXJuIHNhbWVEaXNwbGF5TmFtZUVudHJpZXNcclxuICAgICAgICAgICAgLm1hcChkYXRhID0+IGRhdGEuemlwUG9zdGFsKVxyXG4gICAgICAgICAgICAuZmlsdGVyKHppcGNvZGUgPT4gemlwY29kZSlcclxuICAgICAgICAgICAgLmZpbHRlcigoemlwY29kZSwgaW5kZXgsIHNlbGYpID0+IHNlbGYuaW5kZXhPZih6aXBjb2RlKSA9PT0gaW5kZXgpIFxyXG4gICAgICAgICAgICAuc29ydCgpO1xyXG4gICAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgICByZXR1cm4gc2VsZWN0ZWRBbGlhc0RhdGEuemlwUG9zdGFsID8gW3NlbGVjdGVkQWxpYXNEYXRhLnppcFBvc3RhbF0gOiBbXTtcclxuICAgICAgICB9XHJcbiAgICAgIH1cclxuICAgIH1cclxuXHJcbiAgICBpZiAoY3VycmVudEFkZHJlc3MpIHtcclxuICAgICAgY29uc3QgZHVwbGljYXRlRW50cmllcyA9IGNoZWNrRm9yRHVwbGljYXRlcygnYWRkcmVzcycsIGN1cnJlbnRBZGRyZXNzKTtcclxuXHJcbiAgICAgIGlmIChkdXBsaWNhdGVFbnRyaWVzLmxlbmd0aCA+IDEpIHtcclxuICAgICAgICByZXR1cm4gZHVwbGljYXRlRW50cmllc1xyXG4gICAgICAgICAgLm1hcChkYXRhID0+IGRhdGEuemlwUG9zdGFsKVxyXG4gICAgICAgICAgLmZpbHRlcih6aXBjb2RlID0+IHppcGNvZGUpXHJcbiAgICAgICAgICAuZmlsdGVyKCh6aXBjb2RlLCBpbmRleCwgc2VsZikgPT4gc2VsZi5pbmRleE9mKHppcGNvZGUpID09PSBpbmRleCkgXHJcbiAgICAgICAgICAuc29ydCgpO1xyXG4gICAgICB9XHJcbiAgICB9XHJcblxyXG4gICAgY29uc3QgdW5pcXVlWmlwY29kZXMgPSBuZXcgU2V0PHN0cmluZz4oKTtcclxuICAgIGxlZ3JhbmREYXRhLmZvckVhY2goZGF0YSA9PiB7XHJcbiAgICAgIGlmIChkYXRhLnppcFBvc3RhbCkge1xyXG4gICAgICAgIHVuaXF1ZVppcGNvZGVzLmFkZChkYXRhLnppcFBvc3RhbCk7XHJcbiAgICAgIH1cclxuICAgIH0pO1xyXG4gICAgcmV0dXJuIEFycmF5LmZyb20odW5pcXVlWmlwY29kZXMpLnNvcnQoKTtcclxuICB9O1xyXG5cclxuICBjb25zdCBnZXRDdXJyZW50QWxpYXNPcHRpb25zID0gKCkgPT4ge1xyXG4gICAgY29uc3QgY3VycmVudEFkZHJlc3MgPSBmb3JtLmdldFZhbHVlcyhgZW50cmllcy4ke2VudHJ5SW5kZXh9LiR7ZmllbGRQcmVmaXh9QWRkcmVzc2ApO1xyXG4gICAgY29uc3QgY3VycmVudFppcGNvZGUgPSBmb3JtLmdldFZhbHVlcyhgZW50cmllcy4ke2VudHJ5SW5kZXh9LiR7ZmllbGRQcmVmaXh9WmlwY29kZWApO1xyXG5cclxuICAgIGlmIChjdXJyZW50QWRkcmVzcykge1xyXG4gICAgICBjb25zdCBkdXBsaWNhdGVFbnRyaWVzID0gY2hlY2tGb3JEdXBsaWNhdGVzKCdhZGRyZXNzJywgY3VycmVudEFkZHJlc3MpO1xyXG4gICAgICBpZiAoZHVwbGljYXRlRW50cmllcy5sZW5ndGggPiAxKSB7XHJcbiAgICAgICAgcmV0dXJuIGdldEZpbHRlcmVkQWxpYXNPcHRpb25zKCk7XHJcbiAgICAgIH1cclxuICAgIH1cclxuXHJcbiAgICBpZiAoY3VycmVudFppcGNvZGUpIHtcclxuICAgICAgY29uc3QgZHVwbGljYXRlRW50cmllcyA9IGNoZWNrRm9yRHVwbGljYXRlcygnemlwY29kZScsIGN1cnJlbnRaaXBjb2RlKTtcclxuICAgICAgaWYgKGR1cGxpY2F0ZUVudHJpZXMubGVuZ3RoID4gMSkge1xyXG4gICAgICAgIHJldHVybiBnZXRGaWx0ZXJlZEFsaWFzT3B0aW9ucygpO1xyXG4gICAgICB9XHJcbiAgICB9XHJcblxyXG4gICAgcmV0dXJuIGdldEFsaWFzT3B0aW9ucygpO1xyXG4gIH07XHJcblxyXG4gIGNvbnN0IGdldEZpbHRlcmVkQWxpYXNPcHRpb25zID0gKCkgPT4ge1xyXG4gICAgY29uc3QgY3VycmVudEFkZHJlc3MgPSBmb3JtLmdldFZhbHVlcyhgZW50cmllcy4ke2VudHJ5SW5kZXh9LiR7ZmllbGRQcmVmaXh9QWRkcmVzc2ApO1xyXG4gICAgY29uc3QgY3VycmVudFppcGNvZGUgPSBmb3JtLmdldFZhbHVlcyhgZW50cmllcy4ke2VudHJ5SW5kZXh9LiR7ZmllbGRQcmVmaXh9WmlwY29kZWApO1xyXG5cclxuICAgIGxldCBmaWx0ZXJlZERhdGEgPSBsZWdyYW5kRGF0YTtcclxuXHJcbiAgICBpZiAoY3VycmVudEFkZHJlc3MpIHtcclxuICAgICAgY29uc3QgZHVwbGljYXRlRW50cmllcyA9IGNoZWNrRm9yRHVwbGljYXRlcygnYWRkcmVzcycsIGN1cnJlbnRBZGRyZXNzKTtcclxuICAgICAgaWYgKGR1cGxpY2F0ZUVudHJpZXMubGVuZ3RoID4gMSkge1xyXG4gICAgICAgIGZpbHRlcmVkRGF0YSA9IGR1cGxpY2F0ZUVudHJpZXM7XHJcbiAgICAgIH1cclxuICAgIH1cclxuXHJcbiAgICBpZiAoY3VycmVudFppcGNvZGUpIHtcclxuICAgICAgY29uc3QgZHVwbGljYXRlRW50cmllcyA9IGNoZWNrRm9yRHVwbGljYXRlcygnemlwY29kZScsIGN1cnJlbnRaaXBjb2RlKTtcclxuICAgICAgaWYgKGR1cGxpY2F0ZUVudHJpZXMubGVuZ3RoID4gMSkge1xyXG4gICAgICAgIGZpbHRlcmVkRGF0YSA9IGZpbHRlcmVkRGF0YS5maWx0ZXIoZGF0YSA9PlxyXG4gICAgICAgICAgZHVwbGljYXRlRW50cmllcy5zb21lKGR1cCA9PiBkdXAuaWQgPT09IGRhdGEuaWQpXHJcbiAgICAgICAgKTtcclxuICAgICAgfVxyXG4gICAgfVxyXG5cclxuICAgIGNvbnN0IG9wdGlvbnM6IEFycmF5PHtcclxuICAgICAgdmFsdWU6IHN0cmluZztcclxuICAgICAgbGFiZWw6IHN0cmluZztcclxuICAgICAgdHlwZTogJ2FsaWFzJyB8ICdjb21wYW55JztcclxuICAgICAgZGlzcGxheU5hbWU6IHN0cmluZztcclxuICAgIH0+ID0gW107XHJcblxyXG4gICAgZmlsdGVyZWREYXRhLmZvckVhY2goZGF0YSA9PiB7XHJcbiAgICAgIGNvbnN0IGxvY2F0aW9uID0gZGF0YS5sb2NhdGlvbiB8fCBleHRyYWN0Q2l0eUZyb21BZGRyZXNzKGRhdGEuc2hpcHBpbmdCaWxsaW5nQWRkcmVzcyB8fCAnJyk7XHJcbiAgICAgIGNvbnN0IHppcGNvZGUgPSBkYXRhLnppcFBvc3RhbCB8fCAnJztcclxuXHJcbiAgICAgIGxldCBkaXNwbGF5TmFtZSA9ICcnO1xyXG4gICAgICBsZXQgdHlwZTogJ2FsaWFzJyB8ICdjb21wYW55JyA9ICdjb21wYW55JztcclxuXHJcbiAgICAgIGNvbnN0IGxlZ2FsTmFtZSA9IGRhdGEubGVnYWxOYW1lIHx8ICdVbmtub3duIENvbXBhbnknO1xyXG4gICAgICBjb25zdCBhbGlhc05hbWUgPSBkYXRhLmFsaWFzU2hpcHBpbmdOYW1lcyAmJiBkYXRhLmFsaWFzU2hpcHBpbmdOYW1lcyAhPT0gJ05PTkUnID8gZGF0YS5hbGlhc1NoaXBwaW5nTmFtZXMgOiBudWxsO1xyXG5cclxuICAgICAgaWYgKGFsaWFzTmFtZSkge1xyXG4gICAgICAgIGRpc3BsYXlOYW1lID0gYCR7bGVnYWxOYW1lfSAoJHthbGlhc05hbWV9KWA7XHJcbiAgICAgICAgdHlwZSA9ICdhbGlhcyc7XHJcbiAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgZGlzcGxheU5hbWUgPSBsZWdhbE5hbWU7XHJcbiAgICAgICAgdHlwZSA9ICdjb21wYW55JztcclxuICAgICAgfVxyXG5cclxuICAgICAgY29uc3QgdW5pcXVlS2V5ID0gYCR7ZGF0YS5jdXN0b21lQ29kZX0tJHtkYXRhLmFsaWFzU2hpcHBpbmdOYW1lcyB8fCBkYXRhLmxlZ2FsTmFtZX0tJHtkYXRhLnNoaXBwaW5nQmlsbGluZ0FkZHJlc3N9YDtcclxuXHJcbiAgICAgIGNvbnN0IHNhbWVOYW1lRW50cmllcyA9IGZpbHRlcmVkRGF0YS5maWx0ZXIoZCA9PiB7XHJcbiAgICAgICAgY29uc3QgZExlZ2FsTmFtZSA9IGQubGVnYWxOYW1lIHx8ICdVbmtub3duIENvbXBhbnknO1xyXG4gICAgICAgIGNvbnN0IGRBbGlhc05hbWUgPSBkLmFsaWFzU2hpcHBpbmdOYW1lcyAmJiBkLmFsaWFzU2hpcHBpbmdOYW1lcyAhPT0gJ05PTkUnID8gZC5hbGlhc1NoaXBwaW5nTmFtZXMgOiBudWxsO1xyXG5cclxuICAgICAgICBsZXQgZERpc3BsYXlOYW1lID0gJyc7XHJcbiAgICAgICAgaWYgKGRBbGlhc05hbWUpIHtcclxuICAgICAgICAgIGREaXNwbGF5TmFtZSA9IGAke2RMZWdhbE5hbWV9ICgke2RBbGlhc05hbWV9KWA7XHJcbiAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgIGREaXNwbGF5TmFtZSA9IGRMZWdhbE5hbWU7XHJcbiAgICAgICAgfVxyXG5cclxuICAgICAgICByZXR1cm4gZERpc3BsYXlOYW1lID09PSBkaXNwbGF5TmFtZTtcclxuICAgICAgfSk7XHJcblxyXG4gICAgICBsZXQgZmluYWxEaXNwbGF5TmFtZSA9IGRpc3BsYXlOYW1lO1xyXG4gICAgICBpZiAoc2FtZU5hbWVFbnRyaWVzLmxlbmd0aCA+IDEpIHtcclxuICAgICAgICBpZiAobG9jYXRpb24pIHtcclxuICAgICAgICAgIGZpbmFsRGlzcGxheU5hbWUgPSBgJHtkaXNwbGF5TmFtZX1fJHtsb2NhdGlvbn1gO1xyXG4gICAgICAgIH0gZWxzZSBpZiAoemlwY29kZSkge1xyXG4gICAgICAgICAgZmluYWxEaXNwbGF5TmFtZSA9IGAke2Rpc3BsYXlOYW1lfV8ke3ppcGNvZGV9YDtcclxuICAgICAgICB9XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIG9wdGlvbnMucHVzaCh7XHJcbiAgICAgICAgdmFsdWU6IHVuaXF1ZUtleSxcclxuICAgICAgICBsYWJlbDogZmluYWxEaXNwbGF5TmFtZSxcclxuICAgICAgICB0eXBlOiB0eXBlLFxyXG4gICAgICAgIGRpc3BsYXlOYW1lOiBmaW5hbERpc3BsYXlOYW1lXHJcbiAgICAgIH0pO1xyXG4gICAgfSk7XHJcblxyXG4gICAgY29uc3QgdW5pcXVlT3B0aW9ucyA9IG9wdGlvbnMuZmlsdGVyKChvcHRpb24sIGluZGV4LCBzZWxmKSA9PlxyXG4gICAgICBpbmRleCA9PT0gc2VsZi5maW5kSW5kZXgobyA9PiBvLnZhbHVlID09PSBvcHRpb24udmFsdWUpXHJcbiAgICApO1xyXG5cclxuICAgIHJldHVybiB1bmlxdWVPcHRpb25zLnNvcnQoKGEsIGIpID0+IGEubGFiZWwubG9jYWxlQ29tcGFyZShiLmxhYmVsKSk7XHJcbiAgfTtcclxuICBcclxuICBpZiAobG9hZGluZykge1xyXG4gICAgcmV0dXJuIChcclxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1ncmF5LTUwIHJvdW5kZWQtbGcgcC0zIG1heC13LXNtIG1iLTRcIj5cclxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMiBtYi0yXCI+XHJcbiAgICAgICAgICA8TWFwUGluIGNsYXNzTmFtZT1cInctNCBoLTQgdGV4dC1wdXJwbGUtNjAwXCIgLz5cclxuICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtc2VtaWJvbGQgdGV4dC1ncmF5LTkwMFwiPkxFR1JBTkQgRGV0YWlsczwvaDM+XHJcbiAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS01MDBcIj5Mb2FkaW5nIExFR1JBTkQgZGF0YS4uLjwvZGl2PlxyXG4gICAgICA8L2Rpdj5cclxuICAgICk7XHJcbiAgfVxyXG5cclxuICByZXR1cm4gKFxyXG4gICAgPGRpdiBjbGFzc05hbWU9XCJiZy1ncmF5LTUwIHJvdW5kZWQtbGcgcC0zIG1iLTMgbWF4LXctbGdcIj5cclxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTIgbWItMlwiPlxyXG4gICAgICAgIDxNYXBQaW4gY2xhc3NOYW1lPVwidy00IGgtNCB0ZXh0LXB1cnBsZS02MDBcIiAvPlxyXG4gICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtc2VtaWJvbGQgdGV4dC1ncmF5LTkwMFwiPntibG9ja1RpdGxlfTwvaDM+XHJcbiAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgey8qIFZlcnRpY2FsIGxheW91dCB3aXRoIGFsbCB0aHJlZSBmaWVsZHMgLSB3aWRlciBmb3IgYmV0dGVyIHNwYWNpbmcgKi99XHJcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMSBnYXAtM1wiPlxyXG4gICAgICAgIHsvKiBBbGlhcy9Db21wYW55ICovfVxyXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiWyZfZGl2W2NsYXNzKj0nZmxleCddW2NsYXNzKj0naXRlbXMtY2VudGVyJ11dOiFoLTEwIFsmX2lucHV0XTohdGV4dC1zbSBbJl9sYWJlbF06IXRleHQtc21cIj5cclxuICAgICAgICAgIDxTZWFyY2hTZWxlY3RcclxuICAgICAgICAgICAgZm9ybT17Zm9ybX1cclxuICAgICAgICAgICAgbmFtZT17YGVudHJpZXMuJHtlbnRyeUluZGV4fS4ke2ZpZWxkUHJlZml4fUFsaWFzYH1cclxuICAgICAgICAgICAgbGFiZWw9XCJBbGlhcy9Db21wYW55XCJcclxuICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJTZWxlY3QgQWxpYXMgb3IgQ29tcGFueVwiXHJcbiAgICAgICAgICAgIGlzRW50cnlQYWdlPXt0cnVlfVxyXG4gICAgICAgICAgICBjbGFzc05hbWU9XCJ0ZXh0LXNtXCJcclxuICAgICAgICAgICAgb3B0aW9ucz17Z2V0Q3VycmVudEFsaWFzT3B0aW9ucygpLm1hcCgob3B0aW9uKSA9PiAoe1xyXG4gICAgICAgICAgICAgIHZhbHVlOiBvcHRpb24udmFsdWUsXHJcbiAgICAgICAgICAgICAgbGFiZWw6IG9wdGlvbi5kaXNwbGF5TmFtZSxcclxuICAgICAgICAgICAgICBiYWRnZTogb3B0aW9uLnR5cGUgPT09ICdhbGlhcycgPyAnQWxpYXMnIDogJ0NvbXBhbnknLFxyXG4gICAgICAgICAgICAgIGJhZGdlQ29sb3I6IG9wdGlvbi50eXBlID09PSAnYWxpYXMnID8gJ2JnLWJsdWUtNTAwJyA6ICdiZy1ncmVlbi01MDAnXHJcbiAgICAgICAgICAgIH0pKX1cclxuICAgICAgICAgIC8+XHJcbiAgICAgICAgPC9kaXY+XHJcblxyXG4gICAgICAgIHsvKiBBZGRyZXNzICovfVxyXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiWyZfZGl2W2NsYXNzKj0nZmxleCddW2NsYXNzKj0naXRlbXMtY2VudGVyJ11dOiFoLTEwIFsmX2lucHV0XTohdGV4dC1zbSBbJl9sYWJlbF06IXRleHQtc21cIj5cclxuICAgICAgICAgIDxTZWFyY2hTZWxlY3RcclxuICAgICAgICAgICAgZm9ybT17Zm9ybX1cclxuICAgICAgICAgICAgbmFtZT17YGVudHJpZXMuJHtlbnRyeUluZGV4fS4ke2ZpZWxkUHJlZml4fUFkZHJlc3NgfVxyXG4gICAgICAgICAgICBsYWJlbD1cIkFkZHJlc3NcIlxyXG4gICAgICAgICAgICBwbGFjZWhvbGRlcj1cIlNlbGVjdCBBZGRyZXNzXCJcclxuICAgICAgICAgICAgaXNFbnRyeVBhZ2U9e3RydWV9XHJcbiAgICAgICAgICAgIGNsYXNzTmFtZT1cInRleHQtc21cIlxyXG4gICAgICAgICAgICBvcHRpb25zPXtnZXRBZGRyZXNzT3B0aW9ucygpLm1hcCgoYWRkcmVzcykgPT4gKHtcclxuICAgICAgICAgICAgICB2YWx1ZTogYWRkcmVzcyxcclxuICAgICAgICAgICAgICBsYWJlbDogYWRkcmVzc1xyXG4gICAgICAgICAgICB9KSl9XHJcbiAgICAgICAgICAvPlxyXG4gICAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgICB7LyogWmlwY29kZSAqL31cclxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIlsmX2RpdltjbGFzcyo9J2ZsZXgnXVtjbGFzcyo9J2l0ZW1zLWNlbnRlciddXTohaC0xMCBbJl9pbnB1dF06IXRleHQtc20gWyZfbGFiZWxdOiF0ZXh0LXNtXCI+XHJcbiAgICAgICAgICA8U2VhcmNoU2VsZWN0XHJcbiAgICAgICAgICAgIGZvcm09e2Zvcm19XHJcbiAgICAgICAgICAgIG5hbWU9e2BlbnRyaWVzLiR7ZW50cnlJbmRleH0uJHtmaWVsZFByZWZpeH1aaXBjb2RlYH1cclxuICAgICAgICAgICAgbGFiZWw9XCJaaXBjb2RlXCJcclxuICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJTZWxlY3QgWmlwY29kZVwiXHJcbiAgICAgICAgICAgIGlzRW50cnlQYWdlPXt0cnVlfVxyXG4gICAgICAgICAgICBjbGFzc05hbWU9XCJ0ZXh0LXNtXCJcclxuICAgICAgICAgICAgb3B0aW9ucz17Z2V0WmlwY29kZU9wdGlvbnMoKS5tYXAoKHppcGNvZGUpID0+ICh7XHJcbiAgICAgICAgICAgICAgdmFsdWU6IHppcGNvZGUsXHJcbiAgICAgICAgICAgICAgbGFiZWw6IHppcGNvZGVcclxuICAgICAgICAgICAgfSkpfVxyXG4gICAgICAgICAgLz5cclxuICAgICAgICA8L2Rpdj5cclxuICAgICAgPC9kaXY+XHJcbiAgICA8L2Rpdj5cclxuICApO1xyXG59O1xyXG5cclxuZXhwb3J0IGRlZmF1bHQgTGVncmFuZERldGFpbHNDb21wb25lbnQ7XHJcbiJdLCJuYW1lcyI6WyJSZWFjdCIsInVzZVN0YXRlIiwidXNlQ2FsbGJhY2siLCJ1c2VNZW1vIiwidXNlRWZmZWN0IiwiU2VhcmNoU2VsZWN0IiwiTWFwUGluIiwibGVncmFuZE1hcHBpbmdfcm91dGVzIiwiZ2V0QWxsRGF0YSIsInRvYXN0IiwiTGVncmFuZERldGFpbHNDb21wb25lbnQiLCJmb3JtIiwiZW50cnlJbmRleCIsIm9uTGVncmFuZERhdGFDaGFuZ2UiLCJibG9ja1RpdGxlIiwiZmllbGRQcmVmaXgiLCJsZWdyYW5kRGF0YSIsInNldExlZ3JhbmREYXRhIiwibG9hZGluZyIsInNldExvYWRpbmciLCJmZXRjaExlZ3JhbmREYXRhIiwicmVzcG9uc2UiLCJHRVRfTEVHUkFORF9NQVBQSU5HUyIsImVycm9yIiwiZXh0cmFjdENpdHlGcm9tQWRkcmVzcyIsImFkZHJlc3MiLCJwYXJ0cyIsInNwbGl0IiwibGVuZ3RoIiwiY2l0eVBhcnQiLCJ0cmltIiwiY2l0eSIsInJlcGxhY2UiLCJ3b3JkcyIsIndvcmQiLCJ0ZXN0IiwiaW5jbHVkZXMiLCJnZXRBbGlhc09wdGlvbnMiLCJvcHRpb25zIiwiYmFzZU5hbWVNYXAiLCJNYXAiLCJuYW1lQ291bnRNYXAiLCJmb3JFYWNoIiwiZGF0YSIsImxlZ2FsTmFtZSIsImFsaWFzTmFtZSIsImFsaWFzU2hpcHBpbmdOYW1lcyIsImJhc2VOYW1lIiwic2V0IiwiaWQiLCJnZXQiLCJsb2NhdGlvbiIsInNoaXBwaW5nQmlsbGluZ0FkZHJlc3MiLCJ6aXBjb2RlIiwiemlwUG9zdGFsIiwiZGlzcGxheU5hbWUiLCJ0eXBlIiwibmFtZUNvdW50IiwidW5pcXVlS2V5IiwiY3VzdG9tZUNvZGUiLCJwdXNoIiwidmFsdWUiLCJsYWJlbCIsInVuaXF1ZU9wdGlvbnMiLCJmaWx0ZXIiLCJvcHRpb24iLCJpbmRleCIsInNlbGYiLCJmaW5kSW5kZXgiLCJvIiwic29ydCIsImEiLCJiIiwibG9jYWxlQ29tcGFyZSIsImdldEJhc2VOYW1lIiwiY2hlY2tGb3JEdXBsaWNhdGVzIiwiZmllbGQiLCJtYXRjaGluZ0VudHJpZXMiLCJleGFjdE1hdGNoIiwiZmluZCIsInNhbWVCYXNlTmFtZUVudHJpZXMiLCJzZWxlY3RlZERpc3BsYXlOYW1lIiwic2FtZURpc3BsYXlOYW1lRW50cmllcyIsInNhbWVOYW1lRW50cmllcyIsImQiLCJjbGVhck90aGVyQmxvY2tzIiwiY3VycmVudFByZWZpeCIsInByZWZpeGVzIiwib3RoZXJQcmVmaXhlcyIsInAiLCJwcmVmaXgiLCJjdXJyZW50QWxpYXMiLCJnZXRWYWx1ZXMiLCJjdXJyZW50QWRkcmVzcyIsImN1cnJlbnRaaXBjb2RlIiwic2V0VmFsdWUiLCJzdWJzY3JpcHRpb24iLCJ3YXRjaCIsIl8iLCJuYW1lIiwic2VsZWN0ZWREYXRhIiwiZHVwbGljYXRlRW50cmllcyIsImFkZHJlc3NNYXRjaGVzQWxpYXMiLCJzb21lIiwiZW50cnkiLCJzcGVjaWZpY0VudHJ5IiwiemlwY29kZU1hdGNoZXNBbGlhcyIsImN1cnJlbnRDb21wYW55IiwiYnVzaW5lc3NVbml0IiwiYWxsRGl2aXNpb25zIiwic3BsaXREaXZpc2lvbnMiLCJtYXAiLCJ1bmlxdWVEaXZpc2lvbnMiLCJBcnJheSIsImZyb20iLCJTZXQiLCJjb2RlIiwiY3VycmVudERpdmlzaW9uIiwiZGl2aXNpb25Db2RlVG9QYXNzIiwic2VsZWN0ZWRBbGlhc0RhdGEiLCJhZGRyZXNzTWF0Y2hlc0Jhc2VOYW1lIiwiYWRkcmVzc1NwZWNpZmljRW50cnkiLCJuZXdVbmlxdWVLZXkiLCJ1bmlxdWVaaXBjb2RlcyIsInppcCIsInVuc3Vic2NyaWJlIiwiZ2V0QWRkcmVzc09wdGlvbnMiLCJpbmRleE9mIiwidW5pcXVlQWRkcmVzc2VzIiwiYWRkIiwiZ2V0WmlwY29kZU9wdGlvbnMiLCJnZXRDdXJyZW50QWxpYXNPcHRpb25zIiwiZ2V0RmlsdGVyZWRBbGlhc09wdGlvbnMiLCJmaWx0ZXJlZERhdGEiLCJkdXAiLCJkTGVnYWxOYW1lIiwiZEFsaWFzTmFtZSIsImREaXNwbGF5TmFtZSIsImZpbmFsRGlzcGxheU5hbWUiLCJkaXYiLCJjbGFzc05hbWUiLCJoMyIsInBsYWNlaG9sZGVyIiwiaXNFbnRyeVBhZ2UiLCJiYWRnZSIsImJhZGdlQ29sb3IiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/user/trackSheets/LegrandDetailsComponent.tsx\n"));

/***/ })

});