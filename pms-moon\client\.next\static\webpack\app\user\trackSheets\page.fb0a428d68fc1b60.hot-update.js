"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/user/trackSheets/page",{

/***/ "(app-pages-browser)/./app/user/trackSheets/LegrandDetailsComponent.tsx":
/*!**********************************************************!*\
  !*** ./app/user/trackSheets/LegrandDetailsComponent.tsx ***!
  \**********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _app_component_SearchSelect__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/app/_component/SearchSelect */ \"(app-pages-browser)/./app/_component/SearchSelect.tsx\");\n/* harmony import */ var _barrel_optimize_names_MapPin_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=MapPin!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _lib_routePath__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/routePath */ \"(app-pages-browser)/./lib/routePath.ts\");\n/* harmony import */ var _lib_helpers__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/helpers */ \"(app-pages-browser)/./lib/helpers.ts\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react-hot-toast */ \"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst LegrandDetailsComponent = (param)=>{\n    let { form, entryIndex, onLegrandDataChange, blockTitle = \"LEGRAND Details\", fieldPrefix = \"legrand\" } = param;\n    _s();\n    const [legrandData, setLegrandData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const fetchLegrandData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async ()=>{\n        try {\n            setLoading(true);\n            const response = await (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_4__.getAllData)(_lib_routePath__WEBPACK_IMPORTED_MODULE_3__.legrandMapping_routes.GET_LEGRAND_MAPPINGS);\n            setLegrandData(response || []);\n        } catch (error) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_5__[\"default\"].error(\"Error fetching LEGRAND mapping data:\", error);\n            setLegrandData([]);\n        } finally{\n            setLoading(false);\n        }\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchLegrandData();\n    }, [\n        fetchLegrandData\n    ]);\n    const extractCityFromAddress = (address)=>{\n        if (!address) return \"\";\n        const parts = address.split(\",\");\n        if (parts.length >= 2) {\n            const cityPart = parts[parts.length - 2].trim();\n            const city = cityPart.replace(/\\d+/g, \"\").trim();\n            return city;\n        }\n        const words = address.split(\" \");\n        for (const word of words){\n            if (word.length > 2 && !/^\\d+$/.test(word) && !word.includes(\"ROAD\") && !word.includes(\"STREET\") && !word.includes(\"AVE\")) {\n                return word;\n            }\n        }\n        return \"\";\n    };\n    const getAliasOptions = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        if (!legrandData.length) return [];\n        const options = [];\n        // Pre-compute base names to avoid repeated calculations\n        const baseNameMap = new Map();\n        const nameCountMap = new Map();\n        // First pass: compute base names and count occurrences\n        legrandData.forEach((data)=>{\n            const legalName = data.legalName || \"Unknown Company\";\n            const aliasName = data.aliasShippingNames && data.aliasShippingNames !== \"NONE\" ? data.aliasShippingNames : null;\n            const baseName = aliasName ? \"\".concat(legalName, \" (\").concat(aliasName, \")\") : legalName;\n            baseNameMap.set(data.id, baseName);\n            nameCountMap.set(baseName, (nameCountMap.get(baseName) || 0) + 1);\n        });\n        // Second pass: build options with optimized duplicate checking\n        legrandData.forEach((data)=>{\n            const location = data.location || extractCityFromAddress(data.shippingBillingAddress || \"\");\n            const zipcode = data.zipPostal || \"\";\n            const legalName = data.legalName || \"Unknown Company\";\n            const aliasName = data.aliasShippingNames && data.aliasShippingNames !== \"NONE\" ? data.aliasShippingNames : null;\n            let displayName = baseNameMap.get(data.id) || legalName;\n            const type = aliasName ? \"alias\" : \"company\";\n            // Only add location/zipcode suffix if there are duplicates\n            const nameCount = nameCountMap.get(displayName) || 1;\n            if (nameCount > 1) {\n                if (location) {\n                    displayName = \"\".concat(displayName, \"_\").concat(location);\n                } else if (zipcode) {\n                    displayName = \"\".concat(displayName, \"_\").concat(zipcode);\n                }\n            }\n            const uniqueKey = \"\".concat(data.customeCode, \"-\").concat(data.aliasShippingNames || data.legalName, \"-\").concat(data.shippingBillingAddress);\n            options.push({\n                value: uniqueKey,\n                label: displayName,\n                type: type,\n                displayName: displayName\n            });\n        });\n        // Remove duplicates and sort\n        const uniqueOptions = options.filter((option, index, self)=>index === self.findIndex((o)=>o.value === option.value));\n        return uniqueOptions.sort((a, b)=>a.label.localeCompare(b.label));\n    }, [\n        legrandData\n    ]);\n    const getBaseName = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((data)=>{\n        const legalName = data.legalName || \"Unknown Company\";\n        const aliasName = data.aliasShippingNames && data.aliasShippingNames !== \"NONE\" ? data.aliasShippingNames : null;\n        if (aliasName) {\n            return \"\".concat(legalName, \" (\").concat(aliasName, \")\");\n        } else {\n            return legalName;\n        }\n    }, []);\n    const checkForDuplicates = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        if (!legrandData.length) return ()=>[];\n        return (field, value)=>{\n            let matchingEntries = [];\n            if (field === \"alias\") {\n                const exactMatch = legrandData.find((data)=>{\n                    const uniqueKey = \"\".concat(data.customeCode, \"-\").concat(data.aliasShippingNames || data.legalName, \"-\").concat(data.shippingBillingAddress);\n                    return uniqueKey === value;\n                });\n                if (exactMatch) {\n                    const baseName = getBaseName(exactMatch);\n                    const sameBaseNameEntries = legrandData.filter((data)=>getBaseName(data) === baseName);\n                    if (sameBaseNameEntries.length === 1) {\n                        matchingEntries = [\n                            exactMatch\n                        ];\n                    } else {\n                        var _getAliasOptions_find;\n                        const selectedDisplayName = ((_getAliasOptions_find = getAliasOptions.find((option)=>option.value === value)) === null || _getAliasOptions_find === void 0 ? void 0 : _getAliasOptions_find.displayName) || \"\";\n                        const sameDisplayNameEntries = legrandData.filter((data)=>{\n                            const location = data.location || extractCityFromAddress(data.shippingBillingAddress || \"\");\n                            let displayName = getBaseName(data);\n                            const sameNameEntries = legrandData.filter((d)=>getBaseName(d) === displayName);\n                            if (sameNameEntries.length > 1) {\n                                if (location) {\n                                    displayName = \"\".concat(displayName, \"_\").concat(location);\n                                } else if (data.zipPostal) {\n                                    displayName = \"\".concat(displayName, \"_\").concat(data.zipPostal);\n                                }\n                            }\n                            return displayName === selectedDisplayName;\n                        });\n                        matchingEntries = sameDisplayNameEntries;\n                    }\n                }\n            } else if (field === \"address\") {\n                matchingEntries = legrandData.filter((data)=>data.shippingBillingAddress === value);\n            } else if (field === \"zipcode\") {\n                matchingEntries = legrandData.filter((data)=>data.zipPostal === value);\n            }\n            return matchingEntries;\n        };\n    }, [\n        legrandData,\n        getBaseName,\n        getAliasOptions\n    ]);\n    const clearOtherBlocks = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((currentPrefix)=>{\n        const prefixes = [\n            \"shipper\",\n            \"consignee\",\n            \"billto\"\n        ];\n        const otherPrefixes = prefixes.filter((p)=>p !== currentPrefix);\n        otherPrefixes.forEach((prefix)=>{\n            const currentAlias = form.getValues(\"entries.\".concat(entryIndex, \".\").concat(prefix, \"Alias\"));\n            const currentAddress = form.getValues(\"entries.\".concat(entryIndex, \".\").concat(prefix, \"Address\"));\n            const currentZipcode = form.getValues(\"entries.\".concat(entryIndex, \".\").concat(prefix, \"Zipcode\"));\n            if (currentAlias || currentAddress || currentZipcode) {\n                form.setValue(\"entries.\".concat(entryIndex, \".\").concat(prefix, \"Alias\"), \"\");\n                form.setValue(\"entries.\".concat(entryIndex, \".\").concat(prefix, \"Address\"), \"\");\n                form.setValue(\"entries.\".concat(entryIndex, \".\").concat(prefix, \"Zipcode\"), \"\");\n            }\n        });\n    }, [\n        form,\n        entryIndex\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!legrandData.length) return;\n        const subscription = form.watch((_, param)=>{\n            let { name, type } = param;\n            if (!name || !name.includes(\"entries.\".concat(entryIndex, \".\").concat(fieldPrefix)) || type !== \"change\") return;\n            const currentAlias = form.getValues(\"entries.\".concat(entryIndex, \".\").concat(fieldPrefix, \"Alias\"));\n            const currentAddress = form.getValues(\"entries.\".concat(entryIndex, \".\").concat(fieldPrefix, \"Address\"));\n            const currentZipcode = form.getValues(\"entries.\".concat(entryIndex, \".\").concat(fieldPrefix, \"Zipcode\"));\n            if (currentAlias || currentAddress || currentZipcode) {\n                clearOtherBlocks(fieldPrefix);\n            }\n            if (name === \"entries.\".concat(entryIndex, \".\").concat(fieldPrefix, \"Alias\") && currentAlias) {\n                const selectedData = legrandData.find((data)=>{\n                    const uniqueKey = \"\".concat(data.customeCode, \"-\").concat(data.aliasShippingNames || data.legalName, \"-\").concat(data.shippingBillingAddress);\n                    return uniqueKey === currentAlias;\n                });\n                if (selectedData) {\n                    const duplicateEntries = checkForDuplicates(\"alias\", currentAlias);\n                    if (duplicateEntries.length > 1) {\n                        if (currentAddress) {\n                            const addressMatchesAlias = duplicateEntries.some((entry)=>entry.shippingBillingAddress === currentAddress);\n                            if (addressMatchesAlias) {\n                                const specificEntry = duplicateEntries.find((entry)=>entry.shippingBillingAddress === currentAddress);\n                                if (specificEntry && currentZipcode !== specificEntry.zipPostal) {\n                                    form.setValue(\"entries.\".concat(entryIndex, \".\").concat(fieldPrefix, \"Zipcode\"), specificEntry.zipPostal);\n                                }\n                            } else {\n                                form.setValue(\"entries.\".concat(entryIndex, \".\").concat(fieldPrefix, \"Address\"), \"\");\n                                if (currentZipcode !== \"\") {\n                                    form.setValue(\"entries.\".concat(entryIndex, \".\").concat(fieldPrefix, \"Zipcode\"), \"\");\n                                }\n                            }\n                        } else {\n                            if (currentZipcode) {\n                                const zipcodeMatchesAlias = duplicateEntries.some((entry)=>entry.zipPostal === currentZipcode);\n                                if (zipcodeMatchesAlias) {} else {\n                                    form.setValue(\"entries.\".concat(entryIndex, \".\").concat(fieldPrefix, \"Zipcode\"), \"\");\n                                }\n                            } else {}\n                        }\n                    } else {\n                        if (currentAddress !== selectedData.shippingBillingAddress) {\n                            form.setValue(\"entries.\".concat(entryIndex, \".\").concat(fieldPrefix, \"Address\"), selectedData.shippingBillingAddress);\n                        }\n                        if (currentZipcode !== selectedData.zipPostal) {\n                            form.setValue(\"entries.\".concat(entryIndex, \".\").concat(fieldPrefix, \"Zipcode\"), selectedData.zipPostal);\n                        }\n                    }\n                    const currentCompany = form.getValues(\"entries.\".concat(entryIndex, \".company\"));\n                    if (currentCompany !== selectedData.businessUnit) {\n                        form.setValue(\"entries.\".concat(entryIndex, \".company\"), selectedData.businessUnit);\n                    }\n                    const allDivisions = [];\n                    duplicateEntries.forEach((entry)=>{\n                        if (entry.customeCode) {\n                            if (entry.customeCode.includes(\"/\")) {\n                                const splitDivisions = entry.customeCode.split(\"/\").map((d)=>d.trim());\n                                allDivisions.push(...splitDivisions);\n                            } else {\n                                allDivisions.push(entry.customeCode);\n                            }\n                        }\n                    });\n                    const uniqueDivisions = Array.from(new Set(allDivisions.filter((code)=>code)));\n                    if (uniqueDivisions.length === 1) {\n                        const currentDivision = form.getValues(\"entries.\".concat(entryIndex, \".division\"));\n                        if (currentDivision !== uniqueDivisions[0]) {\n                            form.setValue(\"entries.\".concat(entryIndex, \".division\"), uniqueDivisions[0]);\n                        }\n                    } else {\n                        const currentDivision = form.getValues(\"entries.\".concat(entryIndex, \".division\"));\n                        if (currentDivision !== \"\") {\n                            form.setValue(\"entries.\".concat(entryIndex, \".division\"), \"\");\n                        }\n                    }\n                    if (onLegrandDataChange) {\n                        const divisionCodeToPass = uniqueDivisions.length === 1 ? uniqueDivisions[0] : \"\";\n                        onLegrandDataChange(entryIndex, selectedData.businessUnit || \"\", divisionCodeToPass);\n                    }\n                }\n            }\n            if (name === \"entries.\".concat(entryIndex, \".\").concat(fieldPrefix, \"Address\") && currentAddress) {\n                const duplicateEntries = checkForDuplicates(\"address\", currentAddress);\n                if (duplicateEntries.length > 1) {\n                    if (currentAlias) {\n                        const selectedAliasData = legrandData.find((data)=>{\n                            const uniqueKey = \"\".concat(data.customeCode, \"-\").concat(data.aliasShippingNames || data.legalName, \"-\").concat(data.shippingBillingAddress);\n                            return uniqueKey === currentAlias;\n                        });\n                        if (selectedAliasData) {\n                            const baseName = getBaseName(selectedAliasData);\n                            const addressMatchesBaseName = legrandData.some((data)=>getBaseName(data) === baseName && data.shippingBillingAddress === currentAddress);\n                            if (addressMatchesBaseName) {\n                                const addressSpecificEntry = legrandData.find((data)=>getBaseName(data) === baseName && data.shippingBillingAddress === currentAddress);\n                                if (addressSpecificEntry) {\n                                    const newUniqueKey = \"\".concat(addressSpecificEntry.customeCode, \"-\").concat(addressSpecificEntry.aliasShippingNames || addressSpecificEntry.legalName, \"-\").concat(addressSpecificEntry.shippingBillingAddress);\n                                    if (currentAlias !== newUniqueKey) {\n                                        form.setValue(\"entries.\".concat(entryIndex, \".\").concat(fieldPrefix, \"Alias\"), newUniqueKey);\n                                    }\n                                    if (currentZipcode !== addressSpecificEntry.zipPostal) {\n                                        form.setValue(\"entries.\".concat(entryIndex, \".\").concat(fieldPrefix, \"Zipcode\"), addressSpecificEntry.zipPostal);\n                                    }\n                                    const currentCompany = form.getValues(\"entries.\".concat(entryIndex, \".company\"));\n                                    if (currentCompany !== addressSpecificEntry.businessUnit) {\n                                        form.setValue(\"entries.\".concat(entryIndex, \".company\"), addressSpecificEntry.businessUnit);\n                                    }\n                                    const currentDivision = form.getValues(\"entries.\".concat(entryIndex, \".division\"));\n                                    if (currentDivision !== addressSpecificEntry.customeCode) {\n                                        form.setValue(\"entries.\".concat(entryIndex, \".division\"), addressSpecificEntry.customeCode);\n                                    }\n                                    if (onLegrandDataChange) {\n                                        onLegrandDataChange(entryIndex, addressSpecificEntry.businessUnit || \"\", addressSpecificEntry.customeCode || \"\");\n                                    }\n                                    return;\n                                }\n                            } else {\n                                form.setValue(\"entries.\".concat(entryIndex, \".\").concat(fieldPrefix, \"Alias\"), \"\");\n                            }\n                        }\n                    }\n                    const uniqueZipcodes = Array.from(new Set(duplicateEntries.map((entry)=>entry.zipPostal).filter((zip)=>zip)));\n                    if (uniqueZipcodes.length === 1) {\n                        if (currentZipcode !== uniqueZipcodes[0]) {\n                            form.setValue(\"entries.\".concat(entryIndex, \".\").concat(fieldPrefix, \"Zipcode\"), uniqueZipcodes[0]);\n                        }\n                    } else {\n                        if (currentZipcode !== \"\") {\n                            form.setValue(\"entries.\".concat(entryIndex, \".\").concat(fieldPrefix, \"Zipcode\"), \"\");\n                        }\n                    }\n                } else {\n                    const selectedData = duplicateEntries[0];\n                    if (selectedData) {\n                        const uniqueKey = \"\".concat(selectedData.customeCode, \"-\").concat(selectedData.aliasShippingNames || selectedData.legalName, \"-\").concat(selectedData.shippingBillingAddress);\n                        if (currentAlias !== uniqueKey) {\n                            form.setValue(\"entries.\".concat(entryIndex, \".\").concat(fieldPrefix, \"Alias\"), uniqueKey);\n                        }\n                        if (currentZipcode !== selectedData.zipPostal) {\n                            form.setValue(\"entries.\".concat(entryIndex, \".\").concat(fieldPrefix, \"Zipcode\"), selectedData.zipPostal);\n                        }\n                        const currentCompany = form.getValues(\"entries.\".concat(entryIndex, \".company\"));\n                        if (currentCompany !== selectedData.businessUnit) {\n                            form.setValue(\"entries.\".concat(entryIndex, \".company\"), selectedData.businessUnit);\n                        }\n                        const currentDivision = form.getValues(\"entries.\".concat(entryIndex, \".division\"));\n                        if (currentDivision !== selectedData.customeCode) {\n                            form.setValue(\"entries.\".concat(entryIndex, \".division\"), selectedData.customeCode);\n                        }\n                        if (onLegrandDataChange) {\n                            onLegrandDataChange(entryIndex, selectedData.businessUnit || \"\", selectedData.customeCode || \"\");\n                        }\n                    }\n                }\n            }\n            if (name === \"entries.\".concat(entryIndex, \".\").concat(fieldPrefix, \"Zipcode\") && currentZipcode) {\n                const duplicateEntries = checkForDuplicates(\"zipcode\", currentZipcode);\n                if (duplicateEntries.length > 1) {\n                    if (currentAlias !== \"\") {\n                        form.setValue(\"entries.\".concat(entryIndex, \".\").concat(fieldPrefix, \"Alias\"), \"\");\n                    }\n                    if (currentAddress !== \"\") {\n                        form.setValue(\"entries.\".concat(entryIndex, \".\").concat(fieldPrefix, \"Address\"), \"\");\n                    }\n                } else {\n                    const selectedData = duplicateEntries[0];\n                    if (selectedData) {\n                        const uniqueKey = \"\".concat(selectedData.customeCode, \"-\").concat(selectedData.aliasShippingNames || selectedData.legalName, \"-\").concat(selectedData.shippingBillingAddress);\n                        if (currentAlias !== uniqueKey) {\n                            form.setValue(\"entries.\".concat(entryIndex, \".\").concat(fieldPrefix, \"Alias\"), uniqueKey);\n                        }\n                        if (currentAddress !== selectedData.shippingBillingAddress) {\n                            form.setValue(\"entries.\".concat(entryIndex, \".\").concat(fieldPrefix, \"Address\"), selectedData.shippingBillingAddress);\n                        }\n                        const currentCompany = form.getValues(\"entries.\".concat(entryIndex, \".company\"));\n                        if (currentCompany !== selectedData.businessUnit) {\n                            form.setValue(\"entries.\".concat(entryIndex, \".company\"), selectedData.businessUnit);\n                        }\n                        const currentDivision = form.getValues(\"entries.\".concat(entryIndex, \".division\"));\n                        if (currentDivision !== selectedData.customeCode) {\n                            form.setValue(\"entries.\".concat(entryIndex, \".division\"), selectedData.customeCode);\n                        }\n                        if (onLegrandDataChange) {\n                            onLegrandDataChange(entryIndex, selectedData.businessUnit || \"\", selectedData.customeCode || \"\");\n                        }\n                    }\n                }\n            }\n        });\n        return ()=>subscription.unsubscribe();\n    }, [\n        legrandData.length,\n        form,\n        entryIndex,\n        onLegrandDataChange,\n        fieldPrefix\n    ]);\n    const getAddressOptions = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        const currentAlias = form.getValues(\"entries.\".concat(entryIndex, \".\").concat(fieldPrefix, \"Alias\"));\n        const currentZipcode = form.getValues(\"entries.\".concat(entryIndex, \".\").concat(fieldPrefix, \"Zipcode\"));\n        if (currentAlias) {\n            const selectedAliasData = legrandData.find((data)=>{\n                const uniqueKey = \"\".concat(data.customeCode, \"-\").concat(data.aliasShippingNames || data.legalName, \"-\").concat(data.shippingBillingAddress);\n                return uniqueKey === currentAlias;\n            });\n            if (selectedAliasData) {\n                var _getAliasOptions_find;\n                const selectedDisplayName = ((_getAliasOptions_find = getAliasOptions.find((option)=>option.value === currentAlias)) === null || _getAliasOptions_find === void 0 ? void 0 : _getAliasOptions_find.displayName) || \"\";\n                const sameDisplayNameEntries = legrandData.filter((data)=>{\n                    const location = data.location || extractCityFromAddress(data.shippingBillingAddress || \"\");\n                    const zipcode = data.zipPostal || \"\";\n                    const legalName = data.legalName || \"Unknown Company\";\n                    const aliasName = data.aliasShippingNames && data.aliasShippingNames !== \"NONE\" ? data.aliasShippingNames : null;\n                    let displayName = \"\";\n                    if (aliasName) {\n                        displayName = \"\".concat(legalName, \" (\").concat(aliasName, \")\");\n                    } else {\n                        displayName = legalName;\n                    }\n                    const baseName = displayName;\n                    const sameNameEntries = legrandData.filter((d)=>getBaseName(d) === displayName);\n                    if (sameNameEntries.length > 1) {\n                        if (location) {\n                            displayName = \"\".concat(displayName, \"_\").concat(location);\n                        } else if (data.zipPostal) {\n                            displayName = \"\".concat(displayName, \"_\").concat(data.zipPostal);\n                        }\n                    }\n                    return displayName === selectedDisplayName;\n                });\n                if (sameDisplayNameEntries.length > 1) {\n                    return sameDisplayNameEntries.map((data)=>data.shippingBillingAddress).filter((address)=>address).filter((address, index, self)=>self.indexOf(address) === index).sort();\n                } else {\n                    return selectedAliasData.shippingBillingAddress ? [\n                        selectedAliasData.shippingBillingAddress\n                    ] : [];\n                }\n            }\n        }\n        if (currentZipcode) {\n            const duplicateEntries = checkForDuplicates(\"zipcode\", currentZipcode);\n            if (duplicateEntries.length > 1) {\n                return duplicateEntries.map((data)=>data.shippingBillingAddress).filter((address)=>address).filter((address, index, self)=>self.indexOf(address) === index).sort();\n            }\n        }\n        const uniqueAddresses = new Set();\n        legrandData.forEach((data)=>{\n            if (data.shippingBillingAddress) {\n                uniqueAddresses.add(data.shippingBillingAddress);\n            }\n        });\n        return Array.from(uniqueAddresses).sort();\n    }, [\n        form,\n        entryIndex,\n        fieldPrefix,\n        legrandData,\n        checkForDuplicates\n    ]);\n    const getZipcodeOptions = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        const currentAlias = form.getValues(\"entries.\".concat(entryIndex, \".\").concat(fieldPrefix, \"Alias\"));\n        const currentAddress = form.getValues(\"entries.\".concat(entryIndex, \".\").concat(fieldPrefix, \"Address\"));\n        if (currentAlias) {\n            const selectedAliasData = legrandData.find((data)=>{\n                const uniqueKey = \"\".concat(data.customeCode, \"-\").concat(data.aliasShippingNames || data.legalName, \"-\").concat(data.shippingBillingAddress);\n                return uniqueKey === currentAlias;\n            });\n            if (selectedAliasData) {\n                var _getAliasOptions_find;\n                const selectedDisplayName = ((_getAliasOptions_find = getAliasOptions.find((option)=>option.value === currentAlias)) === null || _getAliasOptions_find === void 0 ? void 0 : _getAliasOptions_find.displayName) || \"\";\n                const sameDisplayNameEntries = legrandData.filter((data)=>{\n                    const location = data.location || extractCityFromAddress(data.shippingBillingAddress || \"\");\n                    const zipcode = data.zipPostal || \"\";\n                    const legalName = data.legalName || \"Unknown Company\";\n                    const aliasName = data.aliasShippingNames && data.aliasShippingNames !== \"NONE\" ? data.aliasShippingNames : null;\n                    let displayName = \"\";\n                    if (aliasName) {\n                        displayName = \"\".concat(legalName, \" (\").concat(aliasName, \")\");\n                    } else {\n                        displayName = legalName;\n                    }\n                    const sameNameEntries = legrandData.filter((d)=>getBaseName(d) === displayName);\n                    if (sameNameEntries.length > 1) {\n                        if (location) {\n                            displayName = \"\".concat(displayName, \"_\").concat(location);\n                        } else if (data.zipPostal) {\n                            displayName = \"\".concat(displayName, \"_\").concat(data.zipPostal);\n                        }\n                    }\n                    return displayName === selectedDisplayName;\n                });\n                if (sameDisplayNameEntries.length > 1) {\n                    return sameDisplayNameEntries.map((data)=>data.zipPostal).filter((zipcode)=>zipcode).filter((zipcode, index, self)=>self.indexOf(zipcode) === index).sort();\n                } else {\n                    return selectedAliasData.zipPostal ? [\n                        selectedAliasData.zipPostal\n                    ] : [];\n                }\n            }\n        }\n        if (currentAddress) {\n            const duplicateEntries = checkForDuplicates(\"address\", currentAddress);\n            if (duplicateEntries.length > 1) {\n                return duplicateEntries.map((data)=>data.zipPostal).filter((zipcode)=>zipcode).filter((zipcode, index, self)=>self.indexOf(zipcode) === index).sort();\n            }\n        }\n        const uniqueZipcodes = new Set();\n        legrandData.forEach((data)=>{\n            if (data.zipPostal) {\n                uniqueZipcodes.add(data.zipPostal);\n            }\n        });\n        return Array.from(uniqueZipcodes).sort();\n    }, [\n        form,\n        entryIndex,\n        fieldPrefix,\n        legrandData,\n        checkForDuplicates\n    ]);\n    const getCurrentAliasOptions = ()=>{\n        const currentAddress = form.getValues(\"entries.\".concat(entryIndex, \".\").concat(fieldPrefix, \"Address\"));\n        const currentZipcode = form.getValues(\"entries.\".concat(entryIndex, \".\").concat(fieldPrefix, \"Zipcode\"));\n        if (currentAddress) {\n            const duplicateEntries = checkForDuplicates(\"address\", currentAddress);\n            if (duplicateEntries.length > 1) {\n                return getFilteredAliasOptions();\n            }\n        }\n        if (currentZipcode) {\n            const duplicateEntries = checkForDuplicates(\"zipcode\", currentZipcode);\n            if (duplicateEntries.length > 1) {\n                return getFilteredAliasOptions();\n            }\n        }\n        return getAliasOptions;\n    };\n    const getFilteredAliasOptions = ()=>{\n        const currentAddress = form.getValues(\"entries.\".concat(entryIndex, \".\").concat(fieldPrefix, \"Address\"));\n        const currentZipcode = form.getValues(\"entries.\".concat(entryIndex, \".\").concat(fieldPrefix, \"Zipcode\"));\n        let filteredData = legrandData;\n        if (currentAddress) {\n            const duplicateEntries = checkForDuplicates(\"address\", currentAddress);\n            if (duplicateEntries.length > 1) {\n                filteredData = duplicateEntries;\n            }\n        }\n        if (currentZipcode) {\n            const duplicateEntries = checkForDuplicates(\"zipcode\", currentZipcode);\n            if (duplicateEntries.length > 1) {\n                filteredData = filteredData.filter((data)=>duplicateEntries.some((dup)=>dup.id === data.id));\n            }\n        }\n        const options = [];\n        filteredData.forEach((data)=>{\n            const location = data.location || extractCityFromAddress(data.shippingBillingAddress || \"\");\n            const zipcode = data.zipPostal || \"\";\n            let displayName = \"\";\n            let type = \"company\";\n            const legalName = data.legalName || \"Unknown Company\";\n            const aliasName = data.aliasShippingNames && data.aliasShippingNames !== \"NONE\" ? data.aliasShippingNames : null;\n            if (aliasName) {\n                displayName = \"\".concat(legalName, \" (\").concat(aliasName, \")\");\n                type = \"alias\";\n            } else {\n                displayName = legalName;\n                type = \"company\";\n            }\n            const uniqueKey = \"\".concat(data.customeCode, \"-\").concat(data.aliasShippingNames || data.legalName, \"-\").concat(data.shippingBillingAddress);\n            const sameNameEntries = filteredData.filter((d)=>{\n                const dLegalName = d.legalName || \"Unknown Company\";\n                const dAliasName = d.aliasShippingNames && d.aliasShippingNames !== \"NONE\" ? d.aliasShippingNames : null;\n                let dDisplayName = \"\";\n                if (dAliasName) {\n                    dDisplayName = \"\".concat(dLegalName, \" (\").concat(dAliasName, \")\");\n                } else {\n                    dDisplayName = dLegalName;\n                }\n                return dDisplayName === displayName;\n            });\n            let finalDisplayName = displayName;\n            if (sameNameEntries.length > 1) {\n                if (location) {\n                    finalDisplayName = \"\".concat(displayName, \"_\").concat(location);\n                } else if (zipcode) {\n                    finalDisplayName = \"\".concat(displayName, \"_\").concat(zipcode);\n                }\n            }\n            options.push({\n                value: uniqueKey,\n                label: finalDisplayName,\n                type: type,\n                displayName: finalDisplayName\n            });\n        });\n        const uniqueOptions = options.filter((option, index, self)=>index === self.findIndex((o)=>o.value === option.value));\n        return uniqueOptions.sort((a, b)=>a.label.localeCompare(b.label));\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-gray-50 rounded-lg p-3 max-w-sm mb-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-2 mb-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MapPin_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            className: \"w-4 h-4 text-purple-600\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\LegrandDetailsComponent.tsx\",\n                            lineNumber: 707,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-sm font-semibold text-gray-900\",\n                            children: \"LEGRAND Details\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\LegrandDetailsComponent.tsx\",\n                            lineNumber: 708,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\LegrandDetailsComponent.tsx\",\n                    lineNumber: 706,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-sm text-gray-500\",\n                    children: \"Loading LEGRAND data...\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\LegrandDetailsComponent.tsx\",\n                    lineNumber: 710,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\LegrandDetailsComponent.tsx\",\n            lineNumber: 705,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-gray-50 rounded-lg p-3 mb-3 max-w-lg\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-2 mb-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MapPin_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        className: \"w-4 h-4 text-purple-600\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\LegrandDetailsComponent.tsx\",\n                        lineNumber: 718,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-sm font-semibold text-gray-900\",\n                        children: blockTitle\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\LegrandDetailsComponent.tsx\",\n                        lineNumber: 719,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\LegrandDetailsComponent.tsx\",\n                lineNumber: 717,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 gap-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"[&_div[class*='flex'][class*='items-center']]:!h-10 [&_input]:!text-sm [&_label]:!text-sm\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_SearchSelect__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            form: form,\n                            name: \"entries.\".concat(entryIndex, \".\").concat(fieldPrefix, \"Alias\"),\n                            label: \"Alias/Company\",\n                            placeholder: \"Select Alias or Company\",\n                            isEntryPage: true,\n                            className: \"text-sm\",\n                            options: getCurrentAliasOptions().map((option)=>({\n                                    value: option.value,\n                                    label: option.displayName,\n                                    badge: option.type === \"alias\" ? \"Alias\" : \"Company\",\n                                    badgeColor: option.type === \"alias\" ? \"bg-blue-500\" : \"bg-green-500\"\n                                }))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\LegrandDetailsComponent.tsx\",\n                            lineNumber: 726,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\LegrandDetailsComponent.tsx\",\n                        lineNumber: 725,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"[&_div[class*='flex'][class*='items-center']]:!h-10 [&_input]:!text-sm [&_label]:!text-sm\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_SearchSelect__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            form: form,\n                            name: \"entries.\".concat(entryIndex, \".\").concat(fieldPrefix, \"Address\"),\n                            label: \"Address\",\n                            placeholder: \"Select Address\",\n                            isEntryPage: true,\n                            className: \"text-sm\",\n                            options: getAddressOptions().map((address)=>({\n                                    value: address,\n                                    label: address\n                                }))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\LegrandDetailsComponent.tsx\",\n                            lineNumber: 744,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\LegrandDetailsComponent.tsx\",\n                        lineNumber: 743,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"[&_div[class*='flex'][class*='items-center']]:!h-10 [&_input]:!text-sm [&_label]:!text-sm\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_SearchSelect__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            form: form,\n                            name: \"entries.\".concat(entryIndex, \".\").concat(fieldPrefix, \"Zipcode\"),\n                            label: \"Zipcode\",\n                            placeholder: \"Select Zipcode\",\n                            isEntryPage: true,\n                            className: \"text-sm\",\n                            options: getZipcodeOptions().map((zipcode)=>({\n                                    value: zipcode,\n                                    label: zipcode\n                                }))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\LegrandDetailsComponent.tsx\",\n                            lineNumber: 760,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\LegrandDetailsComponent.tsx\",\n                        lineNumber: 759,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\LegrandDetailsComponent.tsx\",\n                lineNumber: 723,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\LegrandDetailsComponent.tsx\",\n        lineNumber: 716,\n        columnNumber: 5\n    }, undefined);\n};\n_s(LegrandDetailsComponent, \"yvJ6xwk5dPDXdZ//nSkhUJh1NW0=\");\n_c = LegrandDetailsComponent;\n/* harmony default export */ __webpack_exports__[\"default\"] = (LegrandDetailsComponent);\nvar _c;\n$RefreshReg$(_c, \"LegrandDetailsComponent\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/user/trackSheets/LegrandDetailsComponent.tsx\n"));

/***/ })

});