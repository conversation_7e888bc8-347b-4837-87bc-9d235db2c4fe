"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/user/trackSheets/page",{

/***/ "(app-pages-browser)/./app/user/trackSheets/LegrandDetailsComponent.tsx":
/*!**********************************************************!*\
  !*** ./app/user/trackSheets/LegrandDetailsComponent.tsx ***!
  \**********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _app_component_SearchSelect__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/app/_component/SearchSelect */ \"(app-pages-browser)/./app/_component/SearchSelect.tsx\");\n/* harmony import */ var _barrel_optimize_names_MapPin_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=MapPin!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _lib_routePath__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/routePath */ \"(app-pages-browser)/./lib/routePath.ts\");\n/* harmony import */ var _lib_helpers__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/helpers */ \"(app-pages-browser)/./lib/helpers.ts\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react-hot-toast */ \"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst LegrandDetailsComponent = (param)=>{\n    let { form, entryIndex, onLegrandDataChange, blockTitle = \"LEGRAND Details\", fieldPrefix = \"legrand\" } = param;\n    _s();\n    const [legrandData, setLegrandData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const fetchLegrandData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async ()=>{\n        try {\n            setLoading(true);\n            const response = await (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_4__.getAllData)(_lib_routePath__WEBPACK_IMPORTED_MODULE_3__.legrandMapping_routes.GET_LEGRAND_MAPPINGS);\n            setLegrandData(response || []);\n        } catch (error) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_5__[\"default\"].error(\"Error fetching LEGRAND mapping data:\", error);\n            setLegrandData([]);\n        } finally{\n            setLoading(false);\n        }\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchLegrandData();\n    }, [\n        fetchLegrandData\n    ]);\n    const extractCityFromAddress = (address)=>{\n        if (!address) return \"\";\n        const parts = address.split(\",\");\n        if (parts.length >= 2) {\n            const cityPart = parts[parts.length - 2].trim();\n            const city = cityPart.replace(/\\d+/g, \"\").trim();\n            return city;\n        }\n        const words = address.split(\" \");\n        for (const word of words){\n            if (word.length > 2 && !/^\\d+$/.test(word) && !word.includes(\"ROAD\") && !word.includes(\"STREET\") && !word.includes(\"AVE\")) {\n                return word;\n            }\n        }\n        return \"\";\n    };\n    const getAliasOptions = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        const options = [];\n        legrandData.forEach((data)=>{\n            const location = data.location || extractCityFromAddress(data.shippingBillingAddress || \"\");\n            const zipcode = data.zipPostal || \"\";\n            let displayName = \"\";\n            let type = \"company\";\n            const legalName = data.legalName || \"Unknown Company\";\n            const aliasName = data.aliasShippingNames && data.aliasShippingNames !== \"NONE\" ? data.aliasShippingNames : null;\n            if (aliasName) {\n                displayName = \"\".concat(legalName, \" (\").concat(aliasName, \")\");\n                type = \"alias\";\n            } else {\n                displayName = legalName;\n                type = \"company\";\n            }\n            const uniqueKey = \"\".concat(data.customeCode, \"-\").concat(data.aliasShippingNames || data.legalName, \"-\").concat(data.shippingBillingAddress);\n            const sameNameEntries = legrandData.filter((d)=>{\n                const dLegalName = d.legalName || \"Unknown Company\";\n                const dAliasName = d.aliasShippingNames && d.aliasShippingNames !== \"NONE\" ? d.aliasShippingNames : null;\n                let dDisplayName = \"\";\n                if (dAliasName) {\n                    dDisplayName = \"\".concat(dLegalName, \" (\").concat(dAliasName, \")\");\n                } else {\n                    dDisplayName = dLegalName;\n                }\n                return dDisplayName === displayName;\n            });\n            let finalDisplayName = displayName;\n            if (sameNameEntries.length > 1) {\n                if (location) {\n                    finalDisplayName = \"\".concat(displayName, \"_\").concat(location);\n                } else if (zipcode) {\n                    finalDisplayName = \"\".concat(displayName, \"_\").concat(zipcode);\n                }\n            }\n            options.push({\n                value: uniqueKey,\n                label: finalDisplayName,\n                type: type,\n                displayName: finalDisplayName\n            });\n        });\n        const uniqueOptions = options.filter((option, index, self)=>index === self.findIndex((o)=>o.value === option.value));\n        return uniqueOptions.sort((a, b)=>a.label.localeCompare(b.label));\n    }, [\n        legrandData\n    ]);\n    const getBaseName = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((data)=>{\n        const legalName = data.legalName || \"Unknown Company\";\n        const aliasName = data.aliasShippingNames && data.aliasShippingNames !== \"NONE\" ? data.aliasShippingNames : null;\n        if (aliasName) {\n            return \"\".concat(legalName, \" (\").concat(aliasName, \")\");\n        } else {\n            return legalName;\n        }\n    }, []);\n    const checkForDuplicates = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((field, value)=>{\n        let matchingEntries = [];\n        if (field === \"alias\") {\n            const exactMatch = legrandData.find((data)=>{\n                const uniqueKey = \"\".concat(data.customeCode, \"-\").concat(data.aliasShippingNames || data.legalName, \"-\").concat(data.shippingBillingAddress);\n                return uniqueKey === value;\n            });\n            if (exactMatch) {\n                const baseName = getBaseName(exactMatch);\n                const sameBaseNameEntries = legrandData.filter((data)=>getBaseName(data) === baseName);\n                if (sameBaseNameEntries.length === 1) {\n                    matchingEntries = [\n                        exactMatch\n                    ];\n                } else {\n                    var _getAliasOptions_find;\n                    const selectedDisplayName = ((_getAliasOptions_find = getAliasOptions().find((option)=>option.value === value)) === null || _getAliasOptions_find === void 0 ? void 0 : _getAliasOptions_find.displayName) || \"\";\n                    const sameDisplayNameEntries = legrandData.filter((data)=>{\n                        const location = data.location || extractCityFromAddress(data.shippingBillingAddress || \"\");\n                        let displayName = getBaseName(data);\n                        const sameNameEntries = legrandData.filter((d)=>getBaseName(d) === displayName);\n                        if (sameNameEntries.length > 1) {\n                            if (location) {\n                                displayName = \"\".concat(displayName, \"_\").concat(location);\n                            } else if (data.zipPostal) {\n                                displayName = \"\".concat(displayName, \"_\").concat(data.zipPostal);\n                            }\n                        }\n                        return displayName === selectedDisplayName;\n                    });\n                    matchingEntries = sameDisplayNameEntries;\n                }\n            }\n        } else if (field === \"address\") {\n            matchingEntries = legrandData.filter((data)=>data.shippingBillingAddress === value);\n        } else if (field === \"zipcode\") {\n            matchingEntries = legrandData.filter((data)=>data.zipPostal === value);\n        }\n        return matchingEntries;\n    }, [\n        legrandData,\n        getBaseName,\n        getAliasOptions\n    ]);\n    const clearOtherBlocks = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((currentPrefix)=>{\n        const prefixes = [\n            \"shipper\",\n            \"consignee\",\n            \"billto\"\n        ];\n        const otherPrefixes = prefixes.filter((p)=>p !== currentPrefix);\n        otherPrefixes.forEach((prefix)=>{\n            const currentAlias = form.getValues(\"entries.\".concat(entryIndex, \".\").concat(prefix, \"Alias\"));\n            const currentAddress = form.getValues(\"entries.\".concat(entryIndex, \".\").concat(prefix, \"Address\"));\n            const currentZipcode = form.getValues(\"entries.\".concat(entryIndex, \".\").concat(prefix, \"Zipcode\"));\n            if (currentAlias || currentAddress || currentZipcode) {\n                form.setValue(\"entries.\".concat(entryIndex, \".\").concat(prefix, \"Alias\"), \"\");\n                form.setValue(\"entries.\".concat(entryIndex, \".\").concat(prefix, \"Address\"), \"\");\n                form.setValue(\"entries.\".concat(entryIndex, \".\").concat(prefix, \"Zipcode\"), \"\");\n            }\n        });\n    }, [\n        form,\n        entryIndex\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        if (!legrandData.length) return;\n        const subscription = form.watch((_, param)=>{\n            let { name, type } = param;\n            if (!name || !name.includes(\"entries.\".concat(entryIndex, \".\").concat(fieldPrefix)) || type !== \"change\") return;\n            const currentAlias = form.getValues(\"entries.\".concat(entryIndex, \".\").concat(fieldPrefix, \"Alias\"));\n            const currentAddress = form.getValues(\"entries.\".concat(entryIndex, \".\").concat(fieldPrefix, \"Address\"));\n            const currentZipcode = form.getValues(\"entries.\".concat(entryIndex, \".\").concat(fieldPrefix, \"Zipcode\"));\n            if (currentAlias || currentAddress || currentZipcode) {\n                clearOtherBlocks(fieldPrefix);\n            }\n            if (name === \"entries.\".concat(entryIndex, \".\").concat(fieldPrefix, \"Alias\") && currentAlias) {\n                const selectedData = legrandData.find((data)=>{\n                    const uniqueKey = \"\".concat(data.customeCode, \"-\").concat(data.aliasShippingNames || data.legalName, \"-\").concat(data.shippingBillingAddress);\n                    return uniqueKey === currentAlias;\n                });\n                if (selectedData) {\n                    const duplicateEntries = checkForDuplicates(\"alias\", currentAlias);\n                    if (duplicateEntries.length > 1) {\n                        if (currentAddress) {\n                            const addressMatchesAlias = duplicateEntries.some((entry)=>entry.shippingBillingAddress === currentAddress);\n                            if (addressMatchesAlias) {\n                                const specificEntry = duplicateEntries.find((entry)=>entry.shippingBillingAddress === currentAddress);\n                                if (specificEntry && currentZipcode !== specificEntry.zipPostal) {\n                                    form.setValue(\"entries.\".concat(entryIndex, \".\").concat(fieldPrefix, \"Zipcode\"), specificEntry.zipPostal);\n                                }\n                            } else {\n                                form.setValue(\"entries.\".concat(entryIndex, \".\").concat(fieldPrefix, \"Address\"), \"\");\n                                if (currentZipcode !== \"\") {\n                                    form.setValue(\"entries.\".concat(entryIndex, \".\").concat(fieldPrefix, \"Zipcode\"), \"\");\n                                }\n                            }\n                        } else {\n                            if (currentZipcode) {\n                                const zipcodeMatchesAlias = duplicateEntries.some((entry)=>entry.zipPostal === currentZipcode);\n                                if (zipcodeMatchesAlias) {} else {\n                                    form.setValue(\"entries.\".concat(entryIndex, \".\").concat(fieldPrefix, \"Zipcode\"), \"\");\n                                }\n                            } else {}\n                        }\n                    } else {\n                        if (currentAddress !== selectedData.shippingBillingAddress) {\n                            form.setValue(\"entries.\".concat(entryIndex, \".\").concat(fieldPrefix, \"Address\"), selectedData.shippingBillingAddress);\n                        }\n                        if (currentZipcode !== selectedData.zipPostal) {\n                            form.setValue(\"entries.\".concat(entryIndex, \".\").concat(fieldPrefix, \"Zipcode\"), selectedData.zipPostal);\n                        }\n                    }\n                    const currentCompany = form.getValues(\"entries.\".concat(entryIndex, \".company\"));\n                    if (currentCompany !== selectedData.businessUnit) {\n                        form.setValue(\"entries.\".concat(entryIndex, \".company\"), selectedData.businessUnit);\n                    }\n                    const allDivisions = [];\n                    duplicateEntries.forEach((entry)=>{\n                        if (entry.customeCode) {\n                            if (entry.customeCode.includes(\"/\")) {\n                                const splitDivisions = entry.customeCode.split(\"/\").map((d)=>d.trim());\n                                allDivisions.push(...splitDivisions);\n                            } else {\n                                allDivisions.push(entry.customeCode);\n                            }\n                        }\n                    });\n                    const uniqueDivisions = Array.from(new Set(allDivisions.filter((code)=>code)));\n                    if (uniqueDivisions.length === 1) {\n                        const currentDivision = form.getValues(\"entries.\".concat(entryIndex, \".division\"));\n                        if (currentDivision !== uniqueDivisions[0]) {\n                            form.setValue(\"entries.\".concat(entryIndex, \".division\"), uniqueDivisions[0]);\n                        }\n                    } else {\n                        const currentDivision = form.getValues(\"entries.\".concat(entryIndex, \".division\"));\n                        if (currentDivision !== \"\") {\n                            form.setValue(\"entries.\".concat(entryIndex, \".division\"), \"\");\n                        }\n                    }\n                    if (onLegrandDataChange) {\n                        const divisionCodeToPass = uniqueDivisions.length === 1 ? uniqueDivisions[0] : \"\";\n                        onLegrandDataChange(entryIndex, selectedData.businessUnit || \"\", divisionCodeToPass);\n                    }\n                }\n            }\n            if (name === \"entries.\".concat(entryIndex, \".\").concat(fieldPrefix, \"Address\") && currentAddress) {\n                const duplicateEntries = checkForDuplicates(\"address\", currentAddress);\n                if (duplicateEntries.length > 1) {\n                    if (currentAlias) {\n                        const selectedAliasData = legrandData.find((data)=>{\n                            const uniqueKey = \"\".concat(data.customeCode, \"-\").concat(data.aliasShippingNames || data.legalName, \"-\").concat(data.shippingBillingAddress);\n                            return uniqueKey === currentAlias;\n                        });\n                        if (selectedAliasData) {\n                            const baseName = getBaseName(selectedAliasData);\n                            const addressMatchesBaseName = legrandData.some((data)=>getBaseName(data) === baseName && data.shippingBillingAddress === currentAddress);\n                            if (addressMatchesBaseName) {\n                                const addressSpecificEntry = legrandData.find((data)=>getBaseName(data) === baseName && data.shippingBillingAddress === currentAddress);\n                                if (addressSpecificEntry) {\n                                    const newUniqueKey = \"\".concat(addressSpecificEntry.customeCode, \"-\").concat(addressSpecificEntry.aliasShippingNames || addressSpecificEntry.legalName, \"-\").concat(addressSpecificEntry.shippingBillingAddress);\n                                    if (currentAlias !== newUniqueKey) {\n                                        form.setValue(\"entries.\".concat(entryIndex, \".\").concat(fieldPrefix, \"Alias\"), newUniqueKey);\n                                    }\n                                    if (currentZipcode !== addressSpecificEntry.zipPostal) {\n                                        form.setValue(\"entries.\".concat(entryIndex, \".\").concat(fieldPrefix, \"Zipcode\"), addressSpecificEntry.zipPostal);\n                                    }\n                                    const currentCompany = form.getValues(\"entries.\".concat(entryIndex, \".company\"));\n                                    if (currentCompany !== addressSpecificEntry.businessUnit) {\n                                        form.setValue(\"entries.\".concat(entryIndex, \".company\"), addressSpecificEntry.businessUnit);\n                                    }\n                                    const currentDivision = form.getValues(\"entries.\".concat(entryIndex, \".division\"));\n                                    if (currentDivision !== addressSpecificEntry.customeCode) {\n                                        form.setValue(\"entries.\".concat(entryIndex, \".division\"), addressSpecificEntry.customeCode);\n                                    }\n                                    if (onLegrandDataChange) {\n                                        onLegrandDataChange(entryIndex, addressSpecificEntry.businessUnit || \"\", addressSpecificEntry.customeCode || \"\");\n                                    }\n                                    return;\n                                }\n                            } else {\n                                form.setValue(\"entries.\".concat(entryIndex, \".\").concat(fieldPrefix, \"Alias\"), \"\");\n                            }\n                        }\n                    }\n                    const uniqueZipcodes = Array.from(new Set(duplicateEntries.map((entry)=>entry.zipPostal).filter((zip)=>zip)));\n                    if (uniqueZipcodes.length === 1) {\n                        if (currentZipcode !== uniqueZipcodes[0]) {\n                            form.setValue(\"entries.\".concat(entryIndex, \".\").concat(fieldPrefix, \"Zipcode\"), uniqueZipcodes[0]);\n                        }\n                    } else {\n                        if (currentZipcode !== \"\") {\n                            form.setValue(\"entries.\".concat(entryIndex, \".\").concat(fieldPrefix, \"Zipcode\"), \"\");\n                        }\n                    }\n                } else {\n                    const selectedData = duplicateEntries[0];\n                    if (selectedData) {\n                        const uniqueKey = \"\".concat(selectedData.customeCode, \"-\").concat(selectedData.aliasShippingNames || selectedData.legalName, \"-\").concat(selectedData.shippingBillingAddress);\n                        if (currentAlias !== uniqueKey) {\n                            form.setValue(\"entries.\".concat(entryIndex, \".\").concat(fieldPrefix, \"Alias\"), uniqueKey);\n                        }\n                        if (currentZipcode !== selectedData.zipPostal) {\n                            form.setValue(\"entries.\".concat(entryIndex, \".\").concat(fieldPrefix, \"Zipcode\"), selectedData.zipPostal);\n                        }\n                        const currentCompany = form.getValues(\"entries.\".concat(entryIndex, \".company\"));\n                        if (currentCompany !== selectedData.businessUnit) {\n                            form.setValue(\"entries.\".concat(entryIndex, \".company\"), selectedData.businessUnit);\n                        }\n                        const currentDivision = form.getValues(\"entries.\".concat(entryIndex, \".division\"));\n                        if (currentDivision !== selectedData.customeCode) {\n                            form.setValue(\"entries.\".concat(entryIndex, \".division\"), selectedData.customeCode);\n                        }\n                        if (onLegrandDataChange) {\n                            onLegrandDataChange(entryIndex, selectedData.businessUnit || \"\", selectedData.customeCode || \"\");\n                        }\n                    }\n                }\n            }\n            if (name === \"entries.\".concat(entryIndex, \".\").concat(fieldPrefix, \"Zipcode\") && currentZipcode) {\n                const duplicateEntries = checkForDuplicates(\"zipcode\", currentZipcode);\n                if (duplicateEntries.length > 1) {\n                    if (currentAlias !== \"\") {\n                        form.setValue(\"entries.\".concat(entryIndex, \".\").concat(fieldPrefix, \"Alias\"), \"\");\n                    }\n                    if (currentAddress !== \"\") {\n                        form.setValue(\"entries.\".concat(entryIndex, \".\").concat(fieldPrefix, \"Address\"), \"\");\n                    }\n                } else {\n                    const selectedData = duplicateEntries[0];\n                    if (selectedData) {\n                        const uniqueKey = \"\".concat(selectedData.customeCode, \"-\").concat(selectedData.aliasShippingNames || selectedData.legalName, \"-\").concat(selectedData.shippingBillingAddress);\n                        if (currentAlias !== uniqueKey) {\n                            form.setValue(\"entries.\".concat(entryIndex, \".\").concat(fieldPrefix, \"Alias\"), uniqueKey);\n                        }\n                        if (currentAddress !== selectedData.shippingBillingAddress) {\n                            form.setValue(\"entries.\".concat(entryIndex, \".\").concat(fieldPrefix, \"Address\"), selectedData.shippingBillingAddress);\n                        }\n                        const currentCompany = form.getValues(\"entries.\".concat(entryIndex, \".company\"));\n                        if (currentCompany !== selectedData.businessUnit) {\n                            form.setValue(\"entries.\".concat(entryIndex, \".company\"), selectedData.businessUnit);\n                        }\n                        const currentDivision = form.getValues(\"entries.\".concat(entryIndex, \".division\"));\n                        if (currentDivision !== selectedData.customeCode) {\n                            form.setValue(\"entries.\".concat(entryIndex, \".division\"), selectedData.customeCode);\n                        }\n                        if (onLegrandDataChange) {\n                            onLegrandDataChange(entryIndex, selectedData.businessUnit || \"\", selectedData.customeCode || \"\");\n                        }\n                    }\n                }\n            }\n        });\n        return ()=>subscription.unsubscribe();\n    }, [\n        legrandData,\n        form,\n        entryIndex,\n        onLegrandDataChange,\n        checkForDuplicates,\n        getBaseName,\n        clearOtherBlocks,\n        fieldPrefix\n    ]);\n    const getAddressOptions = ()=>{\n        const currentAlias = form.getValues(\"entries.\".concat(entryIndex, \".\").concat(fieldPrefix, \"Alias\"));\n        const currentZipcode = form.getValues(\"entries.\".concat(entryIndex, \".\").concat(fieldPrefix, \"Zipcode\"));\n        if (currentAlias) {\n            const selectedAliasData = legrandData.find((data)=>{\n                const uniqueKey = \"\".concat(data.customeCode, \"-\").concat(data.aliasShippingNames || data.legalName, \"-\").concat(data.shippingBillingAddress);\n                return uniqueKey === currentAlias;\n            });\n            if (selectedAliasData) {\n                var _getAliasOptions_find;\n                const selectedDisplayName = ((_getAliasOptions_find = getAliasOptions().find((option)=>option.value === currentAlias)) === null || _getAliasOptions_find === void 0 ? void 0 : _getAliasOptions_find.displayName) || \"\";\n                const sameDisplayNameEntries = legrandData.filter((data)=>{\n                    const location = data.location || extractCityFromAddress(data.shippingBillingAddress || \"\");\n                    const zipcode = data.zipPostal || \"\";\n                    const legalName = data.legalName || \"Unknown Company\";\n                    const aliasName = data.aliasShippingNames && data.aliasShippingNames !== \"NONE\" ? data.aliasShippingNames : null;\n                    let displayName = \"\";\n                    if (aliasName) {\n                        displayName = \"\".concat(legalName, \" (\").concat(aliasName, \")\");\n                    } else {\n                        displayName = legalName;\n                    }\n                    const baseName = displayName;\n                    const sameNameEntries = legrandData.filter((d)=>getBaseName(d) === displayName);\n                    if (sameNameEntries.length > 1) {\n                        if (location) {\n                            displayName = \"\".concat(displayName, \"_\").concat(location);\n                        } else if (data.zipPostal) {\n                            displayName = \"\".concat(displayName, \"_\").concat(data.zipPostal);\n                        }\n                    }\n                    return displayName === selectedDisplayName;\n                });\n                if (sameDisplayNameEntries.length > 1) {\n                    return sameDisplayNameEntries.map((data)=>data.shippingBillingAddress).filter((address)=>address).filter((address, index, self)=>self.indexOf(address) === index).sort();\n                } else {\n                    return selectedAliasData.shippingBillingAddress ? [\n                        selectedAliasData.shippingBillingAddress\n                    ] : [];\n                }\n            }\n        }\n        if (currentZipcode) {\n            const duplicateEntries = checkForDuplicates(\"zipcode\", currentZipcode);\n            if (duplicateEntries.length > 1) {\n                return duplicateEntries.map((data)=>data.shippingBillingAddress).filter((address)=>address).filter((address, index, self)=>self.indexOf(address) === index).sort();\n            }\n        }\n        const uniqueAddresses = new Set();\n        legrandData.forEach((data)=>{\n            if (data.shippingBillingAddress) {\n                uniqueAddresses.add(data.shippingBillingAddress);\n            }\n        });\n        return Array.from(uniqueAddresses).sort();\n    };\n    const getZipcodeOptions = ()=>{\n        const currentAlias = form.getValues(\"entries.\".concat(entryIndex, \".\").concat(fieldPrefix, \"Alias\"));\n        const currentAddress = form.getValues(\"entries.\".concat(entryIndex, \".\").concat(fieldPrefix, \"Address\"));\n        if (currentAlias) {\n            const selectedAliasData = legrandData.find((data)=>{\n                const uniqueKey = \"\".concat(data.customeCode, \"-\").concat(data.aliasShippingNames || data.legalName, \"-\").concat(data.shippingBillingAddress);\n                return uniqueKey === currentAlias;\n            });\n            if (selectedAliasData) {\n                var _getAliasOptions_find;\n                const selectedDisplayName = ((_getAliasOptions_find = getAliasOptions().find((option)=>option.value === currentAlias)) === null || _getAliasOptions_find === void 0 ? void 0 : _getAliasOptions_find.displayName) || \"\";\n                const sameDisplayNameEntries = legrandData.filter((data)=>{\n                    const location = data.location || extractCityFromAddress(data.shippingBillingAddress || \"\");\n                    const zipcode = data.zipPostal || \"\";\n                    const legalName = data.legalName || \"Unknown Company\";\n                    const aliasName = data.aliasShippingNames && data.aliasShippingNames !== \"NONE\" ? data.aliasShippingNames : null;\n                    let displayName = \"\";\n                    if (aliasName) {\n                        displayName = \"\".concat(legalName, \" (\").concat(aliasName, \")\");\n                    } else {\n                        displayName = legalName;\n                    }\n                    const sameNameEntries = legrandData.filter((d)=>getBaseName(d) === displayName);\n                    if (sameNameEntries.length > 1) {\n                        if (location) {\n                            displayName = \"\".concat(displayName, \"_\").concat(location);\n                        } else if (data.zipPostal) {\n                            displayName = \"\".concat(displayName, \"_\").concat(data.zipPostal);\n                        }\n                    }\n                    return displayName === selectedDisplayName;\n                });\n                if (sameDisplayNameEntries.length > 1) {\n                    return sameDisplayNameEntries.map((data)=>data.zipPostal).filter((zipcode)=>zipcode).filter((zipcode, index, self)=>self.indexOf(zipcode) === index).sort();\n                } else {\n                    return selectedAliasData.zipPostal ? [\n                        selectedAliasData.zipPostal\n                    ] : [];\n                }\n            }\n        }\n        if (currentAddress) {\n            const duplicateEntries = checkForDuplicates(\"address\", currentAddress);\n            if (duplicateEntries.length > 1) {\n                return duplicateEntries.map((data)=>data.zipPostal).filter((zipcode)=>zipcode).filter((zipcode, index, self)=>self.indexOf(zipcode) === index).sort();\n            }\n        }\n        const uniqueZipcodes = new Set();\n        legrandData.forEach((data)=>{\n            if (data.zipPostal) {\n                uniqueZipcodes.add(data.zipPostal);\n            }\n        });\n        return Array.from(uniqueZipcodes).sort();\n    };\n    const getCurrentAliasOptions = ()=>{\n        const currentAddress = form.getValues(\"entries.\".concat(entryIndex, \".\").concat(fieldPrefix, \"Address\"));\n        const currentZipcode = form.getValues(\"entries.\".concat(entryIndex, \".\").concat(fieldPrefix, \"Zipcode\"));\n        if (currentAddress) {\n            const duplicateEntries = checkForDuplicates(\"address\", currentAddress);\n            if (duplicateEntries.length > 1) {\n                return getFilteredAliasOptions();\n            }\n        }\n        if (currentZipcode) {\n            const duplicateEntries = checkForDuplicates(\"zipcode\", currentZipcode);\n            if (duplicateEntries.length > 1) {\n                return getFilteredAliasOptions();\n            }\n        }\n        return getAliasOptions();\n    };\n    const getFilteredAliasOptions = ()=>{\n        const currentAddress = form.getValues(\"entries.\".concat(entryIndex, \".\").concat(fieldPrefix, \"Address\"));\n        const currentZipcode = form.getValues(\"entries.\".concat(entryIndex, \".\").concat(fieldPrefix, \"Zipcode\"));\n        let filteredData = legrandData;\n        if (currentAddress) {\n            const duplicateEntries = checkForDuplicates(\"address\", currentAddress);\n            if (duplicateEntries.length > 1) {\n                filteredData = duplicateEntries;\n            }\n        }\n        if (currentZipcode) {\n            const duplicateEntries = checkForDuplicates(\"zipcode\", currentZipcode);\n            if (duplicateEntries.length > 1) {\n                filteredData = filteredData.filter((data)=>duplicateEntries.some((dup)=>dup.id === data.id));\n            }\n        }\n        const options = [];\n        filteredData.forEach((data)=>{\n            const location = data.location || extractCityFromAddress(data.shippingBillingAddress || \"\");\n            const zipcode = data.zipPostal || \"\";\n            let displayName = \"\";\n            let type = \"company\";\n            const legalName = data.legalName || \"Unknown Company\";\n            const aliasName = data.aliasShippingNames && data.aliasShippingNames !== \"NONE\" ? data.aliasShippingNames : null;\n            if (aliasName) {\n                displayName = \"\".concat(legalName, \" (\").concat(aliasName, \")\");\n                type = \"alias\";\n            } else {\n                displayName = legalName;\n                type = \"company\";\n            }\n            const uniqueKey = \"\".concat(data.customeCode, \"-\").concat(data.aliasShippingNames || data.legalName, \"-\").concat(data.shippingBillingAddress);\n            const sameNameEntries = filteredData.filter((d)=>{\n                const dLegalName = d.legalName || \"Unknown Company\";\n                const dAliasName = d.aliasShippingNames && d.aliasShippingNames !== \"NONE\" ? d.aliasShippingNames : null;\n                let dDisplayName = \"\";\n                if (dAliasName) {\n                    dDisplayName = \"\".concat(dLegalName, \" (\").concat(dAliasName, \")\");\n                } else {\n                    dDisplayName = dLegalName;\n                }\n                return dDisplayName === displayName;\n            });\n            let finalDisplayName = displayName;\n            if (sameNameEntries.length > 1) {\n                if (location) {\n                    finalDisplayName = \"\".concat(displayName, \"_\").concat(location);\n                } else if (zipcode) {\n                    finalDisplayName = \"\".concat(displayName, \"_\").concat(zipcode);\n                }\n            }\n            options.push({\n                value: uniqueKey,\n                label: finalDisplayName,\n                type: type,\n                displayName: finalDisplayName\n            });\n        });\n        const uniqueOptions = options.filter((option, index, self)=>index === self.findIndex((o)=>o.value === option.value));\n        return uniqueOptions.sort((a, b)=>a.label.localeCompare(b.label));\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-gray-50 rounded-lg p-3 max-w-sm mb-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-2 mb-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MapPin_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            className: \"w-4 h-4 text-purple-600\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\LegrandDetailsComponent.tsx\",\n                            lineNumber: 708,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-sm font-semibold text-gray-900\",\n                            children: \"LEGRAND Details\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\LegrandDetailsComponent.tsx\",\n                            lineNumber: 709,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\LegrandDetailsComponent.tsx\",\n                    lineNumber: 707,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-sm text-gray-500\",\n                    children: \"Loading LEGRAND data...\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\LegrandDetailsComponent.tsx\",\n                    lineNumber: 711,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\LegrandDetailsComponent.tsx\",\n            lineNumber: 706,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-gray-50 rounded-lg p-3 mb-3 max-w-lg\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-2 mb-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MapPin_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        className: \"w-4 h-4 text-purple-600\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\LegrandDetailsComponent.tsx\",\n                        lineNumber: 719,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-sm font-semibold text-gray-900\",\n                        children: blockTitle\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\LegrandDetailsComponent.tsx\",\n                        lineNumber: 720,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\LegrandDetailsComponent.tsx\",\n                lineNumber: 718,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 gap-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"[&_div[class*='flex'][class*='items-center']]:!h-10 [&_input]:!text-sm [&_label]:!text-sm\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_SearchSelect__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            form: form,\n                            name: \"entries.\".concat(entryIndex, \".\").concat(fieldPrefix, \"Alias\"),\n                            label: \"Alias/Company\",\n                            placeholder: \"Select Alias or Company\",\n                            isEntryPage: true,\n                            className: \"text-sm\",\n                            options: getCurrentAliasOptions().map((option)=>({\n                                    value: option.value,\n                                    label: option.displayName,\n                                    badge: option.type === \"alias\" ? \"Alias\" : \"Company\",\n                                    badgeColor: option.type === \"alias\" ? \"bg-blue-500\" : \"bg-green-500\"\n                                }))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\LegrandDetailsComponent.tsx\",\n                            lineNumber: 727,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\LegrandDetailsComponent.tsx\",\n                        lineNumber: 726,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"[&_div[class*='flex'][class*='items-center']]:!h-10 [&_input]:!text-sm [&_label]:!text-sm\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_SearchSelect__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            form: form,\n                            name: \"entries.\".concat(entryIndex, \".\").concat(fieldPrefix, \"Address\"),\n                            label: \"Address\",\n                            placeholder: \"Select Address\",\n                            isEntryPage: true,\n                            className: \"text-sm\",\n                            options: getAddressOptions().map((address)=>({\n                                    value: address,\n                                    label: address\n                                }))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\LegrandDetailsComponent.tsx\",\n                            lineNumber: 745,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\LegrandDetailsComponent.tsx\",\n                        lineNumber: 744,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"[&_div[class*='flex'][class*='items-center']]:!h-10 [&_input]:!text-sm [&_label]:!text-sm\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_SearchSelect__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            form: form,\n                            name: \"entries.\".concat(entryIndex, \".\").concat(fieldPrefix, \"Zipcode\"),\n                            label: \"Zipcode\",\n                            placeholder: \"Select Zipcode\",\n                            isEntryPage: true,\n                            className: \"text-sm\",\n                            options: getZipcodeOptions().map((zipcode)=>({\n                                    value: zipcode,\n                                    label: zipcode\n                                }))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\LegrandDetailsComponent.tsx\",\n                            lineNumber: 761,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\LegrandDetailsComponent.tsx\",\n                        lineNumber: 760,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\LegrandDetailsComponent.tsx\",\n                lineNumber: 724,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\LegrandDetailsComponent.tsx\",\n        lineNumber: 717,\n        columnNumber: 5\n    }, undefined);\n};\n_s(LegrandDetailsComponent, \"DglJNglAgNnw2Uhf60hDciqeA3k=\");\n_c = LegrandDetailsComponent;\n/* harmony default export */ __webpack_exports__[\"default\"] = (LegrandDetailsComponent);\nvar _c;\n$RefreshReg$(_c, \"LegrandDetailsComponent\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/user/trackSheets/LegrandDetailsComponent.tsx\n"));

/***/ })

});