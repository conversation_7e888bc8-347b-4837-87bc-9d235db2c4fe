"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/user/trackSheets/page",{

/***/ "(app-pages-browser)/./app/user/trackSheets/LegrandDetailsComponent.tsx":
/*!**********************************************************!*\
  !*** ./app/user/trackSheets/LegrandDetailsComponent.tsx ***!
  \**********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _app_component_SearchSelect__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/app/_component/SearchSelect */ \"(app-pages-browser)/./app/_component/SearchSelect.tsx\");\n/* harmony import */ var _barrel_optimize_names_MapPin_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=MapPin!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _lib_routePath__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/routePath */ \"(app-pages-browser)/./lib/routePath.ts\");\n/* harmony import */ var _lib_helpers__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/helpers */ \"(app-pages-browser)/./lib/helpers.ts\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react-hot-toast */ \"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst LegrandDetailsComponent = (param)=>{\n    let { form, entryIndex, onLegrandDataChange, blockTitle = \"LEGRAND Details\", fieldPrefix = \"legrand\" } = param;\n    _s();\n    const [legrandData, setLegrandData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const fetchLegrandData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async ()=>{\n        try {\n            setLoading(true);\n            const response = await (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_4__.getAllData)(_lib_routePath__WEBPACK_IMPORTED_MODULE_3__.legrandMapping_routes.GET_LEGRAND_MAPPINGS);\n            setLegrandData(response || []);\n        } catch (error) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_5__[\"default\"].error(\"Error fetching LEGRAND mapping data:\", error);\n            setLegrandData([]);\n        } finally{\n            setLoading(false);\n        }\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        fetchLegrandData();\n    }, [\n        fetchLegrandData\n    ]);\n    const extractCityFromAddress = (address)=>{\n        if (!address) return \"\";\n        const parts = address.split(\",\");\n        if (parts.length >= 2) {\n            const cityPart = parts[parts.length - 2].trim();\n            const city = cityPart.replace(/\\d+/g, \"\").trim();\n            return city;\n        }\n        const words = address.split(\" \");\n        for (const word of words){\n            if (word.length > 2 && !/^\\d+$/.test(word) && !word.includes(\"ROAD\") && !word.includes(\"STREET\") && !word.includes(\"AVE\")) {\n                return word;\n            }\n        }\n        return \"\";\n    };\n    const getAliasOptions = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        const options = [];\n        legrandData.forEach((data)=>{\n            const location = data.location || extractCityFromAddress(data.shippingBillingAddress || \"\");\n            const zipcode = data.zipPostal || \"\";\n            let displayName = \"\";\n            let type = \"company\";\n            const legalName = data.legalName || \"Unknown Company\";\n            const aliasName = data.aliasShippingNames && data.aliasShippingNames !== \"NONE\" ? data.aliasShippingNames : null;\n            if (aliasName) {\n                displayName = \"\".concat(legalName, \" (\").concat(aliasName, \")\");\n                type = \"alias\";\n            } else {\n                displayName = legalName;\n                type = \"company\";\n            }\n            const uniqueKey = \"\".concat(data.customeCode, \"-\").concat(data.aliasShippingNames || data.legalName, \"-\").concat(data.shippingBillingAddress);\n            const sameNameEntries = legrandData.filter((d)=>{\n                const dLegalName = d.legalName || \"Unknown Company\";\n                const dAliasName = d.aliasShippingNames && d.aliasShippingNames !== \"NONE\" ? d.aliasShippingNames : null;\n                let dDisplayName = \"\";\n                if (dAliasName) {\n                    dDisplayName = \"\".concat(dLegalName, \" (\").concat(dAliasName, \")\");\n                } else {\n                    dDisplayName = dLegalName;\n                }\n                return dDisplayName === displayName;\n            });\n            let finalDisplayName = displayName;\n            if (sameNameEntries.length > 1) {\n                if (location) {\n                    finalDisplayName = \"\".concat(displayName, \"_\").concat(location);\n                } else if (zipcode) {\n                    finalDisplayName = \"\".concat(displayName, \"_\").concat(zipcode);\n                }\n            }\n            options.push({\n                value: uniqueKey,\n                label: finalDisplayName,\n                type: type,\n                displayName: finalDisplayName\n            });\n        });\n        const uniqueOptions = options.filter((option, index, self)=>index === self.findIndex((o)=>o.value === option.value));\n        return uniqueOptions.sort((a, b)=>a.label.localeCompare(b.label));\n    }, [\n        legrandData\n    ]);\n    const getBaseName = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((data)=>{\n        const legalName = data.legalName || \"Unknown Company\";\n        const aliasName = data.aliasShippingNames && data.aliasShippingNames !== \"NONE\" ? data.aliasShippingNames : null;\n        if (aliasName) {\n            return \"\".concat(legalName, \" (\").concat(aliasName, \")\");\n        } else {\n            return legalName;\n        }\n    }, []);\n    const checkForDuplicates = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((field, value)=>{\n        let matchingEntries = [];\n        if (field === \"alias\") {\n            const exactMatch = legrandData.find((data)=>{\n                const uniqueKey = \"\".concat(data.customeCode, \"-\").concat(data.aliasShippingNames || data.legalName, \"-\").concat(data.shippingBillingAddress);\n                return uniqueKey === value;\n            });\n            if (exactMatch) {\n                const baseName = getBaseName(exactMatch);\n                const sameBaseNameEntries = legrandData.filter((data)=>getBaseName(data) === baseName);\n                if (sameBaseNameEntries.length === 1) {\n                    matchingEntries = [\n                        exactMatch\n                    ];\n                } else {\n                    var _getAliasOptions_find;\n                    const selectedDisplayName = ((_getAliasOptions_find = getAliasOptions().find((option)=>option.value === value)) === null || _getAliasOptions_find === void 0 ? void 0 : _getAliasOptions_find.displayName) || \"\";\n                    const sameDisplayNameEntries = legrandData.filter((data)=>{\n                        const location = data.location || extractCityFromAddress(data.shippingBillingAddress || \"\");\n                        let displayName = getBaseName(data);\n                        const sameNameEntries = legrandData.filter((d)=>getBaseName(d) === displayName);\n                        if (sameNameEntries.length > 1) {\n                            if (location) {\n                                displayName = \"\".concat(displayName, \"_\").concat(location);\n                            } else if (data.zipPostal) {\n                                displayName = \"\".concat(displayName, \"_\").concat(data.zipPostal);\n                            }\n                        }\n                        return displayName === selectedDisplayName;\n                    });\n                    matchingEntries = sameDisplayNameEntries;\n                }\n            }\n        } else if (field === \"address\") {\n            matchingEntries = legrandData.filter((data)=>data.shippingBillingAddress === value);\n        } else if (field === \"zipcode\") {\n            matchingEntries = legrandData.filter((data)=>data.zipPostal === value);\n        }\n        return matchingEntries;\n    }, [\n        legrandData,\n        getBaseName,\n        getAliasOptions\n    ]);\n    const clearOtherBlocks = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((currentPrefix)=>{\n        const prefixes = [\n            \"shipper\",\n            \"consignee\",\n            \"billto\"\n        ];\n        const otherPrefixes = prefixes.filter((p)=>p !== currentPrefix);\n        otherPrefixes.forEach((prefix)=>{\n            const currentAlias = form.getValues(\"entries.\".concat(entryIndex, \".\").concat(prefix, \"Alias\"));\n            const currentAddress = form.getValues(\"entries.\".concat(entryIndex, \".\").concat(prefix, \"Address\"));\n            const currentZipcode = form.getValues(\"entries.\".concat(entryIndex, \".\").concat(prefix, \"Zipcode\"));\n            if (currentAlias || currentAddress || currentZipcode) {\n                form.setValue(\"entries.\".concat(entryIndex, \".\").concat(prefix, \"Alias\"), \"\");\n                form.setValue(\"entries.\".concat(entryIndex, \".\").concat(prefix, \"Address\"), \"\");\n                form.setValue(\"entries.\".concat(entryIndex, \".\").concat(prefix, \"Zipcode\"), \"\");\n            }\n        });\n    }, [\n        form,\n        entryIndex\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        if (!legrandData.length) return;\n        const subscription = form.watch((_, param)=>{\n            let { name, type } = param;\n            if (!name || !name.includes(\"entries.\".concat(entryIndex, \".\").concat(fieldPrefix)) || type !== \"change\") return;\n            const currentAlias = form.getValues(\"entries.\".concat(entryIndex, \".\").concat(fieldPrefix, \"Alias\"));\n            const currentAddress = form.getValues(\"entries.\".concat(entryIndex, \".\").concat(fieldPrefix, \"Address\"));\n            const currentZipcode = form.getValues(\"entries.\".concat(entryIndex, \".\").concat(fieldPrefix, \"Zipcode\"));\n            if (currentAlias || currentAddress || currentZipcode) {\n                clearOtherBlocks(fieldPrefix);\n            }\n            if (name === \"entries.\".concat(entryIndex, \".\").concat(fieldPrefix, \"Alias\") && currentAlias) {\n                const selectedData = legrandData.find((data)=>{\n                    const uniqueKey = \"\".concat(data.customeCode, \"-\").concat(data.aliasShippingNames || data.legalName, \"-\").concat(data.shippingBillingAddress);\n                    return uniqueKey === currentAlias;\n                });\n                if (selectedData) {\n                    const duplicateEntries = checkForDuplicates(\"alias\", currentAlias);\n                    if (duplicateEntries.length > 1) {\n                        if (currentAddress) {\n                            const addressMatchesAlias = duplicateEntries.some((entry)=>entry.shippingBillingAddress === currentAddress);\n                            if (addressMatchesAlias) {\n                                const specificEntry = duplicateEntries.find((entry)=>entry.shippingBillingAddress === currentAddress);\n                                if (specificEntry && currentZipcode !== specificEntry.zipPostal) {\n                                    form.setValue(\"entries.\".concat(entryIndex, \".\").concat(fieldPrefix, \"Zipcode\"), specificEntry.zipPostal);\n                                }\n                            } else {\n                                form.setValue(\"entries.\".concat(entryIndex, \".\").concat(fieldPrefix, \"Address\"), \"\");\n                                if (currentZipcode !== \"\") {\n                                    form.setValue(\"entries.\".concat(entryIndex, \".\").concat(fieldPrefix, \"Zipcode\"), \"\");\n                                }\n                            }\n                        } else {\n                            if (currentZipcode) {\n                                const zipcodeMatchesAlias = duplicateEntries.some((entry)=>entry.zipPostal === currentZipcode);\n                                if (zipcodeMatchesAlias) {} else {\n                                    form.setValue(\"entries.\".concat(entryIndex, \".\").concat(fieldPrefix, \"Zipcode\"), \"\");\n                                }\n                            } else {}\n                        }\n                    } else {\n                        if (currentAddress !== selectedData.shippingBillingAddress) {\n                            form.setValue(\"entries.\".concat(entryIndex, \".\").concat(fieldPrefix, \"Address\"), selectedData.shippingBillingAddress);\n                        }\n                        if (currentZipcode !== selectedData.zipPostal) {\n                            form.setValue(\"entries.\".concat(entryIndex, \".\").concat(fieldPrefix, \"Zipcode\"), selectedData.zipPostal);\n                        }\n                    }\n                    const currentCompany = form.getValues(\"entries.\".concat(entryIndex, \".company\"));\n                    if (currentCompany !== selectedData.businessUnit) {\n                        form.setValue(\"entries.\".concat(entryIndex, \".company\"), selectedData.businessUnit);\n                    }\n                    const allDivisions = [];\n                    duplicateEntries.forEach((entry)=>{\n                        if (entry.customeCode) {\n                            if (entry.customeCode.includes(\"/\")) {\n                                const splitDivisions = entry.customeCode.split(\"/\").map((d)=>d.trim());\n                                allDivisions.push(...splitDivisions);\n                            } else {\n                                allDivisions.push(entry.customeCode);\n                            }\n                        }\n                    });\n                    const uniqueDivisions = Array.from(new Set(allDivisions.filter((code)=>code)));\n                    if (uniqueDivisions.length === 1) {\n                        const currentDivision = form.getValues(\"entries.\".concat(entryIndex, \".division\"));\n                        if (currentDivision !== uniqueDivisions[0]) {\n                            form.setValue(\"entries.\".concat(entryIndex, \".division\"), uniqueDivisions[0]);\n                        }\n                    } else {\n                        const currentDivision = form.getValues(\"entries.\".concat(entryIndex, \".division\"));\n                        if (currentDivision !== \"\") {\n                            form.setValue(\"entries.\".concat(entryIndex, \".division\"), \"\");\n                        }\n                    }\n                    if (onLegrandDataChange) {\n                        const divisionCodeToPass = uniqueDivisions.length === 1 ? uniqueDivisions[0] : \"\";\n                        onLegrandDataChange(entryIndex, selectedData.businessUnit || \"\", divisionCodeToPass);\n                    }\n                }\n            }\n            if (name === \"entries.\".concat(entryIndex, \".\").concat(fieldPrefix, \"Address\") && currentAddress) {\n                const duplicateEntries = checkForDuplicates(\"address\", currentAddress);\n                if (duplicateEntries.length > 1) {\n                    if (currentAlias) {\n                        const selectedAliasData = legrandData.find((data)=>{\n                            const uniqueKey = \"\".concat(data.customeCode, \"-\").concat(data.aliasShippingNames || data.legalName, \"-\").concat(data.shippingBillingAddress);\n                            return uniqueKey === currentAlias;\n                        });\n                        if (selectedAliasData) {\n                            const baseName = getBaseName(selectedAliasData);\n                            const addressMatchesBaseName = legrandData.some((data)=>getBaseName(data) === baseName && data.shippingBillingAddress === currentAddress);\n                            if (addressMatchesBaseName) {\n                                const addressSpecificEntry = legrandData.find((data)=>getBaseName(data) === baseName && data.shippingBillingAddress === currentAddress);\n                                if (addressSpecificEntry) {\n                                    const newUniqueKey = \"\".concat(addressSpecificEntry.customeCode, \"-\").concat(addressSpecificEntry.aliasShippingNames || addressSpecificEntry.legalName, \"-\").concat(addressSpecificEntry.shippingBillingAddress);\n                                    if (currentAlias !== newUniqueKey) {\n                                        form.setValue(\"entries.\".concat(entryIndex, \".\").concat(fieldPrefix, \"Alias\"), newUniqueKey);\n                                    }\n                                    if (currentZipcode !== addressSpecificEntry.zipPostal) {\n                                        form.setValue(\"entries.\".concat(entryIndex, \".\").concat(fieldPrefix, \"Zipcode\"), addressSpecificEntry.zipPostal);\n                                    }\n                                    const currentCompany = form.getValues(\"entries.\".concat(entryIndex, \".company\"));\n                                    if (currentCompany !== addressSpecificEntry.businessUnit) {\n                                        form.setValue(\"entries.\".concat(entryIndex, \".company\"), addressSpecificEntry.businessUnit);\n                                    }\n                                    const currentDivision = form.getValues(\"entries.\".concat(entryIndex, \".division\"));\n                                    if (currentDivision !== addressSpecificEntry.customeCode) {\n                                        form.setValue(\"entries.\".concat(entryIndex, \".division\"), addressSpecificEntry.customeCode);\n                                    }\n                                    if (onLegrandDataChange) {\n                                        onLegrandDataChange(entryIndex, addressSpecificEntry.businessUnit || \"\", addressSpecificEntry.customeCode || \"\");\n                                    }\n                                    return;\n                                }\n                            } else {\n                                form.setValue(\"entries.\".concat(entryIndex, \".\").concat(fieldPrefix, \"Alias\"), \"\");\n                            }\n                        }\n                    }\n                    const uniqueZipcodes = Array.from(new Set(duplicateEntries.map((entry)=>entry.zipPostal).filter((zip)=>zip)));\n                    if (uniqueZipcodes.length === 1) {\n                        if (currentZipcode !== uniqueZipcodes[0]) {\n                            form.setValue(\"entries.\".concat(entryIndex, \".\").concat(fieldPrefix, \"Zipcode\"), uniqueZipcodes[0]);\n                        }\n                    } else {\n                        if (currentZipcode !== \"\") {\n                            form.setValue(\"entries.\".concat(entryIndex, \".\").concat(fieldPrefix, \"Zipcode\"), \"\");\n                        }\n                    }\n                } else {\n                    const selectedData = duplicateEntries[0];\n                    if (selectedData) {\n                        const uniqueKey = \"\".concat(selectedData.customeCode, \"-\").concat(selectedData.aliasShippingNames || selectedData.legalName, \"-\").concat(selectedData.shippingBillingAddress);\n                        if (currentAlias !== uniqueKey) {\n                            form.setValue(\"entries.\".concat(entryIndex, \".\").concat(fieldPrefix, \"Alias\"), uniqueKey);\n                        }\n                        if (currentZipcode !== selectedData.zipPostal) {\n                            form.setValue(\"entries.\".concat(entryIndex, \".\").concat(fieldPrefix, \"Zipcode\"), selectedData.zipPostal);\n                        }\n                        const currentCompany = form.getValues(\"entries.\".concat(entryIndex, \".company\"));\n                        if (currentCompany !== selectedData.businessUnit) {\n                            form.setValue(\"entries.\".concat(entryIndex, \".company\"), selectedData.businessUnit);\n                        }\n                        const currentDivision = form.getValues(\"entries.\".concat(entryIndex, \".division\"));\n                        if (currentDivision !== selectedData.customeCode) {\n                            form.setValue(\"entries.\".concat(entryIndex, \".division\"), selectedData.customeCode);\n                        }\n                        if (onLegrandDataChange) {\n                            onLegrandDataChange(entryIndex, selectedData.businessUnit || \"\", selectedData.customeCode || \"\");\n                        }\n                    }\n                }\n            }\n            if (name === \"entries.\".concat(entryIndex, \".\").concat(fieldPrefix, \"Zipcode\") && currentZipcode) {\n                const duplicateEntries = checkForDuplicates(\"zipcode\", currentZipcode);\n                if (duplicateEntries.length > 1) {\n                    if (currentAlias !== \"\") {\n                        form.setValue(\"entries.\".concat(entryIndex, \".\").concat(fieldPrefix, \"Alias\"), \"\");\n                    }\n                    if (currentAddress !== \"\") {\n                        form.setValue(\"entries.\".concat(entryIndex, \".\").concat(fieldPrefix, \"Address\"), \"\");\n                    }\n                } else {\n                    const selectedData = duplicateEntries[0];\n                    if (selectedData) {\n                        const uniqueKey = \"\".concat(selectedData.customeCode, \"-\").concat(selectedData.aliasShippingNames || selectedData.legalName, \"-\").concat(selectedData.shippingBillingAddress);\n                        if (currentAlias !== uniqueKey) {\n                            form.setValue(\"entries.\".concat(entryIndex, \".\").concat(fieldPrefix, \"Alias\"), uniqueKey);\n                        }\n                        if (currentAddress !== selectedData.shippingBillingAddress) {\n                            form.setValue(\"entries.\".concat(entryIndex, \".\").concat(fieldPrefix, \"Address\"), selectedData.shippingBillingAddress);\n                        }\n                        const currentCompany = form.getValues(\"entries.\".concat(entryIndex, \".company\"));\n                        if (currentCompany !== selectedData.businessUnit) {\n                            form.setValue(\"entries.\".concat(entryIndex, \".company\"), selectedData.businessUnit);\n                        }\n                        const currentDivision = form.getValues(\"entries.\".concat(entryIndex, \".division\"));\n                        if (currentDivision !== selectedData.customeCode) {\n                            form.setValue(\"entries.\".concat(entryIndex, \".division\"), selectedData.customeCode);\n                        }\n                        if (onLegrandDataChange) {\n                            onLegrandDataChange(entryIndex, selectedData.businessUnit || \"\", selectedData.customeCode || \"\");\n                        }\n                    }\n                }\n            }\n        });\n        return ()=>subscription.unsubscribe();\n    }, [\n        legrandData,\n        form,\n        entryIndex,\n        onLegrandDataChange,\n        checkForDuplicates,\n        getBaseName,\n        clearOtherBlocks,\n        fieldPrefix\n    ]);\n    const getAddressOptions = ()=>{\n        const currentAlias = form.getValues(\"entries.\".concat(entryIndex, \".\").concat(fieldPrefix, \"Alias\"));\n        const currentZipcode = form.getValues(\"entries.\".concat(entryIndex, \".\").concat(fieldPrefix, \"Zipcode\"));\n        if (currentAlias) {\n            const selectedAliasData = legrandData.find((data)=>{\n                const uniqueKey = \"\".concat(data.customeCode, \"-\").concat(data.aliasShippingNames || data.legalName, \"-\").concat(data.shippingBillingAddress);\n                return uniqueKey === currentAlias;\n            });\n            if (selectedAliasData) {\n                var _getAliasOptions_find;\n                const selectedDisplayName = ((_getAliasOptions_find = getAliasOptions().find((option)=>option.value === currentAlias)) === null || _getAliasOptions_find === void 0 ? void 0 : _getAliasOptions_find.displayName) || \"\";\n                const sameDisplayNameEntries = legrandData.filter((data)=>{\n                    const location = data.location || extractCityFromAddress(data.shippingBillingAddress || \"\");\n                    const zipcode = data.zipPostal || \"\";\n                    const legalName = data.legalName || \"Unknown Company\";\n                    const aliasName = data.aliasShippingNames && data.aliasShippingNames !== \"NONE\" ? data.aliasShippingNames : null;\n                    let displayName = \"\";\n                    if (aliasName) {\n                        displayName = \"\".concat(legalName, \" (\").concat(aliasName, \")\");\n                    } else {\n                        displayName = legalName;\n                    }\n                    const baseName = displayName;\n                    const sameNameEntries = legrandData.filter((d)=>getBaseName(d) === displayName);\n                    if (sameNameEntries.length > 1) {\n                        if (location) {\n                            displayName = \"\".concat(displayName, \"_\").concat(location);\n                        } else if (data.zipPostal) {\n                            displayName = \"\".concat(displayName, \"_\").concat(data.zipPostal);\n                        }\n                    }\n                    return displayName === selectedDisplayName;\n                });\n                if (sameDisplayNameEntries.length > 1) {\n                    return sameDisplayNameEntries.map((data)=>data.shippingBillingAddress).filter((address)=>address).filter((address, index, self)=>self.indexOf(address) === index).sort();\n                } else {\n                    return selectedAliasData.shippingBillingAddress ? [\n                        selectedAliasData.shippingBillingAddress\n                    ] : [];\n                }\n            }\n        }\n        if (currentZipcode) {\n            const duplicateEntries = checkForDuplicates(\"zipcode\", currentZipcode);\n            if (duplicateEntries.length > 1) {\n                return duplicateEntries.map((data)=>data.shippingBillingAddress).filter((address)=>address).filter((address, index, self)=>self.indexOf(address) === index).sort();\n            }\n        }\n        const uniqueAddresses = new Set();\n        legrandData.forEach((data)=>{\n            if (data.shippingBillingAddress) {\n                uniqueAddresses.add(data.shippingBillingAddress);\n            }\n        });\n        return Array.from(uniqueAddresses).sort();\n    };\n    const getZipcodeOptions = ()=>{\n        const currentAlias = form.getValues(\"entries.\".concat(entryIndex, \".\").concat(fieldPrefix, \"Alias\"));\n        const currentAddress = form.getValues(\"entries.\".concat(entryIndex, \".\").concat(fieldPrefix, \"Address\"));\n        if (currentAlias) {\n            const selectedAliasData = legrandData.find((data)=>{\n                const uniqueKey = \"\".concat(data.customeCode, \"-\").concat(data.aliasShippingNames || data.legalName, \"-\").concat(data.shippingBillingAddress);\n                return uniqueKey === currentAlias;\n            });\n            if (selectedAliasData) {\n                var _getAliasOptions_find;\n                const selectedDisplayName = ((_getAliasOptions_find = getAliasOptions().find((option)=>option.value === currentAlias)) === null || _getAliasOptions_find === void 0 ? void 0 : _getAliasOptions_find.displayName) || \"\";\n                const sameDisplayNameEntries = legrandData.filter((data)=>{\n                    const location = data.location || extractCityFromAddress(data.shippingBillingAddress || \"\");\n                    const zipcode = data.zipPostal || \"\";\n                    const legalName = data.legalName || \"Unknown Company\";\n                    const aliasName = data.aliasShippingNames && data.aliasShippingNames !== \"NONE\" ? data.aliasShippingNames : null;\n                    let displayName = \"\";\n                    if (aliasName) {\n                        displayName = \"\".concat(legalName, \" (\").concat(aliasName, \")\");\n                    } else {\n                        displayName = legalName;\n                    }\n                    const sameNameEntries = legrandData.filter((d)=>getBaseName(d) === displayName);\n                    if (sameNameEntries.length > 1) {\n                        if (location) {\n                            displayName = \"\".concat(displayName, \"_\").concat(location);\n                        } else if (data.zipPostal) {\n                            displayName = \"\".concat(displayName, \"_\").concat(data.zipPostal);\n                        }\n                    }\n                    return displayName === selectedDisplayName;\n                });\n                if (sameDisplayNameEntries.length > 1) {\n                    return sameDisplayNameEntries.map((data)=>data.zipPostal).filter((zipcode)=>zipcode).filter((zipcode, index, self)=>self.indexOf(zipcode) === index).sort();\n                } else {\n                    return selectedAliasData.zipPostal ? [\n                        selectedAliasData.zipPostal\n                    ] : [];\n                }\n            }\n        }\n        if (currentAddress) {\n            const duplicateEntries = checkForDuplicates(\"address\", currentAddress);\n            if (duplicateEntries.length > 1) {\n                return duplicateEntries.map((data)=>data.zipPostal).filter((zipcode)=>zipcode).filter((zipcode, index, self)=>self.indexOf(zipcode) === index).sort();\n            }\n        }\n        const uniqueZipcodes = new Set();\n        legrandData.forEach((data)=>{\n            if (data.zipPostal) {\n                uniqueZipcodes.add(data.zipPostal);\n            }\n        });\n        return Array.from(uniqueZipcodes).sort();\n    };\n    const getCurrentAliasOptions = ()=>{\n        const currentAddress = form.getValues(\"entries.\".concat(entryIndex, \".\").concat(fieldPrefix, \"Address\"));\n        const currentZipcode = form.getValues(\"entries.\".concat(entryIndex, \".\").concat(fieldPrefix, \"Zipcode\"));\n        if (currentAddress) {\n            const duplicateEntries = checkForDuplicates(\"address\", currentAddress);\n            if (duplicateEntries.length > 1) {\n                return getFilteredAliasOptions();\n            }\n        }\n        if (currentZipcode) {\n            const duplicateEntries = checkForDuplicates(\"zipcode\", currentZipcode);\n            if (duplicateEntries.length > 1) {\n                return getFilteredAliasOptions();\n            }\n        }\n        return getAliasOptions();\n    };\n    const getFilteredAliasOptions = ()=>{\n        const currentAddress = form.getValues(\"entries.\".concat(entryIndex, \".\").concat(fieldPrefix, \"Address\"));\n        const currentZipcode = form.getValues(\"entries.\".concat(entryIndex, \".\").concat(fieldPrefix, \"Zipcode\"));\n        let filteredData = legrandData;\n        if (currentAddress) {\n            const duplicateEntries = checkForDuplicates(\"address\", currentAddress);\n            if (duplicateEntries.length > 1) {\n                filteredData = duplicateEntries;\n            }\n        }\n        if (currentZipcode) {\n            const duplicateEntries = checkForDuplicates(\"zipcode\", currentZipcode);\n            if (duplicateEntries.length > 1) {\n                filteredData = filteredData.filter((data)=>duplicateEntries.some((dup)=>dup.id === data.id));\n            }\n        }\n        const options = [];\n        filteredData.forEach((data)=>{\n            const location = data.location || extractCityFromAddress(data.shippingBillingAddress || \"\");\n            const zipcode = data.zipPostal || \"\";\n            let displayName = \"\";\n            let type = \"company\";\n            const legalName = data.legalName || \"Unknown Company\";\n            const aliasName = data.aliasShippingNames && data.aliasShippingNames !== \"NONE\" ? data.aliasShippingNames : null;\n            if (aliasName) {\n                displayName = \"\".concat(legalName, \" (\").concat(aliasName, \")\");\n                type = \"alias\";\n            } else {\n                displayName = legalName;\n                type = \"company\";\n            }\n            const uniqueKey = \"\".concat(data.customeCode, \"-\").concat(data.aliasShippingNames || data.legalName, \"-\").concat(data.shippingBillingAddress);\n            const sameNameEntries = filteredData.filter((d)=>{\n                const dLegalName = d.legalName || \"Unknown Company\";\n                const dAliasName = d.aliasShippingNames && d.aliasShippingNames !== \"NONE\" ? d.aliasShippingNames : null;\n                let dDisplayName = \"\";\n                if (dAliasName) {\n                    dDisplayName = \"\".concat(dLegalName, \" (\").concat(dAliasName, \")\");\n                } else {\n                    dDisplayName = dLegalName;\n                }\n                return dDisplayName === displayName;\n            });\n            let finalDisplayName = displayName;\n            if (sameNameEntries.length > 1) {\n                if (location) {\n                    finalDisplayName = \"\".concat(displayName, \"_\").concat(location);\n                } else if (zipcode) {\n                    finalDisplayName = \"\".concat(displayName, \"_\").concat(zipcode);\n                }\n            }\n            options.push({\n                value: uniqueKey,\n                label: finalDisplayName,\n                type: type,\n                displayName: finalDisplayName\n            });\n        });\n        const uniqueOptions = options.filter((option, index, self)=>index === self.findIndex((o)=>o.value === option.value));\n        return uniqueOptions.sort((a, b)=>a.label.localeCompare(b.label));\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-gray-50 rounded-lg p-3 max-w-sm mb-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-2 mb-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MapPin_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            className: \"w-4 h-4 text-purple-600\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\LegrandDetailsComponent.tsx\",\n                            lineNumber: 708,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-sm font-semibold text-gray-900\",\n                            children: \"LEGRAND Details\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\LegrandDetailsComponent.tsx\",\n                            lineNumber: 709,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\LegrandDetailsComponent.tsx\",\n                    lineNumber: 707,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-sm text-gray-500\",\n                    children: \"Loading LEGRAND data...\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\LegrandDetailsComponent.tsx\",\n                    lineNumber: 711,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\LegrandDetailsComponent.tsx\",\n            lineNumber: 706,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-gray-50 rounded-lg p-3 mb-3 max-w-lg\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-2 mb-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MapPin_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        className: \"w-4 h-4 text-purple-600\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\LegrandDetailsComponent.tsx\",\n                        lineNumber: 719,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-sm font-semibold text-gray-900\",\n                        children: blockTitle\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\LegrandDetailsComponent.tsx\",\n                        lineNumber: 720,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\LegrandDetailsComponent.tsx\",\n                lineNumber: 718,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 gap-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"[&_div[class*='flex'][class*='items-center']]:!h-10 [&_input]:!text-sm [&_label]:!text-sm\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_SearchSelect__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            form: form,\n                            name: \"entries.\".concat(entryIndex, \".\").concat(fieldPrefix, \"Alias\"),\n                            label: \"Alias/Company\",\n                            placeholder: \"Select Alias or Company\",\n                            isEntryPage: true,\n                            className: \"text-sm\",\n                            options: getCurrentAliasOptions().map((option)=>({\n                                    value: option.value,\n                                    label: option.displayName,\n                                    badge: option.type === \"alias\" ? \"Alias\" : \"Company\",\n                                    badgeColor: option.type === \"alias\" ? \"bg-blue-500\" : \"bg-green-500\"\n                                }))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\LegrandDetailsComponent.tsx\",\n                            lineNumber: 727,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\LegrandDetailsComponent.tsx\",\n                        lineNumber: 726,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"[&_div[class*='flex'][class*='items-center']]:!h-10 [&_input]:!text-sm [&_label]:!text-sm\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_SearchSelect__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            form: form,\n                            name: \"entries.\".concat(entryIndex, \".\").concat(fieldPrefix, \"Address\"),\n                            label: \"Address\",\n                            placeholder: \"Select Address\",\n                            isEntryPage: true,\n                            className: \"text-sm\",\n                            options: getAddressOptions().map((address)=>({\n                                    value: address,\n                                    label: address\n                                }))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\LegrandDetailsComponent.tsx\",\n                            lineNumber: 745,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\LegrandDetailsComponent.tsx\",\n                        lineNumber: 744,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"[&_div[class*='flex'][class*='items-center']]:!h-10 [&_input]:!text-sm [&_label]:!text-sm\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_SearchSelect__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            form: form,\n                            name: \"entries.\".concat(entryIndex, \".\").concat(fieldPrefix, \"Zipcode\"),\n                            label: \"Zipcode\",\n                            placeholder: \"Select Zipcode\",\n                            isEntryPage: true,\n                            className: \"text-sm\",\n                            options: getZipcodeOptions().map((zipcode)=>({\n                                    value: zipcode,\n                                    label: zipcode\n                                }))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\LegrandDetailsComponent.tsx\",\n                            lineNumber: 761,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\LegrandDetailsComponent.tsx\",\n                        lineNumber: 760,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\LegrandDetailsComponent.tsx\",\n                lineNumber: 724,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\LegrandDetailsComponent.tsx\",\n        lineNumber: 717,\n        columnNumber: 5\n    }, undefined);\n};\n_s(LegrandDetailsComponent, \"m5Wdt3TsnTEmkx/wqOJSKnbhTho=\");\n_c = LegrandDetailsComponent;\n/* harmony default export */ __webpack_exports__[\"default\"] = (LegrandDetailsComponent);\nvar _c;\n$RefreshReg$(_c, \"LegrandDetailsComponent\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/user/trackSheets/LegrandDetailsComponent.tsx\n"));

/***/ })

});